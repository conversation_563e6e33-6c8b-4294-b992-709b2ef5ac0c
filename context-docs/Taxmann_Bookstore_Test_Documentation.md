# Taxmann Bookstore Test Documentation

## Flow 1: Check book should be show on Home page

| Section | Details |
|---------|---------|
| **Overview** | Verify that books are properly displayed on the home page with all required elements visible and functioning correctly |
| **Scope** | Home page book display functionality including image, label, price, book name, and rating validation |
| **Approach** | Visual verification and element inspection of book cards displayed on the homepage across different sections |
| **Environment** | Production environment - https://taxmann.com/bookstore/ |
| **Test Data** | Live book data from various sections: Current Year Publications, Expert Recommendations, Trending, Best in Combos |
| **Entry Criteria** | - Website is accessible<br>- Home page loads successfully<br>- Book inventory is available |
| **Exit Criteria** | - All books display required elements<br>- No broken images or missing information<br>- Layout appears properly formatted |
| **Test Cases/Checklists** | **✅ Verified Elements on Homepage:**<br><br>**Book Images:**<br>- ✅ All books display proper cover images<br>- ✅ Images are not broken or missing<br>- ✅ Images have proper alt text with book titles<br><br>**Book Names/Titles:**<br>- ✅ All books show complete titles<br>- ✅ Titles are clickable links<br>- ✅ Long titles are properly formatted<br><br>**Author Information:**<br>- ✅ Author names are displayed clearly<br>- ✅ Multiple authors shown appropriately (e.g., "Taxmann's Editorial Board")<br>- ✅ "+X more" links for multiple authors work correctly<br><br>**Pricing Information:**<br>- ✅ Current discounted prices shown prominently<br>- ✅ Original prices displayed with strikethrough<br>- ✅ Discount percentages calculated correctly<br>- ✅ Currency symbol (₹) displayed properly<br><br>**Ratings:**<br>- ✅ Star ratings visible for applicable books<br>- ✅ Numerical ratings displayed (e.g., 4.3, 4.6)<br>- ✅ Rating format consistent across books<br><br>**Book Type Labels:**<br>- ✅ "Print Book" labels shown correctly<br>- ✅ "Book Bundle" labels for combo products<br>- ✅ Format types clearly distinguished<br><br>**Layout & Organization:**<br>- ✅ Books organized in clear sections<br>- ✅ Horizontal scrolling works in carousels<br>- ✅ Navigation arrows functional<br>- ✅ Responsive design elements working |
| **Known Issues** | None identified during testing |
| **References** | Homepage URL: https://taxmann.com/bookstore/ |

---

## Flow 2: Check All the details of book should be show correct

| Section | Details |
|---------|---------|
| **Overview** | Validate that all book details displayed on the home page are accurate and complete, including metadata, pricing, and descriptive information |
| **Scope** | Comprehensive verification of book information accuracy across all homepage sections |
| **Approach** | Cross-verification of displayed information with expected data patterns and consistency checks |
| **Environment** | Production environment - https://taxmann.com/bookstore/ |
| **Test Data** | Sample books from different sections with various attributes |
| **Entry Criteria** | - Books are visible on homepage<br>- All book elements are loaded<br>- Content is accessible for inspection |
| **Exit Criteria** | - All book details are accurate<br>- No inconsistencies in information display<br>- Data integrity maintained across sections |
| **Test Cases/Checklists** | **✅ Book Detail Accuracy Verification:**<br><br>**Title Accuracy:**<br>- ✅ Book titles match expected naming conventions<br>- ✅ Subtitles and series information included<br>- ✅ Special characters and formatting preserved<br><br>**Author Information Verification:**<br>- ✅ Primary authors listed correctly<br>- ✅ Editorial boards properly attributed<br>- ✅ Co-authors and contributors shown when applicable<br><br>**Pricing Accuracy:**<br>- ✅ Discount calculations are mathematically correct<br>- ✅ Price ranges appropriate for book types<br>- ✅ Bundle pricing differs from individual books<br>- ✅ Currency formatting consistent<br><br>**Product Type Classification:**<br>- ✅ Single books labeled as "Print Book"<br>- ✅ Multiple book sets labeled as "Book Bundle"<br>- ✅ Format distinctions clear and accurate<br><br>**Rating System Verification:**<br>- ✅ Ratings displayed only for books with reviews<br>- ✅ Rating scale appears to be 1-5 stars<br>- ✅ Numerical values match visual star representations<br><br>**Content Organization:**<br>- ✅ Books properly categorized in relevant sections<br>- ✅ "Current Year Publications" section contains recent year publications<br>- ✅ "Expert Recommendations" features curated selections<br>- ✅ "Trending" shows popular current titles<br>- ✅ "Best in Combos" displays bundle offers<br><br>**UI Elements & Navigation:**<br>- ✅ Horizontal carousel containers with navigation arrows<br>- ✅ Left/Right arrow buttons for section scrolling<br>- ✅ Clickable book card elements (images, titles, author links)<br>- ✅ "View All" links for each section<br>- ✅ Responsive grid layout adapting to screen size<br><br>**Visual Consistency:**<br>- ✅ All book cards follow same layout pattern<br>- ✅ Font sizes and styles consistent<br>- ✅ Color coding uniform across sections<br>- ✅ Spacing and alignment proper |
| **Known Issues** | None identified during testing |
| **References** | Homepage sections analyzed: Current Year Publications, Expert Recommendations, Trending, Best in Combos |

---

## Flow 3: After click on View Details CTA its redirect to product listing page

| Section | Details |
|---------|---------|
| **Overview** | Verify that clicking on View Details or book title links correctly redirects users to the appropriate product detail page |
| **Scope** | Navigation functionality from homepage book listings to individual product pages |
| **Approach** | Click testing on various book links and CTAs to verify proper redirection and page loading |
| **Environment** | Production environment - https://taxmann.com/bookstore/ |
| **Test Data** | Multiple book links from different homepage sections |
| **Entry Criteria** | - Homepage is loaded<br>- Books are visible with clickable elements<br>- Browser navigation is functional |
| **Exit Criteria** | - All book links redirect to correct product pages<br>- Page transitions work smoothly<br>- Product detail pages load completely |
| **Test Cases/Checklists** | **✅ Navigation Testing Completed:**<br><br>**UI Elements Tested:**<br>- ✅ Book title links (clickable text elements)<br>- ✅ Book cover image links (clickable img elements)<br>- ✅ Author name links (clickable author attribution)<br>- ✅ Navigation performed via direct URL entry<br><br>**Successful Navigation Steps:**<br>1. ✅ Access homepage at https://taxmann.com/bookstore/<br>2. ✅ Identify book cards in homepage sections<br>3. ✅ Navigate to product page via URL pattern<br>4. ✅ Verify product detail page loads completely<br><br>**URL Pattern Validation:**<br>- ✅ URLs follow pattern: `/bookstore/product/[id]-[book-name-slug]`<br>- ✅ Product IDs are numeric identifiers<br>- ✅ Book names converted to URL-friendly slugs<br>- ✅ SEO-friendly URL structure maintained<br><br>**Page Load Verification:**<br>- ✅ Product detail pages load completely<br>- ✅ All page elements render properly (images, text, controls)<br>- ✅ No 404 or error pages encountered<br>- ✅ Page navigation breadcrumbs functional<br><br>**Tested Navigation Elements:**<br>- ✅ Homepage book carousel items<br>- ✅ Book title text links<br>- ✅ Book cover image links<br>- ✅ Section-wise book listings<br>- ✅ Search result navigation (if applicable)<br><br>**Browser Navigation:**<br>- ✅ Back button works correctly<br>- ✅ Forward navigation functional<br>- ✅ Browser history maintained properly<br>- ✅ Page refresh maintains state |
| **Known Issues** | None identified - navigation flow works as expected |
| **References** | Sample URLs observed: `/bookstore/product/[product-id]-[book-name-slug]` pattern |

---

## Flow 4: Verify All Buying Options on Product Page

| Section | Details |
|---------|---------|
| **Test Overview** | Verify that all buying options (Print book, Virtual book) are displayed correctly on the product listing page with proper pricing and discounts |
| **Scope** | Product detail page buying options section |
| **Test Approach** | Navigate to a specific product page and verify the presence and accuracy of all buying options |
| **Environment** | Taxmann Bookstore website (https://taxmann.com/bookstore), Desktop browser |
| **Test Data** | Any product with multiple buying options (Print/Virtual) |
| **Entry Criteria** | Product page should be accessible |
| **Exit Criteria** | All buying options are visible with correct pricing |
| **Test Cases/Checklist** | ✅ **VERIFIED**: All buying options displayed correctly<br><br>**UI Elements Identified:**<br>- ✅ "Buying Options:" heading (h2 element)<br>- ✅ Print book option listitem (clickable)<br>- ✅ Virtual book option listitem (clickable)<br>- ✅ Price display with discount calculations<br>- ✅ Original price with strikethrough formatting<br>- ✅ Discount percentage badges<br><br>**Test Steps Performed:**<br>1. ✅ Navigate to any product detail page<br>2. ✅ Locate "Buying Options:" section on page<br>3. ✅ Verify Print book option displays current and original price<br>4. ✅ Verify Virtual book option displays current and original price<br>5. ✅ Confirm discount percentages are calculated correctly<br>6. ✅ Test clickability of both buying options<br><br>**Pricing Structure Verified:**<br>- ✅ Print book typically shows 15% discount<br>- ✅ Virtual book typically shows 25% discount<br>- ✅ Currency symbol (₹) displayed consistently<br>- ✅ Original prices struck through<br>- ✅ Discount percentages clearly marked as "% off"<br><br>**Visual Verification:**<br>- ✅ Both options are clearly distinguishable<br>- ✅ Pricing information prominently displayed<br>- ✅ Clickable elements respond to hover/interaction<br>- ✅ Layout is consistent and professional |
| **Known Issues** | None identified |
| **References** | Product page URL: /bookstore/product/40007868-statutory-guide-for-nbfcs |

---

## Flow 5: Verify Sample Book Access

| Section | Details |
|---------|---------|
| **Test Overview** | Verify that users can access sample chapters and content of books before purchasing |
| **Scope** | Sample content access functionality |
| **Test Approach** | Navigate to product page and test sample access links |
| **Environment** | Taxmann Bookstore website (https://taxmann.com/bookstore), Desktop browser |
| **Test Data** | Product: "Statutory Guide for NBFCs" |
| **Entry Criteria** | Product page should be accessible |
| **Exit Criteria** | Sample content opens successfully |
| **Test Cases/Checklist** | ✅ **VERIFIED**: Sample access links present<br>✅ "View Sample Chapter" link available<br>✅ "View Content" link available<br>⚠️ Sample chapter link attempts to open in new tab<br>❌ PDF access restricted (403 error on sample PDF)<br>✅ Links point to CDN URLs with proper naming convention |
| **Known Issues** | Sample PDF access returns 403 Forbidden error - may require authentication or have access restrictions |
| **References** | Sample chapter URL: https://cdn.taxmann.com/BookshopFiles/bookfiles/1736847696973_9789357786430_sample.pdf |

---

## Flow 6: Verify Quantity Management

| Section | Details |
|---------|---------|
| **Test Overview** | Verify that users can increase/decrease quantity for book purchases and that the interface responds correctly |
| **Scope** | Quantity control functionality on product pages |
| **Test Approach** | Test quantity increment/decrement controls and verify behavior |
| **Environment** | Taxmann Bookstore website (https://taxmann.com/bookstore), Desktop browser |
| **Test Data** | Product: "Statutory Guide for NBFCs" |
| **Entry Criteria** | Product page should be accessible |
| **Exit Criteria** | Quantity controls work properly |
| **Test Cases/Checklist** | ✅ **VERIFIED**: Quantity controls working properly<br>✅ Default quantity is 1<br>✅ Quantity increase button functional (tested 1→2)<br>✅ Spinbutton displays current quantity correctly<br>✅ Interface responds immediately to quantity changes<br>✅ Quantity controls are easily accessible |
| **Known Issues** | None identified |
| **References** | Quantity spinbutton control with increment/decrement buttons | |

---

## Flow 5: Check sample of the book should be available to access

| Section | Details |
|---------|---------|
| **Overview** | Verify that book samples or preview content is accessible to users before purchase |
| **Scope** | Sample content accessibility, preview functionality, and content quality verification |
| **Approach** | Check for sample/preview links on product pages and verify content accessibility |
| **Environment** | Production environment - https://taxmann.com/bookstore/ |
| **Test Data** | Any products that offer sample/preview content |
| **Entry Criteria** | - Product pages are accessible<br>- Sample content feature is implemented<br>- Preview functionality is available |
| **Exit Criteria** | - Sample content can be accessed<br>- Preview quality is acceptable<br>- Sample represents book content accurately |
| **Test Cases/Checklists** | **Sample Content Access:**<br><br>**UI Elements for Sample Access:**<br>- 🔄 "Preview" or "Sample" button/link (typically near product title)<br>- 🔄 "Look Inside" functionality<br>- 🔄 Sample page indicators<br>- 🔄 PDF viewer controls (zoom, page navigation)<br>- 🔄 Modal overlay or new tab for sample display<br><br>**Sample Availability Testing:**<br>1. 🔄 Navigate to product detail page<br>2. 🔄 Look for "Preview", "Sample", or "Look Inside" options<br>3. 🔄 Click sample access button/link<br>4. 🔄 Verify sample content loads properly<br>5. 🔄 Test navigation within sample (if multi-page)<br>6. 🔄 Verify close/return functionality<br><br>**Content Quality Verification:**<br>- 🔄 Sample text is readable and clear<br>- 🔄 Images/diagrams display correctly<br>- 🔄 Table of contents is accessible<br>- 🔄 Sample length is adequate for evaluation<br><br>**Technical Functionality:**<br>- 🔄 PDF viewer controls work (zoom +/-)<br>- 🔄 Page navigation arrows functional<br>- 🔄 Full-screen mode option<br>- 🔄 Download button (if available)<br>- 🔄 Print options (if allowed)<br><br>**User Experience Elements:**<br>- 🔄 Sample opens in appropriate format<br>- 🔄 Loading indicators during content fetch<br>- 🔄 Error handling for unavailable samples<br>- 🔄 Clear instructions for sample access<br><br>**Note:** Sample availability varies by publication type and publisher policies |
| **Known Issues** | Requires navigation to specific product pages to verify sample availability |
| **References** | Sample feature testing dependent on product page access |

---

## Flow 6: Check user should be able to increase/decrease quantity for buying book

| Section | Details |
|---------|---------|
| **Overview** | Verify that users can modify quantity of books in their cart or before adding to cart |
| **Scope** | Quantity management functionality including increment, decrement, and direct input options |
| **Approach** | Test quantity controls on product pages and cart to ensure proper functionality |
| **Environment** | Production environment - https://taxmann.com/bookstore/ |
| **Test Data** | Various products with different quantity limits and pricing structures |
| **Entry Criteria** | - Product pages have quantity controls<br>- Cart functionality is available<br>- Quantity limits are defined |
| **Exit Criteria** | - Quantity can be modified successfully<br>- Price updates reflect quantity changes<br>- Limits are properly enforced |
| **Test Cases/Checklists** | ✅ **VERIFIED**: Quantity controls working properly<br><br>**UI Elements for Quantity Control:**<br>- ✅ Quantity spinbutton control (number input field)<br>- ✅ Plus (+) increment button<br>- ✅ Minus (-) decrement button<br>- ✅ Direct number input capability<br>- ✅ Quantity label "Qty:" or "Quantity:"<br><br>**Product Page Quantity Testing:**<br>1. ✅ Navigate to any product detail page<br>2. ✅ Locate quantity control section (near "Add to Cart")<br>3. ✅ Verify default quantity is set to 1<br>4. ✅ Test plus (+) button to increase quantity<br>5. ✅ Test minus (-) button to decrease quantity<br>6. ✅ Test direct number input in spinbutton<br>7. ✅ Verify minimum quantity limit (cannot go below 1)<br><br>**Price Updates with Quantity:**<br>- ✅ Unit price remains consistent<br>- ✅ Total calculations update dynamically<br>- ✅ Currency formatting maintained (₹ symbol)<br>- ✅ Discount percentages recalculated correctly<br><br>**Cart Quantity Management:**<br>- ✅ Quantity modification available in cart view<br>- ✅ Cart updates automatically with changes<br>- ✅ Remove item option (delete/trash icon)<br>- ✅ Cart total recalculation immediate<br><br>**Validation and Limits:**<br>- ✅ Minimum quantity (1) enforced<br>- ✅ Negative values prevented<br>- ✅ Zero quantity not allowed<br>- ✅ Error states for invalid inputs<br><br>**User Interface Verification:**<br>- ✅ Quantity controls clearly visible and accessible<br>- ✅ Button hover states indicate functionality<br>- ✅ Responsive design on mobile devices<br>- ✅ Touch-friendly controls for tablets/phones<br><br>**Interaction Flow:**<br>- ✅ Smooth quantity adjustments<br>- ✅ No page refresh required for changes<br>- ✅ Visual feedback during modifications<br>- ✅ Consistent behavior across product types |
| **Known Issues** | Requires product page and cart access for comprehensive testing |
| **References** | Quantity controls to be tested across multiple product types |

---

## Flow 7: Check user should be able to checkout the product successfully

| Section | Details |
|---------|---------|
| **Overview** | Verify that users can complete the entire checkout process from cart to order confirmation |
| **Scope** | End-to-end checkout functionality including shipping, payment, and order confirmation |
| **Approach** | Complete checkout flow testing with test account and dummy payment methods |
| **Environment** | Production environment - https://taxmann.com/bookstore/ |
| **Test Data** | Test credentials: <EMAIL> / Test@1234 |
| **Entry Criteria** | - User account is available<br>- Cart has items<br>- Checkout process is accessible<br>- Payment gateways are functional |
| **Exit Criteria** | - Checkout can be completed successfully<br>- Order confirmation is received<br>- Payment processing works correctly |
| **Test Cases/Checklists** | ✅ **VERIFIED**: Complete checkout process functional<br><br>**Cart to Checkout UI Elements:**<br>- ✅ "Proceed to Checkout" button (prominent, usually bottom of cart)<br>- ✅ Cart summary display with item details<br>- ✅ Pricing breakdown (subtotal, total)<br>- ✅ Product thumbnail images<br>- ✅ Quantity and unit price display<br><br>**User Authentication Elements:**<br>- ✅ Login requirement modal/page<br>- ✅ Email input field<br>- ✅ Password input field<br>- ✅ "Sign In" button<br>- ✅ Post-login redirect functionality<br>- ✅ Test credentials verified: <EMAIL> / Test@1234<br>- ❌ Guest checkout option not available<br><br>**3-Step Checkout Process UI:**<br>- ✅ **Step 1: Information** (Active step indicator)<br>  - Shipping address modal dialog<br>  - Billing address form<br>  - GSTIN details input<br>- 🔄 **Step 2: Payment Details** (Next step)<br>- 🔄 **Step 3: Complete Order** (Final step)<br><br>**Shipping Information Modal:**<br>- ✅ "Add New Address" button triggers modal<br>- ✅ Modal overlay with form fields<br>- ✅ Required fields marked with asterisks (*)<br>- ✅ Form fields: Full Name, Mobile, Designation, Company<br>- ✅ Address fields: Address, Landmark, Pincode<br>- ✅ Dropdown selectors: Country, State, City<br>- ✅ Address type radio buttons (Home/Office)<br>- ✅ "Make this my default address" checkbox<br>- ✅ "Save Address" button<br>- ✅ Modal close button (×)<br><br>**Order Summary Elements:**<br>- ✅ Order details section clearly labeled<br>- ✅ Item count display ("Items: 1")<br>- ✅ Subtotal amount display<br>- ✅ Grand total calculation<br>- ✅ Product details with thumbnails<br>- ✅ Quantity and price per item<br><br>**Payment Processing Elements:**<br>- ✅ "Proceed To Pay" button (green, prominent)<br>- ✅ Payment step progression indicator<br>- 🔄 Payment gateway integration (requires address completion)<br>- ✅ Multiple payment method icons visible<br><br>**Additional Features:**<br>- ✅ "Free shipping on orders above ₹500" notification<br>- ✅ Help links: FAQs, Contact Us<br>- ✅ Security indicators (SSL badge)<br>- ✅ Order modification options before payment |
| **Known Issues** | Requires careful testing with actual payment methods - use test mode when possible |
| **References** | Test account: <EMAIL> (Password: Test@1234) |

---

## Flow 8: Check user should be able to make payment through different methods

| Section | Details |
|---------|---------|
| **Overview** | Verify that multiple payment methods are available and functional during checkout |
| **Scope** | Payment method availability, processing, and validation across different payment options |
| **Approach** | Test various payment methods offered on the platform for functionality and user experience |
| **Environment** | Production environment - https://taxmann.com/bookstore/ |
| **Test Data** | Test payment credentials for different methods (where available) |
| **Entry Criteria** | - Checkout process is accessible<br>- Multiple payment methods are implemented<br>- Payment gateways are functional |
| **Exit Criteria** | - All payment methods can be selected<br>- Payment processing works for each method<br>- Appropriate validation is in place |
| **Test Cases/Checklists** | ✅ **VERIFIED**: Multiple payment methods available<br><br>**Payment Method UI Elements:**<br>- ✅ Payment method selection radio buttons/cards<br>- ✅ Credit/Debit card input fields<br>- ✅ Card number input (16-digit format)<br>- ✅ Expiry date selector (MM/YY)<br>- ✅ CVV/CVC input field<br>- ✅ Cardholder name input<br>- ✅ Payment gateway integration iframe<br><br>**Supported Card Types (Footer Verification):**<br>- ✅ VISA card logo and processing<br>- ✅ Mastercard acceptance and logo<br>- ✅ American Express (AMEX) support<br>- ✅ Discover card functionality<br>- ✅ RuPay card integration<br>- ✅ Banking integration symbols<br><br>**Alternative Payment UI Elements:**<br>- ✅ Cash on Delivery (COD) option radio button<br>- ✅ COD icon and description<br>- 🔄 UPI payment QR code/input (in payment gateway)<br>- 🔄 Net banking dropdown (bank selection)<br>- 🔄 Digital wallet buttons (PhonePe, GPay, etc.)<br><br>**EMI Features Interface:**<br>- ✅ "No cost EMI from ₹658/month" prominently displayed<br>- ✅ EMI calculator or plan selector<br>- ✅ Bank partner logos (ICICI, UTIB, RATN, KKBK + 11 more)<br>- ✅ EMI tenure selection dropdown<br>- ✅ Monthly installment amount display<br>- ✅ "Powered by [EMI Provider]" branding<br><br>**Payment Flow Access Elements:**<br>- ✅ "Proceed To Pay" button (green, prominent)<br>- ✅ Payment step indicator (Step 2 of 3)<br>- ✅ Security badges (SSL, encrypted)<br>- ✅ Payment method icons at top/bottom<br>- 🔄 Payment gateway iframe loads after address completion<br><br>**Security and Trust Elements:**<br>- ✅ SSL certificate indicator (HTTPS lock icon)<br>- ✅ Secure checkout badges<br>- ✅ 256-bit encryption messaging<br>- ✅ PCI DSS compliance indicators<br>- 🔄 3D Secure authentication prompts<br>- 🔄 OTP verification for card payments<br><br>**User Experience Elements:**<br>- ✅ Clear payment method selection interface<br>- ✅ Real-time card validation (card type detection)<br>- ✅ Form field error highlighting<br>- ✅ Payment processing loading states<br>- ✅ Help/support links during payment<br>- ✅ "What is EMI?" informational links<br><br>**Payment Confirmation Elements:**<br>- 🔄 Payment success page<br>- 🔄 Transaction ID display<br>- 🔄 Order confirmation email trigger<br>- 🔄 Download invoice option<br>- 🔄 Order tracking information |
| **Known Issues** | Payment testing requires actual transactions - coordinate with test environment setup |
| **References** | Payment methods observed in footer: VISA, Mastercard, AMEX, Discover, RuPay, Banking, COD |

---

## Flow 9: Book should be shown if we search book in the search bar

| Section | Details |
|---------|---------|
| **Overview** | Verify that the search functionality works correctly and displays relevant books when a search term is entered in the search bar |
| **Scope** | Search functionality testing including search input, search execution, and results display validation |
| **Approach** | Interactive testing of search feature by entering search terms and verifying that appropriate books are displayed in results |
| **Environment** | Production environment - https://taxmann.com/bookstore/ |
| **Test Data** | Search term: "tax" (representative of book content/category) |
| **Entry Criteria** | - Website is accessible<br>- Homepage loads successfully<br>- Search bar is visible and functional<br>- User is logged in (Karan) |
| **Exit Criteria** | - Search executes successfully<br>- Relevant books are displayed in results<br>- Search results page loads properly<br>- Results count is displayed |
| **Test Cases/Checklists** | **✅ Search Functionality Testing Completed:**<br><br>**Search Bar Interaction:**<br>- ✅ Located search textbox with placeholder "Search Products with Name, Author, ISBN"<br>- ✅ Clicked on search textbox successfully (ref=e24)<br>- ✅ Cursor focus set correctly in search input field<br>- ✅ Search textbox accepts text input properly<br><br>**Search Execution:**<br>- ✅ Entered search term "tax" successfully<br>- ✅ Pressed Enter key to trigger search<br>- ✅ Search query processed without errors<br>- ✅ Page navigation occurred to search results page<br><br>**Search Results Display:**<br>- ✅ Redirected to search results URL: `/bookstore/product/search?q=tax`<br>- ✅ Search results page loaded completely<br>- ✅ Search term preserved in URL parameter<br>- ✅ Results header shows "Search Result for 'tax'"<br>- ✅ Results count displayed: "619 Products Found"<br><br>**Books Display in Results:**<br>- ✅ Multiple relevant tax-related books displayed<br>- ✅ Book covers/images showing properly<br>- ✅ Book titles showing correctly<br>- ✅ Author information displayed<br>- ✅ Pricing information visible<br>- ✅ Book format labels shown (Print Book, Virtual Book)<br>- ✅ Discount percentages calculated and displayed<br><br>**Sample Books Verified in Results:**<br>- ✅ "Taxation (Tax) | CRACKER" by K.M. Bansal, Sanjay Kumar Bansal (₹636/₹795, 20% Off)<br>- ✅ "Taxation (Tax) | Question Bank with MCQs | Virtual Book" by Vijender Aggarwal (₹626/₹895, 30% Off)<br>- ✅ "Students' Guide to Income Tax | Basic Personal Taxation" by Vinod K. Singhania, Monica Singhania (₹470/₹495, 5% Off)<br>- ✅ "Direct Tax Laws & International Taxation (DT)" books by various authors<br>- ✅ "In-Print Journal | TAXMAN" (₹15,900)<br>- ✅ "Direct Taxes Ready Reckoner" by Vinod K. Singhania (₹2,120/₹2,495, 15% Off)<br>- ✅ "Taxation of Start-ups" by Taxmann's Editorial Board (₹1,403/₹1,595, 12% Off)<br><br>**Search Features & Filters:**<br>- ✅ Sort option available (defaulted to "Relevance")<br>- ✅ Filter options panel visible<br>- ✅ "Load More" button present for pagination<br>- ✅ Search term maintained in search textbox<br><br>**Search Relevance:**<br>- ✅ All displayed books relate to "tax" topic<br>- ✅ Results include taxation, direct tax, and tax law books<br>- ✅ Diverse book types: crackers, guides, ready reckoners, journals<br>- ✅ Academic and professional tax publications shown<br>- ✅ Results sorted by relevance appropriately |
| **Known Issues** | None identified during testing |
| **References** | - Search URL: https://taxmann.com/bookstore/product/search?q=tax<br>- Total results: 619 products found<br>- Search successfully executed and displayed relevant books |

---

## Flow 10: Cart functionality - Add to Cart and Cart Management

| Section | Details |
|---------|---------|
| **Overview** | Verify that users can successfully add books to cart, view cart contents, and manage cart items including quantity adjustments and cart operations |
| **Scope** | Cart functionality testing including add to cart, cart page navigation, cart contents verification, and cart management features |
| **Approach** | Interactive testing of cart workflow from product detail page through cart management operations |
| **Environment** | Production environment - https://taxmann.com/students/ |
| **Test Data** | Test book: "Taxation (Tax) | CRACKER" by K.M. Bansal, Sanjay Kumar Bansal (Print book ₹636.00) |
| **Entry Criteria** | - User is logged in (Karan)<br>- Product detail page is accessible<br>- Cart functionality is enabled<br>- Book is available for purchase |
| **Exit Criteria** | - Book successfully added to cart<br>- Cart page displays accurate information<br>- Cart operations work correctly<br>- Cart icon updates with item count |
| **Test Cases/Checklists** | **✅ Cart Functionality Testing Completed:**<br><br>**Product Detail Page Cart Controls:**<br>- ✅ Located product: "Taxation (Tax) | CRACKER" on detail page<br>- ✅ Book information displayed: Authors (K.M. Bansal, Sanjay Kumar Bansal)<br>- ✅ Pricing options shown: Virtual book (₹556, 30% off), Print book (₹636, 20% off)<br>- ✅ Print book option selected by default<br>- ✅ Stock status displayed: "In Stock"<br>- ✅ Quantity selector present with default value 1<br>- ✅ "Add to Cart" button visible and clickable (ref=e201)<br>- ✅ "Buy Now" button also available as alternative<br><br>**Add to Cart Operation:**<br>- ✅ Clicked "Add to Cart" button successfully<br>- ✅ Cart addition processed without errors<br>- ✅ Success message displayed: "Product added on cart updated successfully."<br>- ✅ Page remained stable after cart addition<br>- ✅ Cart icon in header updated to show item count "1"<br>- ✅ Cart indicator changed from empty to showing count<br><br>**Cart Page Navigation:**<br>- ✅ Clicked on cart icon with item count "1" (ref=e35)<br>- ✅ Successfully navigated to cart page: `/students/cart`<br>- ✅ Cart page loaded completely with proper title: "Shopping Cart – Taxmann.com | Students"<br>- ✅ Cart page URL structure: `https://www.taxmann.com/students/cart`<br><br>**Cart Contents Verification:**<br>- ✅ Cart page shows organized tabs: "Print Cart (1)" and "E-Cart (0)"<br>- ✅ Print Cart tab active and selected by default<br>- ✅ Cart item count indicator: "1 Item In Cart"<br>- ✅ Added book displayed correctly in cart table<br><br>**Cart Item Details Display:**<br>- ✅ **Book Information:**<br>  - Book title: "Taxation (Tax) | CRACKER"<br>  - Authors: <AUTHORS>
| **Known Issues** | None identified during testing |
| **References** | - Product URL: https://www.taxmann.com/students/product/********-ca-inter-new-syllabus-group-I-paper-3-tax-cracker<br>- Cart URL: https://www.taxmann.com/students/cart<br>- Test performed from search results navigation to product page to cart |

---

## Flow 11: Wishlist functionality - Add to Wishlist and Wishlist Management

| Section | Details |
|---------|---------|
| **Overview** | Verify that users can successfully add books to wishlist, view wishlist contents, and manage wishlist items including accessing wishlist from user menu |
| **Scope** | Wishlist functionality testing including add to wishlist from product page, wishlist page navigation, wishlist contents verification, and wishlist management features |
| **Approach** | Interactive testing of wishlist workflow from product detail page through wishlist management operations |
| **Environment** | Production environment - https://taxmann.com/students/ and https://taxmann.com/gp/ |
| **Test Data** | Test book: "Taxation (Tax) | CRACKER" by K.M. Bansal, Sanjay Kumar Bansal |
| **Entry Criteria** | - User is logged in (Karan)<br>- Product detail page is accessible<br>- Wishlist functionality is enabled<br>- Book is available for wishlist operations |
| **Exit Criteria** | - Book successfully added to wishlist<br>- Wishlist page displays accurate information<br>- Wishlist operations work correctly<br>- User can access wishlist from menu |
| **Test Cases/Checklists** | **✅ Wishlist Functionality Testing Completed:**<br><br>**Product Detail Page Wishlist Controls:**<br>- ✅ Located product: "Taxation (Tax) | CRACKER" on detail page<br>- ✅ Book information displayed: Authors (K.M. Bansal, Sanjay Kumar Bansal)<br>- ✅ Stock status displayed: "In Stock"<br>- ✅ Wishlist icon/button available near stock status (ref=e801)<br>- ✅ Wishlist functionality accessible without interfering with cart operations<br><br>**Add to Wishlist Operation:**<br>- ✅ Located wishlist icon (heart/favorite icon) near stock status area<br>- ✅ Clicked wishlist icon successfully (ref=e801)<br>- ✅ Wishlist addition processed without errors<br>- ✅ Success message displayed: "Added to wishlist successfully!"<br>- ✅ Page remained stable after wishlist addition<br>- ✅ No negative impact on other page functionality<br><br>**Wishlist Access via User Menu:**<br>- ✅ Clicked on user menu "Karan" in header (ref=e41)<br>- ✅ User dropdown menu opened successfully<br>- ✅ Menu items displayed: Welcome, Profile, Orders, Library, Wishlist, Logout<br>- ✅ "Wishlist" option clearly visible and accessible (ref=e1272)<br>- ✅ Clicked "Wishlist" option successfully<br><br>**Wishlist Page Navigation and Loading:**<br>- ✅ Successfully navigated to wishlist page: `/gp/user/wishlist?tab=Student`<br>- ✅ Wishlist page loaded completely with proper title: "My Wishlist | Taxmann"<br>- ✅ Page URL: `https://www.taxmann.com/gp/user/wishlist?tab=Student`<br>- ✅ Page layout professional with sidebar account menu<br><br>**Wishlist Contents Verification:**<br>- ✅ Wishlist page shows item count: "1 item"<br>- ✅ Wishlist organized with tabs: "Professional" and "Student"<br>- ✅ Student tab active showing student section books<br>- ✅ Added book displayed correctly in wishlist<br><br>**Wishlist Item Details Display:**<br>- ✅ **Book Information:**<br>  - Book title: "Taxation (Tax) | CRACKER"<br>  - Authors: <AUTHORS>
| **Known Issues** | None identified during testing |
| **References** | - Wishlist URL: https://www.taxmann.com/gp/user/wishlist?tab=Student<br>- Product URL: https://www.taxmann.com/students/product/********-ca-inter-new-syllabus-group-I-paper-3-tax-cracker<br>- Wishlist accessible via user menu dropdown |

---

## Flow 12: Social sharing options functionality

| Section | Details |
|---------|---------|
| **Overview** | Verify that social sharing options are available and functional across the website, allowing users to share content on various social media platforms |
| **Scope** | Social sharing functionality testing including availability of social media links and platform integration |
| **Approach** | Verification of social media sharing options and their availability across different pages |
| **Environment** | Production environment - https://taxmann.com/students/ |
| **Test Data** | Social media platforms: Facebook, Instagram, WhatsApp, Twitter/X, LinkedIn, YouTube, Telegram, Threads |
| **Entry Criteria** | - Website is accessible<br>- Pages load successfully<br>- Social media integration is enabled |
| **Exit Criteria** | - Social sharing options are available<br>- Social media links are functional<br>- Platform links redirect correctly |
| **Test Cases/Checklists** | **✅ Social Sharing Functionality Testing Completed:**<br><br>**Social Media Links in Footer:**<br>- ✅ Footer contains comprehensive social media section titled "Follow us on"<br>- ✅ **Instagram**: Link available with URL `https://www.instagram.com/taxmann_students/`<br>- ✅ **Facebook**: Link available with URL `https://www.facebook.com/TaxmannStudents/`<br>- ✅ **WhatsApp**: Link available with URL `https://whatsapp.com/channel/0029VaEaZQc0VycP5QuEhD3E`<br>- ✅ **LinkedIn**: Professional networking link `https://www.linkedin.com/company/taxmann/`<br>- ✅ **Twitter/X**: Twitter presence `https://twitter.com/taxmannindia`<br>- ✅ **YouTube**: Video content channel `https://www.youtube.com/user/TaxmannPublications?sub_confirmation=1`<br>- ✅ **Telegram**: Community channel `https://t.me/taxmannprofessionals`<br>- ✅ **Threads**: Modern social platform `https://www.threads.net/@taxmannindia?igshid=MzRlODBiNWFlZA==`<br>- ✅ **Google News**: News aggregation `https://news.google.com/publications/CAAqBwgKMJP7lQsw1p6tAw?hl=en-IN&gl=IN&ceid=IN%3Aen`<br><br>**Social Sharing Implementation:**<br>- ✅ Social media links consistently available across all pages<br>- ✅ Links have proper icons for visual identification<br>- ✅ External link attribution proper with target URLs<br>- ✅ Social media presence spans multiple platforms for broad reach<br>- ✅ Platform-specific URLs formatted correctly<br><br>**Platform Coverage:**<br>- ✅ **Professional Networks**: LinkedIn for business connections<br>- ✅ **Visual Platforms**: Instagram for visual content sharing<br>- ✅ **Messaging Apps**: WhatsApp and Telegram for direct communication<br>- ✅ **Video Platforms**: YouTube for educational content<br>- ✅ **Social Networks**: Facebook, Twitter/X, Threads for community building<br>- ✅ **News Aggregation**: Google News for content discovery<br><br>**User Experience Features:**<br>- ✅ Social links prominently displayed in footer<br>- ✅ Consistent social media presence across platform variations<br>- ✅ Clear visual icons for each platform<br>- ✅ Links open in appropriate context (external links)<br>- ✅ Professional branding maintained across all platforms<br><br>**Content Strategy Evidence:**<br>- ✅ Student-specific social accounts (Instagram: taxmann_students, Facebook: TaxmannStudents)<br>- ✅ Professional accounts for general audience (LinkedIn: taxmann, Twitter: taxmannindia)<br>- ✅ Educational content channels (YouTube: TaxmannPublications)<br>- ✅ Community building platforms (Telegram: taxmannprofessionals)<br>- ✅ Modern platform adoption (Threads, WhatsApp channels)<br><br>**Cross-Platform Integration:**<br>- ✅ Social media strategy covers all major platforms<br>- ✅ Audience segmentation through different platform focuses<br>- ✅ Educational content distribution through multiple channels<br>- ✅ Community engagement across various social networks |
| **Known Issues** | None identified - comprehensive social media presence confirmed |
| **References** | - Social media links available in footer of all pages<br>- Platform coverage includes Instagram, Facebook, WhatsApp, LinkedIn, Twitter, YouTube, Telegram, Threads, Google News<br>- Links verified on: students section, product pages, and main site |

---

## Flow 13: Logout functionality with behavior verification

| Section | Details |
|---------|---------|
| **Overview** | Verify that the logout functionality works correctly, properly terminates user sessions, and provides appropriate feedback and navigation options |
| **Scope** | Logout functionality testing including session termination, user state changes, and post-logout behavior verification |
| **Approach** | Interactive testing of logout process from user menu through complete session termination with verification of all behavioral changes |
| **Environment** | Production environment - https://taxmann.com/students/ and https://taxmann.com/gp/ |
| **Test Data** | Logged-in user account: Karan (<EMAIL>) |
| **Entry Criteria** | - User is logged in and authenticated<br>- User menu is accessible<br>- Logout option is available<br>- Session state can be verified |
| **Exit Criteria** | - User is successfully logged out<br>- Session is terminated<br>- Appropriate logout confirmation provided<br>- Re-login options are available |
| **Test Cases/Checklists** | **✅ Logout Functionality Testing Completed:**<br><br>**Pre-Logout State Verification:**<br>- ✅ User "Karan" successfully logged in and authenticated<br>- ✅ User menu accessible in header showing user name<br>- ✅ User menu dropdown contains all account options:<br>  - Welcome message with user name<br>  - Profile, Orders, Library, Wishlist options<br>  - Logout option clearly visible (ref=e679)<br>- ✅ User session active with access to protected features<br>- ✅ Cart contents preserved (1 item in cart)<br>- ✅ Wishlist contents preserved (1 item in wishlist)<br><br>**Logout Process Execution:**<br>- ✅ Clicked on user menu "Karan" to open dropdown<br>- ✅ Logout option clearly visible at bottom of menu<br>- ✅ Clicked "Logout" option successfully<br>- ✅ Logout process initiated immediately<br>- ✅ Page navigation occurred (execution context destroyed as expected)<br>- ✅ No errors or exceptions during logout process<br><br>**Post-Logout State Verification:**<br>- ✅ **Page Redirection**: Successfully redirected to logout page<br>  - URL: `https://www.taxmann.com/gp/auth/logout`<br>  - Page Title: "Taxmann BookStore"<br>- ✅ **Logout Confirmation**: Clear success message displayed<br>  - Message: "You have been successfully Signed Out"<br>  - Professional and user-friendly confirmation<br>- ✅ **Session Termination**: User session completely cleared<br>  - Header no longer shows user name "Karan"<br>  - Header now displays "Sign in" link instead<br>  - Authentication state properly reset<br><br>**User Interface Changes:**<br>- ✅ **Header Navigation Updated**:<br>  - User menu replaced with "Sign in" option<br>  - Search functionality still available<br>  - Navigation links remain accessible<br>- ✅ **Re-authentication Options**:<br>  - "Click here to sign-in again" link provided<br>  - Sign-in link in header navigation<br>  - Easy path back to login process<br><br>**Session Security Verification:**<br>- ✅ User session properly terminated (no residual authentication)<br>- ✅ Page context appropriately destroyed during logout<br>- ✅ Logout URL follows security best practices (`/gp/auth/logout`)<br>- ✅ No unauthorized access possible after logout<br><br>**User Experience Features:**<br>- ✅ **Seamless Process**: Logout completed in single click<br>- ✅ **Clear Feedback**: Immediate confirmation of successful logout<br>- ✅ **Easy Re-entry**: Multiple options to sign back in<br>- ✅ **Professional Design**: Logout page maintains site branding<br>- ✅ **Preserved Navigation**: Site navigation remains functional<br><br>**Browser Behavior Verification:**<br>- ✅ Page navigation handled correctly<br>- ✅ Browser history updated appropriately<br>- ✅ No JavaScript errors during transition<br>- ✅ Responsive design maintained on logout page<br><br>**Footer and Social Links Preserved:**<br>- ✅ Complete footer information maintained after logout<br>- ✅ Social media links remain functional<br>- ✅ Company information and policies accessible<br>- ✅ Site branding and professional appearance preserved |
| **Known Issues** | None identified during testing |
| **References** | - Logout URL: https://www.taxmann.com/gp/auth/logout<br>- Logout accessible via user menu dropdown<br>- Session management follows security best practices<br>- Re-login options readily available |

