#!/usr/bin/env python3
"""
Test script to verify CENTRALIZED QA mode functionality
"""

import os
import sys
sys.path.append('web-ui/src')

from utils.qa_system_prompt import get_qa_system_prompt, get_qa_extend_prompt, get_qa_enhanced_system_prompt
from utils.default_settings import get_default_agent_settings

def test_centralized_qa_system():
    """Test the centralized QA system prompt management"""
    print("Testing Centralized QA System...")

    # Test centralized QA prompt
    qa_prompt = get_qa_system_prompt()
    print(f"✓ Centralized QA Prompt: {len(qa_prompt)} characters")
    if qa_prompt:
        print(f"Preview: {qa_prompt[:100]}...")
        # Check for key QA elements
        if "TREE-BRANCH" in qa_prompt and "QA TEST REPORT" in qa_prompt:
            print("✓ Contains tree-branch methodology and report format")
        else:
            print("⚠️ Missing key QA elements")
    else:
        print("⚠️ No QA prompt returned (QA mode may be disabled)")

    # Test fallback prompts
    extend_prompt = get_qa_extend_prompt()
    print(f"✓ Fallback Extend Prompt: {len(extend_prompt)} characters")

    enhanced_prompt = get_qa_enhanced_system_prompt()
    print(f"✓ Enhanced Prompt: {len(enhanced_prompt)} characters")

    return True

def test_default_settings():
    """Test if default settings include QA configuration"""
    print("\nTesting Default Settings...")
    
    settings = get_default_agent_settings()
    
    # Check if QA-related settings are present
    qa_keys = ['override_system_prompt', 'extend_system_prompt']
    for key in qa_keys:
        if key in settings:
            print(f"✓ {key}: {len(str(settings[key]))} characters")
        else:
            print(f"✗ {key}: Missing")
    
    return True

def test_sys_prompt_loading():
    """Test if sys_prompt.txt is loaded correctly"""
    print("\nTesting sys_prompt.txt Loading...")
    
    sys_prompt_path = "sys_prompt.txt"
    if os.path.exists(sys_prompt_path):
        with open(sys_prompt_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        print(f"✓ sys_prompt.txt loaded: {len(content)} characters")
        print(f"Preview: {content[:100]}...")
        return True
    else:
        print("✗ sys_prompt.txt not found")
        return False

def test_environment_variables():
    """Test QA-related environment variables"""
    print("\nTesting Environment Variables...")
    
    qa_env_vars = [
        'DEFAULT_QA_MODE',
        'DEFAULT_OVERRIDE_SYSTEM_PROMPT',
        'DEFAULT_EXTEND_SYSTEM_PROMPT'
    ]
    
    for var in qa_env_vars:
        value = os.getenv(var, "Not set")
        print(f"  {var}: {value}")
    
    return True

def main():
    """Run all tests"""
    print("CENTRALIZED QA Mode Functionality Test")
    print("=" * 60)

    tests = [
        test_centralized_qa_system,
        test_default_settings,
        test_sys_prompt_loading,
        test_environment_variables
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed: {e}")
            results.append(False)

    print("\n" + "=" * 60)
    print(f"Test Results: {sum(results)}/{len(results)} passed")

    if all(results):
        print("✅ All tests passed! Centralized QA mode is working correctly.")
        print("\n🧪 QA AGENT FEATURES:")
        print("- ✅ Single point of system prompt control")
        print("- ✅ Tree-branch testing methodology")
        print("- ✅ Structured QA report generation")
        print("- ✅ Automatic QA behavior injection")
        print("- ✅ Enhanced bug detection and edge case testing")
    else:
        print("❌ Some tests failed. Check the configuration.")

    print("\n📋 CONFIGURATION:")
    print("✅ QA mode is controlled by DEFAULT_QA_MODE=true in .env")
    print("✅ System prompts are managed centrally by qa_system_prompt.py")
    print("✅ No multiple injection points - clean architecture")
    print("✅ Automatic QA report formatting in Final Result")

    print("\n🚀 USAGE:")
    print("1. Ensure DEFAULT_QA_MODE=true in web-ui/.env")
    print("2. Start the web-ui: cd web-ui && python webui.py")
    print("3. Use the 'Run Agent' tab")
    print("4. Give it a testing task and watch it work systematically!")
    print("5. Final Result will show structured QA report")

if __name__ == "__main__":
    main()
