# Web UI for Browser-Use

A Python-based web UI for interacting with browser automation.

---

## Installation Guide

### Setup Instructions

#### 1. Install `browser-use` Python Package

```bash
pip install browser-use
```

#### 2. Install Playwright Browsers

```bash
playwright install
```

#### 3. Clone the Repository

```bash
git clone https://github.com/browser-use/web-ui.git
cd web-ui
```

---

### Install UV

#### For macOS or Linux:

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### For Windows (PowerShell):

```powershell
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

---

### Create and Activate Virtual Environment

#### Create a virtual environment with Python 3.11:

```bash
uv venv --python 3.11
```

#### Activate the virtual environment:

- **Windows (Command Prompt):**

  ```cmd
  .venv\Scripts\activate
  ```

- **Windows (PowerShell):**

  ```powershell
  .\.venv\Scripts\Activate.ps1
  ```

- **Windows (bash):**

  ```bash
  source .venv/Scripts/activate
  ```

- **macOS / Linux:**
  ```bash
  source .venv/bin/activate
  ```

---

### Install Project Dependencies

```bash
uv pip install -r requirements.txt
playwright install
```

---

### Run the Web UI

```bash
py webui.py --ip 127.0.0.1 --port 7788
```

---

### Access the Interface

Open your browser and go to:  
[http://127.0.0.1:7788](http://127.0.0.1:7788)

---
