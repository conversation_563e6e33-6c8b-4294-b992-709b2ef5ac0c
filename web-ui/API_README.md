# DrCode UI Testing API

This document provides comprehensive information about the DrCode UI Testing API, which offers programmatic access to browser automation and UI testing capabilities.

## Overview

The DrCode UI Testing API provides REST endpoints that reuse the same components and settings as the WebUI interface, allowing you to:

- Create and manage browser automation tasks
- Configure LLM providers and models
- Set browser parameters
- Monitor task execution in real-time
- Download task results and recordings
- Save and load configurations

## Quick Start

### 1. Install Dependencies

```bash
pip install fastapi uvicorn pydantic aiohttp websockets
```

### 2. Start the API Server

```bash
# From the web-ui directory
python api_server.py --host 0.0.0.0 --port 8000
```

### 3. Access the API

- **API Base URL**: `http://localhost:8000`
- **Interactive Docs**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### 4. Quick Example

```python
from src.api.client import quick_task

# Run a simple task
result = await quick_task(
    task="Go to google.com and search for 'DrC<PERSON>'",
    llm_provider="openai",
    llm_model="gpt-4.1-mini"
)
print(f"Task completed: {result.status}")
```

## API Endpoints

### Task Management

| Method   | Endpoint                  | Description         |
| -------- | ------------------------- | ------------------- |
| `POST`   | `/tasks`                  | Create a new task   |
| `GET`    | `/tasks/{task_id}`        | Get task status     |
| `GET`    | `/tasks`                  | List all tasks      |
| `POST`   | `/tasks/{task_id}/stop`   | Stop a task         |
| `POST`   | `/tasks/{task_id}/pause`  | Pause a task        |
| `POST`   | `/tasks/{task_id}/resume` | Resume a task       |
| `DELETE` | `/tasks/{task_id}`        | Clear/delete a task |

### Task History and Files

| Method | Endpoint                            | Description            |
| ------ | ----------------------------------- | ---------------------- |
| `GET`  | `/tasks/{task_id}/history`          | Get task history       |
| `GET`  | `/tasks/{task_id}/history/download` | Download history JSON  |
| `GET`  | `/tasks/{task_id}/gif/download`     | Download recording GIF |

### User Assistance

| Method | Endpoint                  | Description             |
| ------ | ------------------------- | ----------------------- |
| `POST` | `/tasks/{task_id}/assist` | Provide user assistance |

### Configuration Management

| Method | Endpoint       | Description        |
| ------ | -------------- | ------------------ |
| `POST` | `/config/save` | Save configuration |
| `POST` | `/config/load` | Load configuration |

### System Information

| Method | Endpoint             | Description             |
| ------ | -------------------- | ----------------------- |
| `GET`  | `/models`            | Get available models    |
| `GET`  | `/models/{provider}` | Get models for provider |
| `GET`  | `/providers`         | Get LLM providers       |
| `GET`  | `/system/info`       | Get system information  |
| `GET`  | `/system/prompt/qa`  | Get QA system prompt    |
| `GET`  | `/health`            | Health check            |

### WebSocket Endpoints

| Endpoint                   | Description            |
| -------------------------- | ---------------------- |
| `WS` `/ws`                 | General system updates |
| `WS` `/ws/tasks/{task_id}` | Task-specific updates  |

## Configuration Models

### AgentSettings

```python
class AgentSettings(BaseModel):
    llm_provider: Optional[str] = None
    llm_model_name: Optional[str] = None
    llm_temperature: float = 0.6
    llm_base_url: Optional[str] = None
    llm_api_key: Optional[str] = None
    use_vision: bool = True
    max_steps: int = 100
    max_actions: int = 10
    max_input_tokens: int = 128000
    tool_calling_method: Optional[str] = "auto"
    override_system_prompt: Optional[str] = None
    extend_system_prompt: Optional[str] = None
    # ... more fields
```

### BrowserSettings

```python
class BrowserSettings(BaseModel):
    browser_binary_path: Optional[str] = None
    browser_user_data_dir: Optional[str] = None
    use_own_browser: bool = False
    keep_browser_open: bool = False
    headless: bool = False
    disable_security: bool = False
    window_w: int = 1280
    window_h: int = 1100
    save_recording_path: Optional[str] = None
    save_agent_history_path: str = "./tmp/agent_history"
    # ... more fields
```

## Usage Examples

### Basic Task Creation

```python
import asyncio
from src.api.client import DrCodeAPIClient
from src.api.models import AgentSettings, BrowserSettings

async def create_task_example():
    async with DrCodeAPIClient() as client:
        # Configure settings
        agent_settings = AgentSettings(
            llm_provider="openai",
            llm_model_name="gpt-4.1-mini",
            llm_temperature=0.3
        )

        browser_settings = BrowserSettings(
            headless=True,
            window_w=1920,
            window_h=1080
        )

        # Create task
        task_response = await client.create_task(
            task="Test the login form on example.com",
            agent_settings=agent_settings,
            browser_settings=browser_settings
        )

        print(f"Task ID: {task_response.task_id}")

        # Wait for completion
        final_status = await client.wait_for_task_completion(
            task_response.task_id
        )

        print(f"Status: {final_status.status}")
        return final_status

# Run the example
asyncio.run(create_task_example())
```

### Real-time Monitoring

```python
from src.api.client import DrCodeWebSocketClient

async def monitor_task(task_id):
    ws_client = DrCodeWebSocketClient()
    await ws_client.connect(task_id)

    # Message handlers
    async def on_status_update(data):
        print(f"Status: {data['data']['status']}")

    async def on_chat_message(data):
        role = data['data']['role']
        content = data['data']['content']
        print(f"[{role}]: {content}")

    # Register handlers
    ws_client.on_message("status_update", on_status_update)
    ws_client.on_message("chat_message", on_chat_message)

    # Listen for updates
    await ws_client.listen()
```

### Configuration Management

```python
async def save_and_load_config():
    async with DrCodeAPIClient() as client:
        # Save current configuration
        config_response = await client.save_configuration(
            config_name="my_test_config",
            agent_settings=AgentSettings(llm_provider="openai"),
            browser_settings=BrowserSettings(headless=True)
        )

        # Load configuration later
        await client.load_configuration(config_response['config_path'])
```

## Environment Variables

| Variable          | Description                   | Required |
| ----------------- | ----------------------------- | -------- |
| `OPENAI_API_KEY`  | OpenAI API key for LLM access | Yes\*    |
| `DRCODE_API_KEY`  | DrCode backend API key        | No       |
| `DRCODE_API_URL`  | DrCode API endpoint           | No       |
| `DEFAULT_QA_MODE` | Enable QA testing mode        | No       |

\*Required for OpenAI provider, other providers may use different keys.

## Error Handling

The API uses standard HTTP status codes:

- `200`: Success
- `400`: Bad Request (invalid parameters)
- `404`: Not Found (task/resource not found)
- `500`: Internal Server Error

Error responses include details:

```json
{
  "error": "Not Found",
  "message": "Task not found",
  "details": {...}
}
```

## Rate Limiting and Sessions

- The API supports multiple sessions using `session_id` parameter
- Each session maintains its own state and browser instances
- Default session ID is "default"
- No built-in rate limiting (implement as needed)

## Security Considerations

- The API runs locally by default (`127.0.0.1:8000`)
- For production use:
  - Configure proper CORS origins
  - Add authentication/authorization
  - Use HTTPS
  - Validate all inputs
  - Implement rate limiting

## Integration with WebUI

The API reuses the same components as the WebUI:

- Same `WebuiManager` for state management
- Same agent and browser configuration
- Same task execution logic
- Same callback system for real-time updates

This ensures consistency between WebUI and API interfaces.

## Troubleshooting

### Common Issues

1. **API server not starting**

   - Check if port 8000 is available
   - Install required dependencies: `pip install fastapi uvicorn`

2. **Task creation fails**

   - Verify LLM provider API keys are set
   - Check browser binary path if using custom browser

3. **WebSocket connection fails**

   - Ensure WebSocket support is enabled
   - Check firewall settings

4. **QA summaries not generated**
   - Set `OPENAI_API_KEY` environment variable
   - Enable QA mode with `DEFAULT_QA_MODE=true`

### Logs

The API server logs to console by default. Adjust log level:

```bash
python api_server.py --log-level debug
```

## Examples

See `examples/api_examples.py` for comprehensive usage examples including:

- Basic task execution
- Advanced configuration
- Real-time monitoring
- Error handling
- User assistance
- Configuration management

Run examples:

```bash
cd examples
python api_examples.py
```

## Contributing

To extend the API:

1. Add new models to `src/api/models.py`
2. Add new endpoints to `src/api/routes.py`
3. Update client methods in `src/api/client.py`
4. Add WebSocket events to `src/api/websockets.py`
5. Update documentation

## Support

For issues and questions:

- Check the interactive API docs at `/docs`
- Review example code in `examples/`
- Enable debug logging for detailed error information
