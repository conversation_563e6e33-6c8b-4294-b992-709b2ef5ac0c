#!/usr/bin/env python3
"""
Test script for the complete refinement + task creation workflow.
"""

import requests
import json
import time


def test_complete_workflow():
    """Test the complete workflow from prompt refinement to task creation."""
    
    base_url = "http://127.0.0.1:8001"
    
    print("Testing Complete Refinement + Task Creation Workflow")
    print("=" * 60)
    
    # Step 1: Try to create a task with a vague prompt (should trigger refinement)
    print("\n1. Creating task with vague prompt (should trigger refinement)...")
    
    vague_task_payload = {
        "task": "test the platform",
        "skip_refinement": False
    }
    
    response = requests.post(f"{base_url}/tasks", json=vague_task_payload)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        task_result = response.json()
        print(f"Response: {json.dumps(task_result, indent=2)}")
        
        if task_result.get("status") == "needs_refinement":
            session_id = task_result["refinement_session_id"]
            questions = task_result["clarifying_questions"]
            
            print(f"\nTask creation triggered refinement!")
            print(f"Session ID: {session_id}")
            print("Questions to answer:")
            for i, q in enumerate(questions, 1):
                print(f"  {i}. {q}")
            
            # Step 2: Answer the questions
            print("\n2. Answering clarifying questions...")
            
            answers_payload = {
                "session_id": session_id,
                "answers": [
                    "The Mindler Partner Platform login page at /partner/login",
                    "Enter valid partner credentials and click login button",
                    "Verify dashboard loads with partner name and navigation menu"
                ]
            }
            
            response = requests.post(
                f"{base_url}/refinement/{session_id}/answers", 
                json=answers_payload
            )
            
            if response.status_code == 200:
                answers_result = response.json()
                print(f"Refinement result: {json.dumps(answers_result, indent=2)}")
                
                # Step 3: Create task with refined prompt
                print("\n3. Creating task with refined prompt...")
                
                refined_task_payload = {
                    "task": "placeholder",  # Will be replaced by refinement session
                    "refinement_session_id": session_id,
                    "skip_refinement": False
                }
                
                response = requests.post(f"{base_url}/tasks", json=refined_task_payload)
                print(f"Status: {response.status_code}")
                
                if response.status_code == 200:
                    final_task_result = response.json()
                    print(f"Task created successfully!")
                    print(f"Task ID: {final_task_result.get('task_id')}")
                    print(f"Status: {final_task_result.get('status')}")
                    
                    # Step 4: Check task status
                    task_id = final_task_result.get('task_id')
                    if task_id:
                        print(f"\n4. Checking task status...")
                        time.sleep(2)  # Give it a moment to start
                        
                        response = requests.get(f"{base_url}/tasks/{task_id}")
                        if response.status_code == 200:
                            status_result = response.json()
                            print(f"Task status: {status_result.get('status')}")
                            print(f"Chat history length: {len(status_result.get('chat_history', []))}")
                        else:
                            print(f"Error getting task status: {response.text}")
                else:
                    print(f"Error creating final task: {response.text}")
            else:
                print(f"Error providing answers: {response.text}")
        else:
            print("Unexpected response - task should have triggered refinement")
    else:
        print(f"Error creating task: {response.text}")
    
    # Step 5: Test with detailed prompt (should skip refinement)
    print("\n5. Testing with detailed prompt (should skip refinement)...")
    
    detailed_task_payload = {
        "task": "Navigate to the Mindler Partner Platform login page at /partner/login, enter valid partner credentials (username: <EMAIL>, password: testpass123), click the login button, and verify successful authentication by checking that the dashboard page loads with the partner's name displayed in the header and the main navigation menu is visible.",
        "skip_refinement": False
    }
    
    response = requests.post(f"{base_url}/tasks", json=detailed_task_payload)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        detailed_task_result = response.json()
        print(f"Task created directly without refinement!")
        print(f"Task ID: {detailed_task_result.get('task_id')}")
        print(f"Status: {detailed_task_result.get('status')}")
    else:
        print(f"Error: {response.text}")
    
    # Step 6: Test skip refinement option
    print("\n6. Testing skip refinement option...")
    
    skip_refinement_payload = {
        "task": "test something",
        "skip_refinement": True
    }
    
    response = requests.post(f"{base_url}/tasks", json=skip_refinement_payload)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        skip_result = response.json()
        print(f"Task created with refinement skipped!")
        print(f"Task ID: {skip_result.get('task_id')}")
        print(f"Status: {skip_result.get('status')}")
    else:
        print(f"Error: {response.text}")
    
    # Step 7: List all tasks
    print("\n7. Listing all tasks...")
    
    response = requests.get(f"{base_url}/tasks")
    if response.status_code == 200:
        tasks_result = response.json()
        print(f"Total tasks: {len(tasks_result.get('tasks', []))}")
        for task in tasks_result.get('tasks', []):
            print(f"  - Task {task['task_id']}: {task['status']} - {task['task'][:50]}...")
    else:
        print(f"Error listing tasks: {response.text}")


if __name__ == "__main__":
    test_complete_workflow()
