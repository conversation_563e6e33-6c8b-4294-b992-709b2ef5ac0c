"""
Unified startup script for Dr<PERSON><PERSON> Browser Use Web UI.

This script can run:
1. Gradio Web UI only
2. FastAPI server only  
3. Both Gradio UI and FastAPI server simultaneously
"""
import argparse
import asyncio
import logging
import multiprocessing
import signal
import sys
from typing import Optional

import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_gradio_ui(ip: str, port: int, theme: str):
    """Run the Gradio Web UI"""
    try:
        from src.webui.interface import theme_map, create_ui
        
        logger.info(f"Starting Gradio UI on {ip}:{port}")
        demo = create_ui(theme_name=theme)
        demo.queue().launch(server_name=ip, server_port=port, share=True)
    except Exception as e:
        logger.error(f"Failed to start Gradio UI: {e}")
        sys.exit(1)


def run_api_server(host: str, port: int, reload: bool = False):
    """Run the FastAPI server"""
    try:
        logger.info(f"Starting API server on {host}:{port} (reload={reload})")
        
        # Import the FastAPI app
        from src.api.main import app
        
        # # Import extensions to register additional endpoints
        # import src.api.api_extensions as api_extensions
        
        uvicorn.run(app, host=host, port=port, log_level="info", reload=reload)
    except Exception as e:
        logger.error(f"Failed to start API server: {e}")
        sys.exit(1)


def run_both_servers(gradio_ip: str, gradio_port: int, api_host: str, api_port: int, theme: str, api_reload: bool = False):
    """Run both Gradio UI and API server simultaneously"""
    
    def signal_handler(signum, frame):
        """Handle shutdown signals"""
        logger.info("Received shutdown signal, stopping servers...")
        sys.exit(0)
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start Gradio UI in a separate process
    gradio_process = multiprocessing.Process(
        target=run_gradio_ui,
        args=(gradio_ip, gradio_port, theme)
    )
    
    # Start API server in a separate process
    api_process = multiprocessing.Process(
        target=run_api_server,
        args=(api_host, api_port, api_reload)
    )
    
    try:
        logger.info("Starting both Gradio UI and API server...")
        
        gradio_process.start()
        api_process.start()
        
        logger.info(f"🎯 Gradio UI: http://{gradio_ip}:{gradio_port}")
        logger.info(f"🚀 API Server: http://{api_host}:{api_port}")
        logger.info(f"📚 API Docs: http://{api_host}:{api_port}/docs")
        
        # Wait for both processes
        gradio_process.join()
        api_process.join()
        
    except KeyboardInterrupt:
        logger.info("Shutting down servers...")
    except Exception as e:
        logger.error(f"Error running servers: {e}")
    finally:
        # Cleanup processes
        if gradio_process.is_alive():
            gradio_process.terminate()
            gradio_process.join()
        
        if api_process.is_alive():
            api_process.terminate()
            api_process.join()
        
        logger.info("Servers stopped.")


def main():
    parser = argparse.ArgumentParser(description="DrCode Browser Use Web UI & API Server")
    
    # Mode selection
    parser.add_argument(
        "--mode",
        type=str,
        choices=["ui", "api", "both"],
        default="api",
        help="Run mode: 'ui' for Gradio only, 'api' for FastAPI only, 'both' for simultaneous"
    )
    
    # Gradio UI arguments
    parser.add_argument("--ui-ip", type=str, default="127.0.0.1", help="Gradio UI IP address")
    parser.add_argument("--ui-port", type=int, default=7788, help="Gradio UI port")
    parser.add_argument(
        "--theme",
        type=str,
        default="Ocean",
        choices=["Default", "Soft", "Monochrome", "Glass", "Origin", "Citrus", "Ocean", "Base"],
        help="Gradio UI theme"
    )
    
    # FastAPI arguments
    parser.add_argument("--api-host", type=str, default="0.0.0.0", help="API server host")
    parser.add_argument("--api-port", type=int, default=8000, help="API server port")
    
    # Advanced options
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--reload", action="store_true", help="Reload the server when code changes", default=False)
    
    args = parser.parse_args()
    
    # Setup debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Display startup banner
    print("\n" + "="*60)
    print("🤖 DrCode Browser Use Web UI & API")
    print("="*60)
    print(f"Mode: {args.mode.upper()}")
    
    if args.mode in ["ui", "both"]:
        print(f"Gradio UI: http://{args.ui_ip}:{args.ui_port}")
    
    if args.mode in ["api", "both"]:
        print(f"API Server: http://{args.api_host}:{args.api_port}")
        print(f"API Docs: http://{args.api_host}:{args.api_port}/docs")
    
    print("="*60 + "\n")
    
    # Run based on mode
    try:
        if args.mode == "ui":
            run_gradio_ui(args.ui_ip, args.ui_port, args.theme)
        elif args.mode == "api":
            run_api_server(args.api_host, args.api_port, args.reload)
        elif args.mode == "both":
            run_both_servers(args.ui_ip, args.ui_port, args.api_host, args.api_port, args.theme, args.reload)
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down...")
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
