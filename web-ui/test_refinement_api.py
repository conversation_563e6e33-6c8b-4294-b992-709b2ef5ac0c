#!/usr/bin/env python3
"""
Test script for the refinement API endpoints.
"""

import requests
import json
import time


def test_refinement_api():
    """Test the refinement API endpoints."""
    
    base_url = "http://127.0.0.1:8001"
    
    print("Testing Prompt Refinement API")
    print("=" * 50)
    
    # Test 1: Start refinement with a vague prompt
    print("\n1. Testing refinement start with vague prompt...")
    
    start_payload = {
        "prompt": "test the login"
    }
    
    response = requests.post(f"{base_url}/refinement/start", json=start_payload)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        start_result = response.json()
        print(f"Response: {json.dumps(start_result, indent=2)}")
        
        if start_result.get("needs_refinement"):
            session_id = start_result["session_id"]
            questions = start_result["clarifying_questions"]
            
            print(f"\nSession ID: {session_id}")
            print("Clarifying questions:")
            for i, q in enumerate(questions, 1):
                print(f"  {i}. {q}")
            
            # Test 2: Provide answers
            print("\n2. Providing answers to clarifying questions...")
            
            answers_payload = {
                "session_id": session_id,
                "answers": [
                    "The partner login page at /partner/login",
                    "Enter valid username and password, then click login button",
                    "Verify that the dashboard page loads with partner name displayed"
                ]
            }
            
            response = requests.post(
                f"{base_url}/refinement/{session_id}/answers", 
                json=answers_payload
            )
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                answers_result = response.json()
                print(f"Response: {json.dumps(answers_result, indent=2)}")
                
                # Test 3: Check session status
                print("\n3. Checking session status...")
                
                response = requests.get(f"{base_url}/refinement/{session_id}/status")
                print(f"Status: {response.status_code}")
                
                if response.status_code == 200:
                    status_result = response.json()
                    print(f"Final refined prompt: {status_result.get('refined_prompt')}")
                    
                    # Test 4: Create task with refined prompt
                    if status_result.get("state") == "completed":
                        print("\n4. Creating task with refined prompt...")
                        
                        task_payload = {
                            "task": "placeholder",  # Will be replaced by refinement
                            "refinement_session_id": session_id,
                            "skip_refinement": False
                        }
                        
                        response = requests.post(f"{base_url}/tasks", json=task_payload)
                        print(f"Status: {response.status_code}")
                        
                        if response.status_code == 200:
                            task_result = response.json()
                            print(f"Task created: {json.dumps(task_result, indent=2)}")
                        else:
                            print(f"Error creating task: {response.text}")
                else:
                    print(f"Error getting status: {response.text}")
            else:
                print(f"Error providing answers: {response.text}")
        else:
            print("Prompt was already sufficiently detailed!")
    else:
        print(f"Error starting refinement: {response.text}")
    
    # Test 5: Test with already detailed prompt
    print("\n5. Testing with already detailed prompt...")
    
    detailed_payload = {
        "prompt": "Navigate to the Mindler Partner Platform login page at /partner/login, enter valid credentials (username: <EMAIL>, password: testpass123), click the login button, and verify successful authentication by checking that the dashboard page loads with the partner's name displayed in the header."
    }
    
    response = requests.post(f"{base_url}/refinement/start", json=detailed_payload)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        detailed_result = response.json()
        print(f"Needs refinement: {detailed_result.get('needs_refinement')}")
        print(f"Message: {detailed_result.get('message')}")
    else:
        print(f"Error: {response.text}")
    
    # Test 6: List active sessions
    print("\n6. Listing active refinement sessions...")
    
    response = requests.get(f"{base_url}/refinement/sessions")
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        sessions_result = response.json()
        print(f"Active sessions: {len(sessions_result.get('sessions', []))}")
    else:
        print(f"Error: {response.text}")


if __name__ == "__main__":
    test_refinement_api()
