import unittest
import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from uuid import uuid4
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.api.services.chat_service import ChatService
from src.api.models.schemas import Cha<PERSON>, ChatCreate, ChatUpdate, ChatResponse, ChatListResponse, ChatWithMessagesResponse


class TestChatService(unittest.TestCase):
    """Unit tests for ChatService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = ChatService()
        self.mock_repository = Mock()
        self.service.repository = self.mock_repository
        
        # Sample test data
        self.sample_chat_id = str(uuid4())
        self.sample_user_id = "test_user_123"
        self.sample_project_id = "test_project_456"
        self.sample_title = "Test Chat"
        
        self.sample_chat = Chat(
            chat_id=self.sample_chat_id,
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            title=self.sample_title,
            created_at=datetime(2024, 1, 1, 12, 0, 0),
            updated_at=datetime(2024, 1, 1, 12, 0, 0),
            message_count=5
        )
        
        self.sample_chat_create = ChatCreate(
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            title=self.sample_title
        )
    
    @pytest.mark.asyncio
    async def test_create_chat_success(self):
        """Test successful chat creation."""
        self.mock_repository.create_chat.return_value = self.sample_chat
        
        result = await self.service.create_chat(self.sample_chat_create)
        
        self.mock_repository.create_chat.assert_called_once_with(self.sample_chat_create)
        self.assertIsInstance(result, ChatResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Chat created successfully")
        self.assertEqual(result.data, self.sample_chat)
    
    @pytest.mark.asyncio
    async def test_create_chat_failure(self):
        """Test chat creation failure."""
        self.mock_repository.create_chat.side_effect = Exception("Database error")
        
        result = await self.service.create_chat(self.sample_chat_create)
        
        self.assertIsInstance(result, ChatResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to create chat", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_chat_found(self):
        """Test getting an existing chat."""
        self.mock_repository.get_chat.return_value = self.sample_chat
        
        result = await self.service.get_chat(self.sample_chat_id)
        
        self.mock_repository.get_chat.assert_called_once_with(self.sample_chat_id)
        self.assertIsInstance(result, ChatResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Chat retrieved successfully")
        self.assertEqual(result.data, self.sample_chat)
    
    @pytest.mark.asyncio
    async def test_get_chat_not_found(self):
        """Test getting a non-existent chat."""
        self.mock_repository.get_chat.return_value = None
        
        result = await self.service.get_chat("nonexistent_id")
        
        self.assertIsInstance(result, ChatResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_chat_exception(self):
        """Test get_chat with exception."""
        self.mock_repository.get_chat.side_effect = Exception("Database error")
        
        result = await self.service.get_chat(self.sample_chat_id)
        
        self.assertIsInstance(result, ChatResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to retrieve chat", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_all_chats_success(self):
        """Test getting all chats successfully."""
        chats = [self.sample_chat]
        self.mock_repository.get_all_chats.return_value = chats
        self.mock_repository.get_chat_count.return_value = 1
        
        result = await self.service.get_all_chats(skip=0, limit=10)
        
        self.mock_repository.get_all_chats.assert_called_once_with(skip=0, limit=10)
        self.mock_repository.get_chat_count.assert_called_once()
        self.assertIsInstance(result, ChatListResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Chats retrieved successfully")
        self.assertEqual(result.data, chats)
        self.assertEqual(result.total, 1)
    
    @pytest.mark.asyncio
    async def test_get_all_chats_failure(self):
        """Test get_all_chats with exception."""
        self.mock_repository.get_all_chats.side_effect = Exception("Database error")
        
        result = await self.service.get_all_chats()
        
        self.assertIsInstance(result, ChatListResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to retrieve chats", result.message)
        self.assertEqual(result.data, [])
        self.assertEqual(result.total, 0)
    
    @pytest.mark.asyncio
    async def test_get_user_chats_success(self):
        """Test getting user chats successfully."""
        chats = [self.sample_chat]
        self.mock_repository.get_user_chats.return_value = chats
        self.mock_repository.get_user_chat_count.return_value = 1
        
        result = await self.service.get_user_chats(self.sample_user_id, skip=0, limit=5)
        
        self.mock_repository.get_user_chats.assert_called_once_with(self.sample_user_id, skip=0, limit=5)
        self.mock_repository.get_user_chat_count.assert_called_once_with(self.sample_user_id)
        self.assertIsInstance(result, ChatListResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "User chats retrieved successfully")
        self.assertEqual(result.data, chats)
        self.assertEqual(result.total, 1)
    
    @pytest.mark.asyncio
    async def test_get_user_chats_failure(self):
        """Test get_user_chats with exception."""
        self.mock_repository.get_user_chats.side_effect = Exception("Database error")
        
        result = await self.service.get_user_chats(self.sample_user_id)
        
        self.assertIsInstance(result, ChatListResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to retrieve user chats", result.message)
        self.assertEqual(result.data, [])
        self.assertEqual(result.total, 0)
    
    @pytest.mark.asyncio
    async def test_get_chat_with_messages_found(self):
        """Test getting chat with messages successfully."""
        from src.api.models.schemas import ChatWithMessages
        
        chat_with_messages = ChatWithMessages(
            chat_id=self.sample_chat_id,
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            title=self.sample_title,
            created_at=datetime(2024, 1, 1, 12, 0, 0),
            updated_at=datetime(2024, 1, 1, 12, 0, 0),
            message_count=5,
            messages=[],
            tasks=[]
        )
        
        self.mock_repository.get_chat_with_messages.return_value = chat_with_messages
        
        result = await self.service.get_chat_with_messages(self.sample_chat_id, message_limit=50)
        
        self.mock_repository.get_chat_with_messages.assert_called_once_with(self.sample_chat_id, message_limit=50)
        self.assertIsInstance(result, ChatWithMessagesResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Chat with messages retrieved successfully")
        self.assertEqual(result.data, chat_with_messages)
    
    @pytest.mark.asyncio
    async def test_get_chat_with_messages_not_found(self):
        """Test getting chat with messages when chat not found."""
        self.mock_repository.get_chat_with_messages.return_value = None
        
        result = await self.service.get_chat_with_messages("nonexistent_id")
        
        self.assertIsInstance(result, ChatWithMessagesResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_chat_with_messages_exception(self):
        """Test get_chat_with_messages with exception."""
        self.mock_repository.get_chat_with_messages.side_effect = Exception("Database error")
        
        result = await self.service.get_chat_with_messages(self.sample_chat_id)
        
        self.assertIsInstance(result, ChatWithMessagesResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to retrieve chat with messages", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_update_chat_success(self):
        """Test successful chat update."""
        chat_update = ChatUpdate(title="Updated Title")
        updated_chat = Chat(
            chat_id=self.sample_chat_id,
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            title="Updated Title",
            created_at=datetime(2024, 1, 1, 12, 0, 0),
            updated_at=datetime(2024, 1, 1, 13, 0, 0),
            message_count=5
        )
        
        self.mock_repository.update_chat.return_value = updated_chat
        
        result = await self.service.update_chat(self.sample_chat_id, chat_update)
        
        self.mock_repository.update_chat.assert_called_once_with(self.sample_chat_id, chat_update)
        self.assertIsInstance(result, ChatResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Chat updated successfully")
        self.assertEqual(result.data, updated_chat)
    
    @pytest.mark.asyncio
    async def test_update_chat_not_found(self):
        """Test updating a non-existent chat."""
        chat_update = ChatUpdate(title="Updated Title")
        self.mock_repository.update_chat.return_value = None
        
        result = await self.service.update_chat("nonexistent_id", chat_update)
        
        self.assertIsInstance(result, ChatResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_update_chat_exception(self):
        """Test update_chat with exception."""
        chat_update = ChatUpdate(title="Updated Title")
        self.mock_repository.update_chat.side_effect = Exception("Database error")
        
        result = await self.service.update_chat(self.sample_chat_id, chat_update)
        
        self.assertIsInstance(result, ChatResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to update chat", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_delete_chat_success(self):
        """Test successful chat deletion."""
        self.mock_repository.delete_chat.return_value = True
        
        result = await self.service.delete_chat(self.sample_chat_id)
        
        self.mock_repository.delete_chat.assert_called_once_with(self.sample_chat_id)
        self.assertIsInstance(result, ChatResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Chat deleted successfully")
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_delete_chat_not_found(self):
        """Test deleting a non-existent chat."""
        self.mock_repository.delete_chat.return_value = False
        
        result = await self.service.delete_chat("nonexistent_id")
        
        self.assertIsInstance(result, ChatResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_delete_chat_exception(self):
        """Test delete_chat with exception."""
        self.mock_repository.delete_chat.side_effect = Exception("Database error")
        
        result = await self.service.delete_chat(self.sample_chat_id)
        
        self.assertIsInstance(result, ChatResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to delete chat", result.message)
        self.assertIsNone(result.data)


if __name__ == '__main__':
    unittest.main()