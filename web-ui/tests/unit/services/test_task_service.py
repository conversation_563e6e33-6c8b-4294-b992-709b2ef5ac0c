import unittest
import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from uuid import uuid4
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.api.services.task_service import TaskService
from src.api.models.schemas import (
    Task, TaskCreate, TaskUpdate, TaskResponse, TaskListResponse, 
    TaskStatusEnum, TaskPriority, MessageRole
)


class TestTaskService(unittest.TestCase):
    """Unit tests for TaskService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = TaskService()
        self.mock_repository = Mock()
        self.service.repository = self.mock_repository
        
        # Sample test data
        self.sample_task_id = str(uuid4())
        self.sample_chat_id = str(uuid4())
        self.sample_user_id = "test_user_123"
        self.sample_project_id = "test_project_456"
        self.sample_title = "Test Task"
        self.sample_description = "Test task description"
        
        self.sample_task = Task(
            id=self.sample_task_id,
            title=self.sample_title,
            description=self.sample_description,
            priority=TaskPriority.MEDIUM,
            status=TaskStatusEnum.PENDING,
            created_at=datetime(2024, 1, 1, 12, 0, 0),
            updated_at=datetime(2024, 1, 1, 12, 0, 0),
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            chat_id=self.sample_chat_id
        )
        
        self.sample_task_create = TaskCreate(
            title=self.sample_title,
            description=self.sample_description,
            priority=TaskPriority.MEDIUM,
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            chat_id=self.sample_chat_id
        )
        
        # Mock FastAPI BackgroundTasks
        self.mock_background_tasks = Mock()
    
    @pytest.mark.asyncio
    async def test_create_task_success(self):
        """Test successful task creation."""
        # Mock the dependencies
        with patch('src.api.services.task_service.chat_repository') as mock_chat_repo, \
             patch('src.api.services.task_service.message_service') as mock_msg_service:
            
            # Setup mocks
            mock_chat = Mock()
            mock_chat_repo.get_chat.return_value = mock_chat
            mock_msg_service.create_message = AsyncMock()
            self.mock_repository.create_task.return_value = self.sample_task
            
            result = await self.service.create_task(self.sample_task_create, self.mock_background_tasks)
            
            # Verify calls
            mock_chat_repo.get_chat.assert_called_once_with(self.sample_chat_id)
            self.mock_repository.create_task.assert_called_once_with(self.sample_task_create)
            mock_msg_service.create_message.assert_called_once()
            self.mock_background_tasks.add_task.assert_called_once()
            
            # Verify result
            self.assertIsInstance(result, TaskResponse)
            self.assertTrue(result.success)
            self.assertIn("created successfully", result.message)
            self.assertEqual(result.data, self.sample_task)
    
    @pytest.mark.asyncio
    async def test_create_task_chat_not_found(self):
        """Test task creation when chat doesn't exist."""
        with patch('src.api.services.task_service.chat_repository') as mock_chat_repo:
            mock_chat_repo.get_chat.return_value = None
            
            result = await self.service.create_task(self.sample_task_create, self.mock_background_tasks)
            
            self.assertIsInstance(result, TaskResponse)
            self.assertFalse(result.success)
            self.assertIn("Chat with ID", result.message)
            self.assertIn("not found", result.message)
            self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_create_task_without_chat_id(self):
        """Test task creation without chat_id."""
        task_create_no_chat = TaskCreate(
            title=self.sample_title,
            description=self.sample_description,
            priority=TaskPriority.HIGH,
            user_id=self.sample_user_id,
            project_id=self.sample_project_id
        )
        
        with patch('src.api.services.task_service.message_service') as mock_msg_service:
            mock_msg_service.create_message = AsyncMock()
            self.mock_repository.create_task.return_value = self.sample_task
            
            result = await self.service.create_task(task_create_no_chat, self.mock_background_tasks)
            
            # Should succeed without chat validation
            self.assertIsInstance(result, TaskResponse)
            self.assertTrue(result.success)
            self.assertEqual(result.data, self.sample_task)
    
    @pytest.mark.asyncio
    async def test_create_task_exception(self):
        """Test task creation with exception."""
        with patch('src.api.services.task_service.chat_repository') as mock_chat_repo:
            mock_chat_repo.get_chat.side_effect = Exception("Database error")
            
            result = await self.service.create_task(self.sample_task_create, self.mock_background_tasks)
            
            self.assertIsInstance(result, TaskResponse)
            self.assertFalse(result.success)
            self.assertIn("Failed to create task", result.message)
            self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_task_found(self):
        """Test getting an existing task."""
        self.mock_repository.get_task.return_value = self.sample_task
        
        result = await self.service.get_task(self.sample_task_id)
        
        self.mock_repository.get_task.assert_called_once_with(self.sample_task_id)
        self.assertIsInstance(result, TaskResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Task retrieved successfully")
        self.assertEqual(result.data, self.sample_task)
    
    @pytest.mark.asyncio
    async def test_get_task_not_found(self):
        """Test getting a non-existent task."""
        self.mock_repository.get_task.return_value = None
        
        result = await self.service.get_task("nonexistent_id")
        
        self.assertIsInstance(result, TaskResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_all_tasks_success(self):
        """Test getting all tasks successfully."""
        tasks = [self.sample_task]
        self.mock_repository.get_all_tasks.return_value = tasks
        self.mock_repository.get_task_count.return_value = 1
        
        result = await self.service.get_all_tasks(skip=0, limit=10)
        
        self.mock_repository.get_all_tasks.assert_called_once_with(skip=0, limit=10)
        self.mock_repository.get_task_count.assert_called_once()
        self.assertIsInstance(result, TaskListResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Tasks retrieved successfully")
        self.assertEqual(result.data, tasks)
        self.assertEqual(result.total, 1)
    
    @pytest.mark.asyncio
    async def test_get_all_tasks_failure(self):
        """Test get_all_tasks with exception."""
        self.mock_repository.get_all_tasks.side_effect = Exception("Database error")
        
        result = await self.service.get_all_tasks()
        
        self.assertIsInstance(result, TaskListResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to retrieve tasks", result.message)
        self.assertEqual(result.data, [])
        self.assertEqual(result.total, 0)
    
    @pytest.mark.asyncio
    async def test_update_task_success(self):
        """Test successful task update."""
        task_update = TaskUpdate(title="Updated Title", status=TaskStatusEnum.RUNNING)
        updated_task = Task(
            id=self.sample_task_id,
            title="Updated Title",
            description=self.sample_description,
            priority=TaskPriority.MEDIUM,
            status=TaskStatusEnum.RUNNING,
            created_at=datetime(2024, 1, 1, 12, 0, 0),
            updated_at=datetime(2024, 1, 1, 13, 0, 0),
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            chat_id=self.sample_chat_id
        )
        
        self.mock_repository.update_task.return_value = updated_task
        
        result = await self.service.update_task(self.sample_task_id, task_update)
        
        self.mock_repository.update_task.assert_called_once_with(self.sample_task_id, task_update)
        self.assertIsInstance(result, TaskResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Task updated successfully")
        self.assertEqual(result.data, updated_task)
    
    @pytest.mark.asyncio
    async def test_update_task_not_found(self):
        """Test updating a non-existent task."""
        task_update = TaskUpdate(title="Updated Title")
        self.mock_repository.update_task.return_value = None
        
        result = await self.service.update_task("nonexistent_id", task_update)
        
        self.assertIsInstance(result, TaskResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_update_task_exception(self):
        """Test update_task with exception."""
        task_update = TaskUpdate(title="Updated Title")
        self.mock_repository.update_task.side_effect = Exception("Database error")
        
        result = await self.service.update_task(self.sample_task_id, task_update)
        
        self.assertIsInstance(result, TaskResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to update task", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_delete_task_success(self):
        """Test successful task deletion."""
        self.mock_repository.delete_task.return_value = True
        
        result = await self.service.delete_task(self.sample_task_id)
        
        self.mock_repository.delete_task.assert_called_once_with(self.sample_task_id)
        self.assertIsInstance(result, TaskResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Task deleted successfully")
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_delete_task_not_found(self):
        """Test deleting a non-existent task."""
        self.mock_repository.delete_task.return_value = False
        
        result = await self.service.delete_task("nonexistent_id")
        
        self.assertIsInstance(result, TaskResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_delete_task_exception(self):
        """Test delete_task with exception."""
        self.mock_repository.delete_task.side_effect = Exception("Database error")
        
        result = await self.service.delete_task(self.sample_task_id)
        
        self.assertIsInstance(result, TaskResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to delete task", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_tasks_by_status_success(self):
        """Test getting tasks by status successfully."""
        tasks = [self.sample_task]
        self.mock_repository.get_tasks_by_status.return_value = tasks
        
        result = await self.service.get_tasks_by_status(TaskStatusEnum.PENDING)
        
        self.mock_repository.get_tasks_by_status.assert_called_once_with(TaskStatusEnum.PENDING)
        self.assertIsInstance(result, TaskListResponse)
        self.assertTrue(result.success)
        self.assertIn("pending", result.message)
        self.assertEqual(result.data, tasks)
        self.assertEqual(result.total, 1)
    
    @pytest.mark.asyncio
    async def test_get_tasks_by_status_failure(self):
        """Test get_tasks_by_status with exception."""
        self.mock_repository.get_tasks_by_status.side_effect = Exception("Database error")
        
        result = await self.service.get_tasks_by_status(TaskStatusEnum.RUNNING)
        
        self.assertIsInstance(result, TaskListResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to retrieve tasks by status", result.message)
        self.assertEqual(result.data, [])
        self.assertEqual(result.total, 0)
    
    @pytest.mark.asyncio
    async def test_update_task_status_success(self):
        """Test updating task status successfully."""
        updated_task = Task(
            id=self.sample_task_id,
            title=self.sample_title,
            description=self.sample_description,
            priority=TaskPriority.MEDIUM,
            status=TaskStatusEnum.COMPLETED,
            created_at=datetime(2024, 1, 1, 12, 0, 0),
            updated_at=datetime(2024, 1, 1, 13, 0, 0),
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            chat_id=self.sample_chat_id
        )
        
        self.mock_repository.update_task.return_value = updated_task
        
        result = await self.service.update_task_status(self.sample_task_id, TaskStatusEnum.COMPLETED)
        
        # Verify update_task was called with TaskUpdate containing the status
        call_args = self.mock_repository.update_task.call_args
        self.assertEqual(call_args[0][0], self.sample_task_id)  # First argument is task_id
        task_update = call_args[0][1]  # Second argument is TaskUpdate
        self.assertEqual(task_update.status, TaskStatusEnum.COMPLETED)
        
        self.assertIsInstance(result, TaskResponse)
        self.assertTrue(result.success)
        self.assertIn("Task status updated", result.message)
        self.assertEqual(result.data, updated_task)
    
    @pytest.mark.asyncio
    async def test_update_task_status_failure(self):
        """Test update_task_status with exception."""
        self.mock_repository.update_task.side_effect = Exception("Database error")
        
        result = await self.service.update_task_status(self.sample_task_id, TaskStatusEnum.FAILED)
        
        self.assertIsInstance(result, TaskResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to update task status", result.message)
        self.assertIsNone(result.data)


if __name__ == '__main__':
    unittest.main()