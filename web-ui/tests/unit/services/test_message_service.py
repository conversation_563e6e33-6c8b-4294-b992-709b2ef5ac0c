import unittest
import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from uuid import uuid4
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.api.services.message_service import MessageService
from src.api.models.schemas import Message, MessageCreate, MessageUpdate, MessageResponse, MessageListResponse, MessageRole


class TestMessageService(unittest.TestCase):
    """Unit tests for MessageService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = MessageService()
        self.mock_repository = Mock()
        self.service.repository = self.mock_repository
        
        # Sample test data
        self.sample_message_id = str(uuid4())
        self.sample_chat_id = str(uuid4())
        self.sample_content = "Test message content"
        self.sample_metadata = {"key": "value"}
        
        self.sample_message = Message(
            message_id=self.sample_message_id,
            chat_id=self.sample_chat_id,
            content=self.sample_content,
            role=MessageRole.USER,
            metadata=self.sample_metadata,
            created_at=datetime(2024, 1, 1, 12, 0, 0),
            updated_at=datetime(2024, 1, 1, 12, 0, 0)
        )
        
        self.sample_message_create = MessageCreate(
            chat_id=self.sample_chat_id,
            content=self.sample_content,
            role=MessageRole.USER,
            metadata=self.sample_metadata
        )
    
    @pytest.mark.asyncio
    async def test_create_message_success(self):
        """Test successful message creation."""
        self.mock_repository.create_message.return_value = self.sample_message
        
        with patch('src.api.services.message_service.publish_chat_message') as mock_publish:
            mock_publish.return_value = None  # Async function mock
            
            result = await self.service.create_message(self.sample_message_create)
            
            self.mock_repository.create_message.assert_called_once_with(self.sample_message_create)
            mock_publish.assert_called_once_with(self.sample_chat_id, self.sample_content)
            self.assertIsInstance(result, MessageResponse)
            self.assertTrue(result.success)
            self.assertEqual(result.message, "Message created successfully")
            self.assertEqual(result.data, self.sample_message)
    
    @pytest.mark.asyncio
    async def test_create_message_failure(self):
        """Test message creation failure."""
        self.mock_repository.create_message.side_effect = Exception("Database error")
        
        with patch('src.api.services.message_service.publish_chat_message') as mock_publish:
            result = await self.service.create_message(self.sample_message_create)
            
            # publish_chat_message should not be called if creation fails
            mock_publish.assert_not_called()
            self.assertIsInstance(result, MessageResponse)
            self.assertFalse(result.success)
            self.assertIn("Failed to create message", result.message)
            self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_message_found(self):
        """Test getting an existing message."""
        self.mock_repository.get_message.return_value = self.sample_message
        
        result = await self.service.get_message(self.sample_message_id)
        
        self.mock_repository.get_message.assert_called_once_with(self.sample_message_id)
        self.assertIsInstance(result, MessageResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Message retrieved successfully")
        self.assertEqual(result.data, self.sample_message)
    
    @pytest.mark.asyncio
    async def test_get_message_not_found(self):
        """Test getting a non-existent message."""
        self.mock_repository.get_message.return_value = None
        
        result = await self.service.get_message("nonexistent_id")
        
        self.assertIsInstance(result, MessageResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_message_exception(self):
        """Test get_message with exception."""
        self.mock_repository.get_message.side_effect = Exception("Database error")
        
        result = await self.service.get_message(self.sample_message_id)
        
        self.assertIsInstance(result, MessageResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to retrieve message", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_get_chat_messages_success(self):
        """Test getting chat messages successfully."""
        messages = [self.sample_message]
        self.mock_repository.get_chat_messages.return_value = messages
        self.mock_repository.get_chat_message_count.return_value = 1
        
        result = await self.service.get_chat_messages(self.sample_chat_id, skip=0, limit=10)
        
        self.mock_repository.get_chat_messages.assert_called_once_with(self.sample_chat_id, skip=0, limit=10)
        self.mock_repository.get_chat_message_count.assert_called_once_with(self.sample_chat_id)
        self.assertIsInstance(result, MessageListResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Messages retrieved successfully")
        self.assertEqual(result.data, messages)
        self.assertEqual(result.total, 1)
    
    @pytest.mark.asyncio
    async def test_get_chat_messages_failure(self):
        """Test get_chat_messages with exception."""
        self.mock_repository.get_chat_messages.side_effect = Exception("Database error")
        
        result = await self.service.get_chat_messages(self.sample_chat_id)
        
        self.assertIsInstance(result, MessageListResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to retrieve messages", result.message)
        self.assertEqual(result.data, [])
        self.assertEqual(result.total, 0)
    
    @pytest.mark.asyncio
    async def test_update_message_success(self):
        """Test successful message update."""
        message_update = MessageUpdate(content="Updated content")
        updated_message = Message(
            message_id=self.sample_message_id,
            chat_id=self.sample_chat_id,
            content="Updated content",
            role=MessageRole.USER,
            metadata=self.sample_metadata,
            created_at=datetime(2024, 1, 1, 12, 0, 0),
            updated_at=datetime(2024, 1, 1, 13, 0, 0)
        )
        
        self.mock_repository.update_message.return_value = updated_message
        
        result = await self.service.update_message(self.sample_message_id, message_update)
        
        self.mock_repository.update_message.assert_called_once_with(self.sample_message_id, message_update)
        self.assertIsInstance(result, MessageResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Message updated successfully")
        self.assertEqual(result.data, updated_message)
    
    @pytest.mark.asyncio
    async def test_update_message_not_found(self):
        """Test updating a non-existent message."""
        message_update = MessageUpdate(content="Updated content")
        self.mock_repository.update_message.return_value = None
        
        result = await self.service.update_message("nonexistent_id", message_update)
        
        self.assertIsInstance(result, MessageResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_update_message_exception(self):
        """Test update_message with exception."""
        message_update = MessageUpdate(content="Updated content")
        self.mock_repository.update_message.side_effect = Exception("Database error")
        
        result = await self.service.update_message(self.sample_message_id, message_update)
        
        self.assertIsInstance(result, MessageResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to update message", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_delete_message_success(self):
        """Test successful message deletion."""
        self.mock_repository.delete_message.return_value = True
        
        result = await self.service.delete_message(self.sample_message_id)
        
        self.mock_repository.delete_message.assert_called_once_with(self.sample_message_id)
        self.assertIsInstance(result, MessageResponse)
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Message deleted successfully")
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_delete_message_not_found(self):
        """Test deleting a non-existent message."""
        self.mock_repository.delete_message.return_value = False
        
        result = await self.service.delete_message("nonexistent_id")
        
        self.assertIsInstance(result, MessageResponse)
        self.assertFalse(result.success)
        self.assertIn("not found", result.message)
        self.assertIsNone(result.data)
    
    @pytest.mark.asyncio
    async def test_delete_message_exception(self):
        """Test delete_message with exception."""
        self.mock_repository.delete_message.side_effect = Exception("Database error")
        
        result = await self.service.delete_message(self.sample_message_id)
        
        self.assertIsInstance(result, MessageResponse)
        self.assertFalse(result.success)
        self.assertIn("Failed to delete message", result.message)
        self.assertIsNone(result.data)


if __name__ == '__main__':
    unittest.main()