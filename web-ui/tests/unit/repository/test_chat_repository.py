import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from uuid import uuid4
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.api.repository.chat_repository import ChatRepository
from src.api.models.schemas import Cha<PERSON>, ChatCreate, ChatUpdate, ChatWithMessages


class TestChatRepository(unittest.TestCase):
    """Unit tests for ChatRepository."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_redis = Mock()
        self.repository = ChatRepository()
        self.repository.redis_client = self.mock_redis
        
        # Sample test data
        self.sample_chat_id = str(uuid4())
        self.sample_user_id = "test_user_123"
        self.sample_project_id = "test_project_456"
        self.sample_title = "Test Chat"
        
        self.sample_chat_dict = {
            "chat_id": self.sample_chat_id,
            "user_id": self.sample_user_id,
            "project_id": self.sample_project_id,
            "title": self.sample_title,
            "created_at": "2024-01-01T12:00:00",
            "updated_at": "2024-01-01T12:00:00",
            "message_count": "5"
        }
        
        self.sample_chat_create = ChatCreate(
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            title=self.sample_title
        )
    
    def test_create_chat_success(self):
        """Test successful chat creation."""
        # Mock uuid4 to return predictable value
        with patch('src.api.repository.chat_repository.uuid4') as mock_uuid, \
             patch('src.api.repository.chat_repository.datetime') as mock_datetime:
            
            mock_uuid.return_value = self.sample_chat_id
            mock_datetime.now.return_value.timestamp.return_value = 1704110400
            mock_datetime.fromtimestamp.return_value.isoformat.return_value = "2024-01-01T12:00:00"
            
            result = self.repository.create_chat(self.sample_chat_create)
            
            # Verify Redis operations
            self.mock_redis.hset.assert_called_once()
            self.mock_redis.zadd.assert_called()
            
            # Verify result
            self.assertIsInstance(result, Chat)
            self.assertEqual(result.chat_id, self.sample_chat_id)
            self.assertEqual(result.user_id, self.sample_user_id)
            self.assertEqual(result.title, self.sample_title)
    
    def test_get_chat_found(self):
        """Test getting an existing chat."""
        self.mock_redis.hgetall.return_value = self.sample_chat_dict
        
        result = self.repository.get_chat(self.sample_chat_id)
        
        self.mock_redis.hgetall.assert_called_once_with(f"chat:{self.sample_chat_id}")
        self.assertIsInstance(result, Chat)
        self.assertEqual(result.chat_id, self.sample_chat_id)
        self.assertEqual(result.user_id, self.sample_user_id)
    
    def test_get_chat_not_found(self):
        """Test getting a non-existent chat."""
        self.mock_redis.hgetall.return_value = {}
        
        result = self.repository.get_chat("nonexistent_id")
        
        self.assertIsNone(result)
    
    def test_get_all_chats(self):
        """Test getting all chats with pagination."""
        chat_ids = [self.sample_chat_id, "chat2", "chat3"]
        self.mock_redis.zrevrange.return_value = chat_ids
        
        # Mock get_chat to return valid chat for our sample
        with patch.object(self.repository, 'get_chat') as mock_get_chat:
            mock_chat = Chat(
                chat_id=self.sample_chat_id,
                user_id=self.sample_user_id,
                project_id=self.sample_project_id,
                title=self.sample_title,
                created_at=datetime(2024, 1, 1, 12, 0, 0),
                updated_at=datetime(2024, 1, 1, 12, 0, 0),
                message_count=5
            )
            mock_get_chat.side_effect = [mock_chat, mock_chat, mock_chat]
            
            result = self.repository.get_all_chats(skip=0, limit=10)
            
            self.mock_redis.zrevrange.assert_called_once_with("chats", 0, 9)
            self.assertEqual(len(result), 3)
            self.assertEqual(mock_get_chat.call_count, 3)
    
    def test_get_user_chats(self):
        """Test getting chats for a specific user."""
        chat_ids = [self.sample_chat_id]
        self.mock_redis.zrevrange.return_value = chat_ids
        
        with patch.object(self.repository, 'get_chat') as mock_get_chat:
            mock_chat = Chat(
                chat_id=self.sample_chat_id,
                user_id=self.sample_user_id,
                project_id=self.sample_project_id,
                title=self.sample_title,
                created_at=datetime(2024, 1, 1, 12, 0, 0),
                updated_at=datetime(2024, 1, 1, 12, 0, 0),
                message_count=5
            )
            mock_get_chat.return_value = mock_chat
            
            result = self.repository.get_user_chats(self.sample_user_id, skip=0, limit=10)
            
            self.mock_redis.zrevrange.assert_called_once_with(f"user:{self.sample_user_id}:chats", 0, 9)
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0].user_id, self.sample_user_id)
    
    def test_update_chat_success(self):
        """Test successful chat update."""
        self.mock_redis.exists.return_value = True
        self.mock_redis.hgetall.return_value = self.sample_chat_dict.copy()
        
        chat_update = ChatUpdate(title="Updated Title")
        
        with patch('src.api.repository.chat_repository.datetime') as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T13:00:00"
            
            result = self.repository.update_chat(self.sample_chat_id, chat_update)
            
            self.mock_redis.exists.assert_called_once_with(f"chat:{self.sample_chat_id}")
            self.mock_redis.hset.assert_called_once()
            self.assertIsInstance(result, Chat)
            # Note: The exact title check would require more complex mocking of the update process
    
    def test_update_chat_not_found(self):
        """Test updating a non-existent chat."""
        self.mock_redis.exists.return_value = False
        
        chat_update = ChatUpdate(title="Updated Title")
        result = self.repository.update_chat("nonexistent_id", chat_update)
        
        self.assertIsNone(result)
    
    # TODO: Add comprehensive test for delete_chat that properly mocks cross-repository dependencies
    # This requires complex mocking of imports that happen inside the method
    def test_delete_chat_basic_case(self):
        """Test delete_chat returns False for non-existent chat."""
        self.mock_redis.exists.return_value = False
        
        result = self.repository.delete_chat("nonexistent_id")
        
        self.assertFalse(result)
    
    def test_delete_chat_not_found(self):
        """Test deleting a non-existent chat."""
        self.mock_redis.exists.return_value = False
        
        result = self.repository.delete_chat("nonexistent_id")
        
        self.assertFalse(result)
    
    def test_get_chat_count(self):
        """Test getting total chat count."""
        self.mock_redis.zcard.return_value = 10
        
        result = self.repository.get_chat_count()
        
        self.mock_redis.zcard.assert_called_once_with("chats")
        self.assertEqual(result, 10)
    
    def test_get_user_chat_count(self):
        """Test getting user's chat count."""
        self.mock_redis.zcard.return_value = 5
        
        result = self.repository.get_user_chat_count(self.sample_user_id)
        
        self.mock_redis.zcard.assert_called_once_with(f"user:{self.sample_user_id}:chats")
        self.assertEqual(result, 5)
    
    def test_increment_message_count(self):
        """Test incrementing message count."""
        self.mock_redis.exists.return_value = True
        
        with patch('src.api.repository.chat_repository.datetime') as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T13:00:00"
            
            self.repository.increment_message_count(self.sample_chat_id)
            
            self.mock_redis.hincrby.assert_called_once_with(f"chat:{self.sample_chat_id}", "message_count", 1)
            self.mock_redis.hset.assert_called_once()
    
    def test_decrement_message_count(self):
        """Test decrementing message count."""
        self.mock_redis.exists.return_value = True
        self.mock_redis.hget.return_value = "5"
        
        with patch('src.api.repository.chat_repository.datetime') as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T13:00:00"
            
            self.repository.decrement_message_count(self.sample_chat_id)
            
            self.mock_redis.hget.assert_called_once_with(f"chat:{self.sample_chat_id}", "message_count")
            self.mock_redis.hset.assert_called()
    
    def test_decrement_message_count_zero_minimum(self):
        """Test decrementing message count doesn't go below zero."""
        self.mock_redis.exists.return_value = True
        self.mock_redis.hget.return_value = "0"
        
        with patch('src.api.repository.chat_repository.datetime') as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T13:00:00"
            
            self.repository.decrement_message_count(self.sample_chat_id)
            
            # Should set count to 0, not negative
            calls = self.mock_redis.hset.call_args_list
            # Find the call that sets message_count
            message_count_call = None
            for call in calls:
                if len(call[0]) > 2 and call[0][1] == "message_count":
                    message_count_call = call
                    break
            
            if message_count_call:
                self.assertEqual(message_count_call[0][2], "0")
    
    def test_convert_chat_dict(self):
        """Test converting Redis chat dictionary to Chat model format."""
        result = self.repository._convert_chat_dict(self.sample_chat_dict)
        
        self.assertEqual(result["chat_id"], self.sample_chat_id)
        self.assertEqual(result["user_id"], self.sample_user_id)
        self.assertEqual(result["title"], self.sample_title)
        self.assertEqual(result["message_count"], 5)
        self.assertIsInstance(result["created_at"], datetime)
        self.assertIsInstance(result["updated_at"], datetime)


if __name__ == '__main__':
    unittest.main()