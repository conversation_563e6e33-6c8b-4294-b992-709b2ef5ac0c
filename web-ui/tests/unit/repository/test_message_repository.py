import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from uuid import uuid4
import json
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.api.repository.message_repository import MessageRepository
from src.api.models.schemas import Message, MessageCreate, MessageUpdate, MessageRole


class TestMessageRepository(unittest.TestCase):
    """Unit tests for MessageRepository."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_redis = Mock()
        self.repository = MessageRepository()
        self.repository.redis_client = self.mock_redis
        
        # Sample test data
        self.sample_message_id = str(uuid4())
        self.sample_chat_id = str(uuid4())
        self.sample_content = "Test message content"
        self.sample_metadata = {"key": "value", "timestamp": "2024-01-01T12:00:00"}
        
        self.sample_message_dict = {
            "message_id": self.sample_message_id,
            "chat_id": self.sample_chat_id,
            "content": self.sample_content,
            "role": MessageRole.USER.value,
            "metadata": json.dumps(self.sample_metadata),
            "created_at": "2024-01-01T12:00:00",
            "updated_at": "2024-01-01T12:00:00"
        }
        
        self.sample_message_create = MessageCreate(
            chat_id=self.sample_chat_id,
            content=self.sample_content,
            role=MessageRole.USER,
            metadata=self.sample_metadata
        )
    
    def test_create_message_success(self):
        """Test successful message creation."""
        with patch('src.api.repository.message_repository.uuid4') as mock_uuid, \
             patch('src.api.repository.message_repository.datetime') as mock_datetime:
            
            mock_uuid.return_value = self.sample_message_id
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T12:00:00"
            
            # Mock the chat_repository import inside the function
            with patch('src.api.repository.chat_repository.chat_repository') as mock_chat_repo:
                result = self.repository.create_message(self.sample_message_create)
                
                # Verify Redis operations
                self.mock_redis.hset.assert_called_once()
                self.mock_redis.sadd.assert_called()
                
                # Verify chat repository update
                mock_chat_repo.increment_message_count.assert_called_once_with(self.sample_chat_id)
                
                # Verify result
                self.assertIsInstance(result, Message)
                self.assertEqual(result.message_id, self.sample_message_id)
                self.assertEqual(result.chat_id, self.sample_chat_id)
                self.assertEqual(result.content, self.sample_content)
                self.assertEqual(result.role, MessageRole.USER)
    
    def test_create_message_without_metadata(self):
        """Test creating message without metadata."""
        # Create message with explicit None metadata to override default
        message_create_no_meta = MessageCreate(
            chat_id=self.sample_chat_id,
            content=self.sample_content,
            role=MessageRole.USER,
            metadata=None
        )
        
        with patch('src.api.repository.message_repository.uuid4') as mock_uuid, \
             patch('src.api.repository.message_repository.datetime') as mock_datetime:
            
            mock_uuid.return_value = self.sample_message_id
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T12:00:00"
            
            with patch('src.api.repository.chat_repository.chat_repository') as mock_chat_repo:
                result = self.repository.create_message(message_create_no_meta)
                
                # Verify the hset call includes empty metadata
                hset_call = self.mock_redis.hset.call_args
                mapping = hset_call[1]['mapping']
                self.assertEqual(mapping['metadata'], '{}')
                
                self.assertIsInstance(result, Message)
    
    def test_get_message_found(self):
        """Test getting an existing message."""
        self.mock_redis.hgetall.return_value = self.sample_message_dict
        
        result = self.repository.get_message(self.sample_message_id)
        
        self.mock_redis.hgetall.assert_called_once_with(f"message:{self.sample_message_id}")
        self.assertIsInstance(result, Message)
        self.assertEqual(result.message_id, self.sample_message_id)
        self.assertEqual(result.content, self.sample_content)
        self.assertEqual(result.role, MessageRole.USER)
        self.assertEqual(result.metadata, self.sample_metadata)
    
    def test_get_message_not_found(self):
        """Test getting a non-existent message."""
        self.mock_redis.hgetall.return_value = {}
        
        result = self.repository.get_message("nonexistent_id")
        
        self.assertIsNone(result)
    
    def test_get_chat_messages(self):
        """Test getting messages for a chat."""
        message_ids = {self.sample_message_id, "msg2", "msg3"}
        self.mock_redis.smembers.return_value = message_ids
        
        # Mock get_message to return valid messages
        with patch.object(self.repository, 'get_message') as mock_get_message:
            mock_messages = [
                Message(
                    message_id=self.sample_message_id,
                    chat_id=self.sample_chat_id,
                    content="Message 1",
                    role=MessageRole.USER,
                    created_at=datetime(2024, 1, 1, 12, 0, 0),
                    updated_at=datetime(2024, 1, 1, 12, 0, 0)
                ),
                Message(
                    message_id="msg2",
                    chat_id=self.sample_chat_id,
                    content="Message 2",
                    role=MessageRole.ASSISTANT,
                    created_at=datetime(2024, 1, 1, 12, 1, 0),
                    updated_at=datetime(2024, 1, 1, 12, 1, 0)
                ),
                Message(
                    message_id="msg3",
                    chat_id=self.sample_chat_id,
                    content="Message 3",
                    role=MessageRole.USER,
                    created_at=datetime(2024, 1, 1, 12, 2, 0),
                    updated_at=datetime(2024, 1, 1, 12, 2, 0)
                )
            ]
            mock_get_message.side_effect = mock_messages
            
            result = self.repository.get_chat_messages(self.sample_chat_id, skip=0, limit=10)
            
            self.mock_redis.smembers.assert_called_once_with(f"chat:{self.sample_chat_id}:messages")
            self.assertEqual(len(result), 3)
            # Verify messages are sorted by created_at (oldest first)
            self.assertEqual(result[0].created_at, datetime(2024, 1, 1, 12, 0, 0))
            self.assertEqual(result[2].created_at, datetime(2024, 1, 1, 12, 2, 0))
    
    def test_get_chat_messages_with_pagination(self):
        """Test getting messages with skip and limit."""
        message_ids = {f"msg{i}" for i in range(5)}
        self.mock_redis.smembers.return_value = message_ids
        
        with patch.object(self.repository, 'get_message') as mock_get_message:
            mock_messages = [
                Message(
                    message_id=f"msg{i}",
                    chat_id=self.sample_chat_id,
                    content=f"Message {i}",
                    role=MessageRole.USER,
                    created_at=datetime(2024, 1, 1, 12, i, 0),
                    updated_at=datetime(2024, 1, 1, 12, i, 0)
                ) for i in range(5)
            ]
            mock_get_message.side_effect = mock_messages
            
            result = self.repository.get_chat_messages(self.sample_chat_id, skip=1, limit=2)
            
            # Should return 2 messages starting from index 1
            self.assertEqual(len(result), 2)
    
    def test_update_message_success(self):
        """Test successful message update."""
        self.mock_redis.exists.return_value = True
        self.mock_redis.hgetall.return_value = self.sample_message_dict.copy()
        
        message_update = MessageUpdate(content="Updated content")
        
        with patch('src.api.repository.message_repository.datetime') as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T13:00:00"
            
            result = self.repository.update_message(self.sample_message_id, message_update)
            
            self.mock_redis.exists.assert_called_once_with(f"message:{self.sample_message_id}")
            self.mock_redis.hset.assert_called_once()
            self.assertIsInstance(result, Message)
    
    def test_update_message_with_metadata(self):
        """Test updating message with metadata."""
        self.mock_redis.exists.return_value = True
        self.mock_redis.hgetall.return_value = self.sample_message_dict.copy()
        
        new_metadata = {"updated": True, "timestamp": "2024-01-01T13:00:00"}
        message_update = MessageUpdate(metadata=new_metadata)
        
        with patch('src.api.repository.message_repository.datetime') as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T13:00:00"
            
            result = self.repository.update_message(self.sample_message_id, message_update)
            
            # Check that metadata was JSON-encoded in the hset call
            hset_call = self.mock_redis.hset.call_args
            mapping = hset_call[1]['mapping']
            self.assertEqual(mapping['metadata'], json.dumps(new_metadata))
    
    def test_update_message_not_found(self):
        """Test updating a non-existent message."""
        self.mock_redis.exists.return_value = False
        
        message_update = MessageUpdate(content="Updated content")
        result = self.repository.update_message("nonexistent_id", message_update)
        
        self.assertIsNone(result)
    
    def test_delete_message_success(self):
        """Test successful message deletion."""
        self.mock_redis.exists.return_value = True
        
        # Mock get_message to return a valid message
        with patch.object(self.repository, 'get_message') as mock_get_message:
            
            mock_message = Message(
                message_id=self.sample_message_id,
                chat_id=self.sample_chat_id,
                content=self.sample_content,
                role=MessageRole.USER,
                created_at=datetime(2024, 1, 1, 12, 0, 0),
                updated_at=datetime(2024, 1, 1, 12, 0, 0)
            )
            mock_get_message.return_value = mock_message
            
            with patch('src.api.repository.chat_repository.chat_repository') as mock_chat_repo:
                result = self.repository.delete_message(self.sample_message_id)
                
                self.assertTrue(result)
                self.mock_redis.srem.assert_called()
                self.mock_redis.delete.assert_called_once_with(f"message:{self.sample_message_id}")
                mock_chat_repo.decrement_message_count.assert_called_once_with(self.sample_chat_id)
    
    def test_delete_message_not_found(self):
        """Test deleting a non-existent message."""
        self.mock_redis.exists.return_value = False
        
        result = self.repository.delete_message("nonexistent_id")
        
        self.assertFalse(result)
    
    def test_delete_chat_messages(self):
        """Test deleting all messages in a chat."""
        message_ids = {self.sample_message_id, "msg2", "msg3"}
        self.mock_redis.smembers.return_value = message_ids
        self.mock_redis.exists.side_effect = [True, True, True]  # All messages exist
        
        result = self.repository.delete_chat_messages(self.sample_chat_id)
        
        self.mock_redis.smembers.assert_called_once_with(f"chat:{self.sample_chat_id}:messages")
        self.assertEqual(result, 3)  # Should return count of deleted messages
        self.assertEqual(self.mock_redis.delete.call_count, 4)  # 3 messages + 1 chat set
        self.mock_redis.srem.assert_called()
    
    def test_get_chat_message_count(self):
        """Test getting message count for a chat."""
        self.mock_redis.scard.return_value = 5
        
        result = self.repository.get_chat_message_count(self.sample_chat_id)
        
        self.mock_redis.scard.assert_called_once_with(f"chat:{self.sample_chat_id}:messages")
        self.assertEqual(result, 5)
    
    def test_get_total_message_count(self):
        """Test getting total message count across all chats."""
        self.mock_redis.scard.return_value = 100
        
        result = self.repository.get_total_message_count()
        
        self.mock_redis.scard.assert_called_once_with("messages")
        self.assertEqual(result, 100)
    
    def test_convert_message_dict_with_metadata(self):
        """Test converting Redis message dictionary with metadata."""
        result = self.repository._convert_message_dict(self.sample_message_dict)
        
        self.assertEqual(result["message_id"], self.sample_message_id)
        self.assertEqual(result["chat_id"], self.sample_chat_id)
        self.assertEqual(result["content"], self.sample_content)
        self.assertEqual(result["role"], MessageRole.USER)
        self.assertEqual(result["metadata"], self.sample_metadata)
        self.assertIsInstance(result["created_at"], datetime)
        self.assertIsInstance(result["updated_at"], datetime)
    
    def test_convert_message_dict_without_metadata(self):
        """Test converting Redis message dictionary without metadata."""
        message_dict_no_meta = self.sample_message_dict.copy()
        message_dict_no_meta["metadata"] = ""
        
        result = self.repository._convert_message_dict(message_dict_no_meta)
        
        self.assertIsNone(result["metadata"])
    
    def test_convert_message_dict_invalid_metadata(self):
        """Test converting Redis message dictionary with invalid JSON metadata."""
        message_dict_invalid_meta = self.sample_message_dict.copy()
        message_dict_invalid_meta["metadata"] = "invalid json"
        
        result = self.repository._convert_message_dict(message_dict_invalid_meta)
        
        self.assertIsNone(result["metadata"])
    
    def test_convert_message_dict_empty_metadata(self):
        """Test converting Redis message dictionary with empty metadata."""
        message_dict_empty_meta = self.sample_message_dict.copy()
        message_dict_empty_meta["metadata"] = "{}"
        
        result = self.repository._convert_message_dict(message_dict_empty_meta)
        
        self.assertIsNone(result["metadata"])


if __name__ == '__main__':
    unittest.main()