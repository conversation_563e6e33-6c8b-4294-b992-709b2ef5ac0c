import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from uuid import uuid4
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.api.repository.task_repository import TaskRepository
from src.api.models.schemas import Task, TaskCreate, TaskUpdate, TaskStatusEnum, TaskPriority


class TestTaskRepository(unittest.TestCase):
    """Unit tests for TaskRepository."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_redis = Mock()
        self.repository = TaskRepository()
        self.repository.redis_client = self.mock_redis
        
        # Sample test data
        self.sample_task_id = str(uuid4())
        self.sample_chat_id = str(uuid4())
        self.sample_user_id = "test_user_123"
        self.sample_project_id = "test_project_456"
        self.sample_title = "Test Task"
        self.sample_description = "Test task description"
        
        self.sample_task_dict = {
            "id": self.sample_task_id,
            "title": self.sample_title,
            "description": self.sample_description,
            "priority": TaskPriority.MEDIUM.value,
            "status": TaskStatusEnum.PENDING.value,
            "created_at": "2024-01-01T12:00:00",
            "updated_at": "2024-01-01T12:00:00",
            "user_id": self.sample_user_id,
            "project_id": self.sample_project_id,
            "chat_id": self.sample_chat_id
        }
        
        self.sample_task_create = TaskCreate(
            title=self.sample_title,
            description=self.sample_description,
            priority=TaskPriority.MEDIUM,
            user_id=self.sample_user_id,
            project_id=self.sample_project_id,
            chat_id=self.sample_chat_id
        )
    
    def test_create_task_success(self):
        """Test successful task creation."""
        with patch('src.api.repository.task_repository.uuid4') as mock_uuid, \
             patch('src.api.repository.task_repository.datetime') as mock_datetime:
            
            mock_uuid.return_value = self.sample_task_id
            mock_datetime.now.return_value.timestamp.return_value = 1704110400
            mock_datetime.fromtimestamp.return_value.isoformat.return_value = "2024-01-01T12:00:00"
            
            # Mock add_task_to_chat method
            with patch.object(self.repository, 'add_task_to_chat') as mock_add_to_chat:
                result = self.repository.create_task(self.sample_task_create)
                
                # Verify Redis operations
                self.mock_redis.hset.assert_called_once()
                self.mock_redis.zadd.assert_called()
                mock_add_to_chat.assert_called_once()
                
                # Verify result
                self.assertIsInstance(result, Task)
                self.assertEqual(result.id, self.sample_task_id)
                self.assertEqual(result.title, self.sample_title)
                self.assertEqual(result.priority, TaskPriority.MEDIUM)
                self.assertEqual(result.status, TaskStatusEnum.PENDING)
    
    def test_create_task_without_chat_id(self):
        """Test creating task without chat_id."""
        task_create_no_chat = TaskCreate(
            title=self.sample_title,
            description=self.sample_description,
            priority=TaskPriority.HIGH
        )
        
        with patch('src.api.repository.task_repository.uuid4') as mock_uuid, \
             patch('src.api.repository.task_repository.datetime') as mock_datetime:
            
            # First uuid4 call for task_id, second for chat_id
            mock_uuid.side_effect = [self.sample_task_id, self.sample_chat_id]
            mock_datetime.now.return_value.timestamp.return_value = 1704110400
            mock_datetime.fromtimestamp.return_value.isoformat.return_value = "2024-01-01T12:00:00"
            
            with patch.object(self.repository, 'add_task_to_chat') as mock_add_to_chat:
                result = self.repository.create_task(task_create_no_chat)
                
                self.assertIsInstance(result, Task)
                # Should have generated a chat_id
                mock_add_to_chat.assert_called_once()
    
    def test_get_task_found(self):
        """Test getting an existing task."""
        self.mock_redis.hgetall.return_value = self.sample_task_dict
        
        result = self.repository.get_task(self.sample_task_id)
        
        self.mock_redis.hgetall.assert_called_once_with(f"task:{self.sample_task_id}")
        self.assertIsInstance(result, Task)
        self.assertEqual(result.id, self.sample_task_id)
        self.assertEqual(result.title, self.sample_title)
        self.assertEqual(result.priority, TaskPriority.MEDIUM)
        self.assertEqual(result.status, TaskStatusEnum.PENDING)
    
    def test_get_task_not_found(self):
        """Test getting a non-existent task."""
        self.mock_redis.hgetall.return_value = {}
        
        result = self.repository.get_task("nonexistent_id")
        
        self.assertIsNone(result)
    
    def test_get_all_tasks(self):
        """Test getting all tasks with pagination."""
        task_ids = [self.sample_task_id, "task2", "task3"]
        self.mock_redis.zrevrange.return_value = task_ids
        
        # Mock get_task to return valid tasks
        with patch.object(self.repository, 'get_task') as mock_get_task:
            mock_task = Task(
                id=self.sample_task_id,
                title=self.sample_title,
                description=self.sample_description,
                priority=TaskPriority.MEDIUM,
                status=TaskStatusEnum.PENDING,
                created_at=datetime(2024, 1, 1, 12, 0, 0),
                updated_at=datetime(2024, 1, 1, 12, 0, 0),
                user_id=self.sample_user_id,
                project_id=self.sample_project_id,
                chat_id=self.sample_chat_id
            )
            mock_get_task.side_effect = [mock_task, mock_task, mock_task]
            
            result = self.repository.get_all_tasks(skip=0, limit=10)
            
            self.mock_redis.zrevrange.assert_called_once_with("tasks", 0, 9)
            self.assertEqual(len(result), 3)
            self.assertEqual(mock_get_task.call_count, 3)
    
    def test_get_all_tasks_empty_result(self):
        """Test getting all tasks when Redis returns non-list."""
        self.mock_redis.zrevrange.return_value = "not_a_list"
        
        result = self.repository.get_all_tasks()
        
        self.assertEqual(result, [])
    
    def test_update_task_success(self):
        """Test successful task update."""
        self.mock_redis.exists.return_value = True
        self.mock_redis.hgetall.return_value = self.sample_task_dict.copy()
        
        task_update = TaskUpdate(title="Updated Title", status=TaskStatusEnum.RUNNING)
        
        with patch('src.api.repository.task_repository.datetime') as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T13:00:00"
            
            result = self.repository.update_task(self.sample_task_id, task_update)
            
            self.mock_redis.exists.assert_called_once_with(f"task:{self.sample_task_id}")
            self.mock_redis.hset.assert_called_once()
            self.assertIsInstance(result, Task)
    
    def test_update_task_not_found(self):
        """Test updating a non-existent task."""
        self.mock_redis.exists.return_value = False
        
        task_update = TaskUpdate(title="Updated Title")
        result = self.repository.update_task("nonexistent_id", task_update)
        
        self.assertIsNone(result)
    
    def test_delete_task_success(self):
        """Test successful task deletion."""
        self.mock_redis.exists.return_value = True
        
        # Mock get_task to return a valid task
        with patch.object(self.repository, 'get_task') as mock_get_task:
            mock_task = Task(
                id=self.sample_task_id,
                title=self.sample_title,
                description=self.sample_description,
                priority=TaskPriority.MEDIUM,
                status=TaskStatusEnum.PENDING,
                created_at=datetime(2024, 1, 1, 12, 0, 0),
                updated_at=datetime(2024, 1, 1, 12, 0, 0),
                user_id=self.sample_user_id,
                project_id=self.sample_project_id,
                chat_id=self.sample_chat_id
            )
            mock_get_task.return_value = mock_task
            
            result = self.repository.delete_task(self.sample_task_id)
            
            self.assertTrue(result)
            self.mock_redis.delete.assert_called_once_with(f"task:{self.sample_task_id}")
            self.mock_redis.srem.assert_called()
    
    def test_delete_task_not_found(self):
        """Test deleting a non-existent task."""
        self.mock_redis.exists.return_value = False
        
        result = self.repository.delete_task("nonexistent_id")
        
        self.assertFalse(result)
    
    def test_get_task_count(self):
        """Test getting total task count."""
        self.mock_redis.scard.return_value = 15
        
        result = self.repository.get_task_count()
        
        self.mock_redis.scard.assert_called_once_with("tasks")
        self.assertEqual(result, 15)
    
    def test_get_tasks_by_status(self):
        """Test getting tasks filtered by status."""
        task_ids = [self.sample_task_id, "task2", "task3"]
        self.mock_redis.zrevrange.return_value = task_ids
        
        with patch.object(self.repository, 'get_task') as mock_get_task:
            # Create tasks with different statuses
            tasks = [
                Task(
                    id=self.sample_task_id,
                    title="Task 1",
                    description="",
                    priority=TaskPriority.MEDIUM,
                    status=TaskStatusEnum.PENDING,
                    created_at=datetime(2024, 1, 1, 12, 0, 0),
                    updated_at=datetime(2024, 1, 1, 12, 0, 0)
                ),
                Task(
                    id="task2",
                    title="Task 2",
                    description="",
                    priority=TaskPriority.MEDIUM,
                    status=TaskStatusEnum.RUNNING,
                    created_at=datetime(2024, 1, 1, 12, 1, 0),
                    updated_at=datetime(2024, 1, 1, 12, 1, 0)
                ),
                Task(
                    id="task3",
                    title="Task 3",
                    description="",
                    priority=TaskPriority.MEDIUM,
                    status=TaskStatusEnum.PENDING,
                    created_at=datetime(2024, 1, 1, 12, 2, 0),
                    updated_at=datetime(2024, 1, 1, 12, 2, 0)
                )
            ]
            mock_get_task.side_effect = tasks
            
            result = self.repository.get_tasks_by_status(TaskStatusEnum.PENDING)
            
            # Should return only the 2 PENDING tasks
            self.assertEqual(len(result), 2)
            self.assertTrue(all(task.status == TaskStatusEnum.PENDING for task in result))
    
    def test_get_tasks_by_status_empty_result(self):
        """Test getting tasks by status when Redis returns non-list."""
        self.mock_redis.zrevrange.return_value = "not_a_list"
        
        result = self.repository.get_tasks_by_status(TaskStatusEnum.PENDING)
        
        self.assertEqual(result, [])
    
    def test_get_chat_tasks(self):
        """Test getting tasks for a specific chat."""
        task_ids = [self.sample_task_id, "task2"]
        self.mock_redis.zrevrange.return_value = task_ids
        
        with patch.object(self.repository, 'get_task') as mock_get_task:
            mock_task = Task(
                id=self.sample_task_id,
                title=self.sample_title,
                description=self.sample_description,
                priority=TaskPriority.MEDIUM,
                status=TaskStatusEnum.PENDING,
                created_at=datetime(2024, 1, 1, 12, 0, 0),
                updated_at=datetime(2024, 1, 1, 12, 0, 0),
                chat_id=self.sample_chat_id
            )
            mock_get_task.side_effect = [mock_task, mock_task]
            
            result = self.repository.get_chat_tasks(self.sample_chat_id)
            
            self.mock_redis.zrevrange.assert_called_once_with(f"chat:{self.sample_chat_id}:tasks", 0, -1)
            self.assertEqual(len(result), 2)
    
    def test_get_chat_tasks_empty_result(self):
        """Test getting chat tasks when Redis returns non-list."""
        self.mock_redis.zrevrange.return_value = "not_a_list"
        
        result = self.repository.get_chat_tasks(self.sample_chat_id)
        
        self.assertEqual(result, [])
    
    def test_add_task_to_chat(self):
        """Test adding a task to a chat."""
        timestamp = 1704110400
        
        self.repository.add_task_to_chat(self.sample_chat_id, self.sample_task_id, timestamp)
        
        self.mock_redis.zadd.assert_called_once_with(
            f"chat:{self.sample_chat_id}:tasks", 
            {self.sample_task_id: timestamp}
        )
    
    def test_add_task_to_chat_without_timestamp(self):
        """Test adding a task to a chat without explicit timestamp."""
        with patch('src.api.repository.task_repository.datetime') as mock_datetime:
            mock_datetime.now.return_value.timestamp.return_value = 1704110400
            
            self.repository.add_task_to_chat(self.sample_chat_id, self.sample_task_id)
            
            self.mock_redis.zadd.assert_called_once_with(
                f"chat:{self.sample_chat_id}:tasks", 
                {self.sample_task_id: 1704110400}
            )
    
    def test_convert_task_dict_complete(self):
        """Test converting complete Redis task dictionary."""
        result = self.repository._convert_task_dict(self.sample_task_dict)
        
        self.assertEqual(result["id"], self.sample_task_id)
        self.assertEqual(result["title"], self.sample_title)
        self.assertEqual(result["description"], self.sample_description)
        self.assertEqual(result["priority"], TaskPriority.MEDIUM)
        self.assertEqual(result["status"], TaskStatusEnum.PENDING)
        self.assertEqual(result["user_id"], self.sample_user_id)
        self.assertEqual(result["project_id"], self.sample_project_id)
        self.assertEqual(result["chat_id"], self.sample_chat_id)
        self.assertIsInstance(result["created_at"], datetime)
        self.assertIsInstance(result["updated_at"], datetime)
    
    def test_convert_task_dict_minimal(self):
        """Test converting minimal Redis task dictionary."""
        minimal_dict = {
            "id": self.sample_task_id,
            "title": self.sample_title,
            "status": TaskStatusEnum.PENDING.value,
            "created_at": "2024-01-01T12:00:00",
            "updated_at": "2024-01-01T12:00:00"
        }
        
        result = self.repository._convert_task_dict(minimal_dict)
        
        self.assertEqual(result["id"], self.sample_task_id)
        self.assertEqual(result["title"], self.sample_title)
        self.assertEqual(result["description"], "")  # Default empty string
        self.assertEqual(result["priority"], TaskPriority.MEDIUM)  # Default medium priority
        self.assertEqual(result["status"], TaskStatusEnum.PENDING)
        self.assertIsNone(result["user_id"])  # Should be None for missing fields
        self.assertIsNone(result["project_id"])
        self.assertIsNone(result["chat_id"])
    
    def test_convert_task_dict_missing_priority(self):
        """Test converting task dict with missing priority."""
        dict_no_priority = self.sample_task_dict.copy()
        del dict_no_priority["priority"]
        
        result = self.repository._convert_task_dict(dict_no_priority)
        
        self.assertEqual(result["priority"], TaskPriority.MEDIUM)  # Should default to MEDIUM
    
    def test_convert_task_dict_empty_priority(self):
        """Test converting task dict with empty priority."""
        dict_empty_priority = self.sample_task_dict.copy()
        dict_empty_priority["priority"] = ""
        
        result = self.repository._convert_task_dict(dict_empty_priority)
        
        self.assertEqual(result["priority"], TaskPriority.MEDIUM)  # Should default to MEDIUM


if __name__ == '__main__':
    unittest.main()