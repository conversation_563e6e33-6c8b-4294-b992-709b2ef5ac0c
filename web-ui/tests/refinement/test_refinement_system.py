#!/usr/bin/env python3
"""
Comprehensive test suite for the prompt refinement system.
"""

import sys
import asyncio
import requests
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.prompt_refinement.refinement_manager import RefinementManager


class TestRefinementSystem:
    """Test suite for the refinement system."""
    
    def __init__(self):
        self.manager = RefinementManager()
        self.base_url = "http://127.0.0.1:8001"
    
    def test_prompt_analysis(self):
        """Test prompt analysis functionality."""
        print("\n" + "="*60)
        print("TESTING PROMPT ANALYSIS")
        print("="*60)
        
        test_cases = [
            {
                "prompt": "test the system",
                "expected_refinement": True,
                "description": "Very vague prompt"
            },
            {
                "prompt": "check login functionality",
                "expected_refinement": True,
                "description": "Somewhat vague prompt"
            },
            {
                "prompt": "Navigate to the login page at /login, enter username '<EMAIL>' and password 'password123', click the login button, and verify that the dashboard page loads with the user's name displayed in the header.",
                "expected_refinement": False,
                "description": "Detailed prompt"
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\nTest Case {i}: {case['description']}")
            print(f"Prompt: '{case['prompt']}'")
            
            result = self.manager.start_refinement_session(case['prompt'])
            needs_refinement = result.get("needs_refinement", True)
            
            print(f"Expected refinement: {case['expected_refinement']}")
            print(f"Actual refinement needed: {needs_refinement}")
            
            if needs_refinement == case['expected_refinement']:
                print("✅ PASS")
            else:
                print("❌ FAIL")
            
            if needs_refinement:
                questions = result.get("clarifying_questions", [])
                print(f"Generated {len(questions)} questions:")
                for j, q in enumerate(questions, 1):
                    print(f"  {j}. {q}")
    
    def test_refinement_workflow(self):
        """Test the complete refinement workflow."""
        print("\n" + "="*60)
        print("TESTING REFINEMENT WORKFLOW")
        print("="*60)
        
        # Start with vague prompt
        prompt = "test the login"
        print(f"Starting with prompt: '{prompt}'")
        
        result = self.manager.start_refinement_session(prompt)
        
        if result.get("needs_refinement"):
            session_id = result["session_id"]
            questions = result["clarifying_questions"]
            
            print(f"Session ID: {session_id}")
            print("Questions generated:")
            for i, q in enumerate(questions, 1):
                print(f"  {i}. {q}")
            
            # Provide sample answers
            answers = [
                "The main login page at /auth/login",
                "Enter valid credentials and click the submit button",
                "Verify that the user dashboard loads with welcome message"
            ]
            
            print("\nProviding answers:")
            for i, a in enumerate(answers[:len(questions)], 1):
                print(f"  {i}. {a}")
            
            # Process answers
            answer_result = self.manager.provide_answers(session_id, answers[:len(questions)])
            
            print(f"\nRefinement completed: {answer_result.get('state') == 'completed'}")
            
            if answer_result.get("refined_prompt"):
                print(f"Refined prompt: {answer_result['refined_prompt']}")
                print("✅ Workflow completed successfully")
            else:
                print("❌ Workflow failed")
        else:
            print("❌ Expected refinement but none was needed")
    
    def test_api_endpoints(self):
        """Test API endpoints."""
        print("\n" + "="*60)
        print("TESTING API ENDPOINTS")
        print("="*60)
        
        # Test refinement start endpoint
        print("1. Testing /refinement/start endpoint...")
        
        start_payload = {"prompt": "test the platform"}
        
        try:
            response = requests.post(f"{self.base_url}/refinement/start", json=start_payload)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Start endpoint working - Session: {result.get('session_id')}")
                
                if result.get("needs_refinement"):
                    session_id = result["session_id"]
                    questions = result["clarifying_questions"]
                    
                    # Test answers endpoint
                    print("2. Testing /refinement/{session_id}/answers endpoint...")
                    
                    answers_payload = {
                        "session_id": session_id,
                        "answers": [
                            "The main application dashboard",
                            "Click buttons and verify functionality",
                            "Check that all features work correctly"
                        ]
                    }
                    
                    response = requests.post(
                        f"{self.base_url}/refinement/{session_id}/answers",
                        json=answers_payload
                    )
                    
                    if response.status_code == 200:
                        print("✅ Answers endpoint working")
                        
                        # Test status endpoint
                        print("3. Testing /refinement/{session_id}/status endpoint...")
                        
                        response = requests.get(f"{self.base_url}/refinement/{session_id}/status")
                        
                        if response.status_code == 200:
                            print("✅ Status endpoint working")
                        else:
                            print(f"❌ Status endpoint failed: {response.status_code}")
                    else:
                        print(f"❌ Answers endpoint failed: {response.status_code}")
                else:
                    print("❌ Expected refinement but none was needed")
            else:
                print(f"❌ Start endpoint failed: {response.status_code}")
        
        except requests.exceptions.ConnectionError:
            print("❌ API server not running - skipping API tests")
        except Exception as e:
            print(f"❌ API test error: {e}")
    
    def test_task_integration(self):
        """Test integration with task creation."""
        print("\n" + "="*60)
        print("TESTING TASK INTEGRATION")
        print("="*60)
        
        try:
            # Test task creation with vague prompt
            print("1. Testing task creation with vague prompt...")
            
            task_payload = {
                "task": "test the system",
                "skip_refinement": False
            }
            
            response = requests.post(f"{self.base_url}/tasks", json=task_payload)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("status") == "needs_refinement":
                    print("✅ Task creation correctly triggered refinement")
                    print(f"Refinement session: {result.get('refinement_session_id')}")
                    print(f"Questions: {len(result.get('clarifying_questions', []))}")
                else:
                    print(f"❌ Expected refinement trigger, got status: {result.get('status')}")
            else:
                print(f"❌ Task creation failed: {response.status_code}")
            
            # Test task creation with detailed prompt
            print("2. Testing task creation with detailed prompt...")
            
            detailed_task_payload = {
                "task": "Navigate to the login page at /login, enter username '<EMAIL>' and password 'testpass', click login button, and verify dashboard loads with user profile visible.",
                "skip_refinement": False
            }
            
            response = requests.post(f"{self.base_url}/tasks", json=detailed_task_payload)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("task_id"):
                    print("✅ Detailed prompt created task directly")
                    print(f"Task ID: {result.get('task_id')}")
                else:
                    print("❌ Expected direct task creation")
            else:
                print(f"❌ Detailed task creation failed: {response.status_code}")
        
        except requests.exceptions.ConnectionError:
            print("❌ API server not running - skipping integration tests")
        except Exception as e:
            print(f"❌ Integration test error: {e}")
    
    def run_all_tests(self):
        """Run all tests."""
        print("🧪 STARTING COMPREHENSIVE REFINEMENT SYSTEM TESTS")
        print("="*80)
        
        self.test_prompt_analysis()
        self.test_refinement_workflow()
        self.test_api_endpoints()
        self.test_task_integration()
        
        print("\n" + "="*80)
        print("🎉 ALL TESTS COMPLETED")
        print("="*80)


if __name__ == "__main__":
    test_suite = TestRefinementSystem()
    test_suite.run_all_tests()
