#!/usr/bin/env python3
"""
Example usage of DrCode UI Testing API

This script demonstrates how to use the DrCode API for browser automation tasks.
It shows both the low-level API client and the convenient quick_task function.
"""

import asyncio
import os
from src.api.client import Dr<PERSON>odeAP<PERSON>lient, DrCodeWebSocketClient, quick_task
from src.api.models import AgentSettings, BrowserSettings


async def example_basic_task():
    """Example: Basic task execution."""
    print("=== Basic Task Example ===")
    
    # Quick way to run a task
    result = await quick_task(
        task="Go to google.com and search for 'DrCode UI testing'",
        llm_provider="openai",
        llm_model="gpt-4.1-mini",
        headless=True,
        timeout=300
    )
    
    print(f"Task completed with status: {result.status}")
    print(f"Duration: {result.duration}s")
    print(f"Tokens used: {result.tokens_used}")
    
    return result


async def example_advanced_task():
    """Example: Advanced task with custom settings."""
    print("\n=== Advanced Task Example ===")
    
    # Custom agent settings
    agent_settings = AgentSettings(
        llm_provider="openai",
        llm_model_name="gpt-4.1-mini",
        llm_temperature=0.3,
        max_steps=50,
        use_vision=True,
        extend_system_prompt="You are a QA testing expert. Focus on thorough testing."
    )
    
    # Custom browser settings
    browser_settings = BrowserSettings(
        headless=False,  # Show browser for debugging
        window_w=1920,
        window_h=1080,
        save_recording_path="./recordings",
        save_agent_history_path="./history"
    )
    
    async with DrCodeAPIClient() as client:
        # Create task
        task_response = await client.create_task(
            task="Test the login functionality on example.com",
            agent_settings=agent_settings,
            browser_settings=browser_settings
        )
        
        print(f"Created task: {task_response.task_id}")
        
        # Wait for completion
        final_status = await client.wait_for_task_completion(
            task_response.task_id, 
            timeout=600
        )
        
        print(f"Task completed: {final_status.status}")
        
        # Download results
        if final_status.status == "completed":
            history_path = await client.download_task_history(task_response.task_id)
            gif_path = await client.download_task_gif(task_response.task_id)
            
            print(f"History saved to: {history_path}")
            print(f"Recording saved to: {gif_path}")
        
        return final_status


async def example_task_monitoring():
    """Example: Real-time task monitoring with WebSocket."""
    print("\n=== Task Monitoring Example ===")
    
    # Start a long-running task
    async with DrCodeAPIClient() as client:
        task_response = await client.create_task(
            task="Navigate through multiple pages and test forms",
            agent_settings=AgentSettings(llm_provider="openai", llm_model_name="gpt-4.1-mini"),
            browser_settings=BrowserSettings(headless=True)
        )
        
        task_id = task_response.task_id
        print(f"Started task: {task_id}")
        
        # Set up WebSocket monitoring
        ws_client = DrCodeWebSocketClient()
        await ws_client.connect(task_id)
        
        # Message handlers
        async def on_status_update(data):
            print(f"Status: {data['data']['status']}")
        
        async def on_chat_message(data):
            role = data['data']['role']
            content = data['data']['content'][:100]  # Truncate for display
            print(f"Chat [{role}]: {content}...")
        
        async def on_error(data):
            print(f"Error: {data['data']['error']}")
        
        # Register handlers
        ws_client.on_message("status_update", on_status_update)
        ws_client.on_message("chat_message", on_chat_message)
        ws_client.on_message("error", on_error)
        
        # Monitor task
        monitor_task = asyncio.create_task(ws_client.listen())
        
        # Wait for completion or timeout
        try:
            final_status = await client.wait_for_task_completion(task_id, timeout=300)
            print(f"Task finished: {final_status.status}")
        except TimeoutError:
            print("Task timed out")
            await client.stop_task(task_id)
        finally:
            monitor_task.cancel()
            await ws_client.disconnect()


async def example_configuration_management():
    """Example: Save and load configurations."""
    print("\n=== Configuration Management Example ===")
    
    async with DrCodeAPIClient() as client:
        # Create custom settings
        agent_settings = AgentSettings(
            llm_provider="openai",
            llm_model_name="gpt-4.1-mini",
            llm_temperature=0.2,
            max_steps=100,
            extend_system_prompt="Focus on accessibility testing"
        )
        
        browser_settings = BrowserSettings(
            headless=True,
            window_w=1024,
            window_h=768
        )
        
        # Save configuration
        config_response = await client.save_configuration(
            config_name="accessibility_testing",
            agent_settings=agent_settings,
            browser_settings=browser_settings
        )
        
        print(f"Saved config: {config_response['config_name']}")
        print(f"Config path: {config_response['config_path']}")
        
        # Load configuration (in a new session)
        await client.load_configuration(config_response['config_path'])
        print("Configuration loaded successfully")


async def example_system_info():
    """Example: Get system information."""
    print("\n=== System Information Example ===")
    
    async with DrCodeAPIClient() as client:
        # Health check
        health = await client.health_check()
        print(f"API Health: {health['status']}")
        
        # System info
        info = await client.get_system_info()
        print(f"Active tasks: {info['active_tasks_count']}")
        print(f"QA Mode: {info['default_qa_mode']}")
        print(f"OpenAI API: {'✓' if info['environment']['openai_api_key_set'] else '✗'}")
        
        # Available providers
        providers = await client.get_providers()
        print(f"Available providers: {', '.join(providers['providers'])}")
        
        # Available models
        models = await client.get_available_models()
        for provider_info in models[:3]:  # Show first 3 providers
            print(f"{provider_info['provider']}: {len(provider_info['models'])} models")


async def example_user_assistance():
    """Example: Handle tasks that require user assistance."""
    print("\n=== User Assistance Example ===")
    
    async with DrCodeAPIClient() as client:
        # Create a task that might need assistance
        task_response = await client.create_task(
            task="Complete a CAPTCHA on a website",
            agent_settings=AgentSettings(llm_provider="openai", llm_model_name="gpt-4.1-mini")
        )
        
        task_id = task_response.task_id
        print(f"Started task: {task_id}")
        
        # Monitor for assistance requests
        while True:
            status = await client.get_task_status(task_id)
            
            if status.status in ["completed", "error", "stopped"]:
                print(f"Task finished: {status.status}")
                break
            
            # Check chat history for assistance requests
            if status.chat_history:
                last_message = status.chat_history[-1]
                if "Need Help" in last_message.get("content", ""):
                    print("Agent needs assistance!")
                    print(f"Request: {last_message['content']}")
                    
                    # Provide assistance
                    response = "I have manually completed the CAPTCHA. Please continue."
                    await client.provide_assistance(task_id, response)
                    print("Assistance provided")
            
            await asyncio.sleep(2)


async def main():
    """Run all examples."""
    print("DrCode UI Testing API Examples")
    print("=" * 40)
    
    # Check if API server is running
    try:
        async with DrCodeAPIClient() as client:
            await client.health_check()
        print("✓ API server is running")
    except Exception as e:
        print(f"✗ API server not available: {e}")
        print("Please start the API server first: python api_server.py")
        return
    
    # Run examples
    examples = [
        example_system_info,
        example_basic_task,
        example_configuration_management,
        # example_advanced_task,  # Uncomment for non-headless testing
        # example_task_monitoring,  # Uncomment for WebSocket example
        # example_user_assistance,  # Uncomment for assistance example
    ]
    
    for example in examples:
        try:
            await example()
            await asyncio.sleep(1)  # Brief pause between examples
        except Exception as e:
            print(f"Example failed: {e}")
            continue
    
    print("\n" + "=" * 40)
    print("Examples completed!")


if __name__ == "__main__":
    # Set up environment
    if not os.getenv("OPENAI_API_KEY"):
        print("Warning: OPENAI_API_KEY not set. Some features may not work.")
    
    # Run examples
    asyncio.run(main())
