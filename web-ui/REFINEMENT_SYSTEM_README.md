# Prompt Refinement System

This document describes the new prompt refinement system that enhances the UI testing platform with intelligent prompt analysis and human-agent loop functionality.

## Overview

The prompt refinement system automatically analyzes user prompts to determine if they contain sufficient detail for browser automation. When prompts are too vague, the system:

1. **Analyzes** the prompt against a vector database of platform documentation
2. **Generates** relevant clarifying questions
3. **Refines** the prompt based on user answers
4. **Iterates** until the prompt is sufficiently detailed
5. **Executes** the refined prompt with browser automation

## Architecture

### Components

1. **Vector Database (Pinecone + Gemini)**
   - Stores Mindler Partner Platform documentation
   - Uses Google's Gemini embedding-001 model
   - Provides context for generating relevant questions

2. **Prompt Analyzer**
   - Calculates prompt specificity scores
   - Identifies vague indicators
   - Determines if refinement is needed

3. **Refinement Manager**
   - Manages refinement sessions
   - Orchestrates the human-agent loop
   - Tracks iteration history

4. **API Endpoints**
   - Integrates refinement into existing task creation
   - Provides dedicated refinement endpoints
   - Supports Swagger UI testing

## API Endpoints

### Task Creation with Refinement

**POST /tasks**

Creates a new task with optional prompt refinement.

```json
{
  "task": "test the login",
  "skip_refinement": false,
  "refinement_session_id": null,
  "agent_settings": {...},
  "browser_settings": {...}
}
```

**Response (if refinement needed):**
```json
{
  "task_id": null,
  "status": "needs_refinement",
  "message": "Task needs refinement. Please answer the clarifying questions.",
  "refinement_session_id": "uuid-here",
  "clarifying_questions": [
    "Which specific login page should be tested?",
    "What credentials should be used?",
    "What should be verified after login?"
  ]
}
```

### Refinement Endpoints

#### Start Refinement
**POST /refinement/start**

```json
{
  "prompt": "test the platform"
}
```

#### Provide Answers
**POST /refinement/{session_id}/answers**

```json
{
  "session_id": "uuid-here",
  "answers": [
    "The partner login page at /partner/login",
    "Valid partner credentials",
    "Dashboard loads with partner name"
  ]
}
```

#### Get Session Status
**GET /refinement/{session_id}/status**

#### List Active Sessions
**GET /refinement/sessions**

## Usage Examples

### Example 1: Vague Prompt Refinement

**Initial prompt:** "test the platform"

**Generated questions:**
1. Which specific assessments or features should be tested?
2. What exact actions should be performed?
3. What are the expected outcomes?

**User answers:**
1. The login functionality
2. Enter credentials and click login button
3. Verify dashboard loads with partner name

**Refined prompt:**
```
Navigate to the Mindler Partner Platform login page at /partner/login.
1. Locate the username input field and enter valid partner credentials
2. Click the "Login" button to submit the credentials
3. Verify that the dashboard loads successfully by checking for the partner's name
4. Confirm that the navigation menu is visible with expected options
```

### Example 2: Already Detailed Prompt

**Prompt:** "Navigate to the Mindler Partner Platform login page at /partner/login, enter valid credentials (username: <EMAIL>, password: testpass123), click the login button, and verify successful authentication by checking that the dashboard page loads with the partner's name displayed in the header."

**Result:** No refinement needed - proceeds directly to task creation.

## Configuration

### Environment Variables

```bash
# Vector Database
PINECONE_API_KEY=your_pinecone_api_key
GOOGLE_API_KEY=your_google_api_key

# LLM for Question Generation
OPENAI_API_KEY=your_openai_api_key
```

### Refinement Settings

- **Specificity Threshold:** 0.6 (prompts below this score need refinement)
- **Similarity Threshold:** 0.6 (minimum similarity for relevant context)
- **Max Iterations:** 3 (maximum refinement rounds per session)
- **Session Timeout:** 24 hours

## Testing

### Manual Testing via Swagger UI

1. Start the API server: `python api_server.py`
2. Open Swagger UI: http://localhost:8000/docs
3. Test the refinement endpoints:
   - POST /refinement/start
   - POST /refinement/{session_id}/answers
   - GET /refinement/{session_id}/status

### Automated Testing

Run the test scripts:

```bash
# Test refinement system
python test_refinement.py

# Test API endpoints
python test_refinement_api.py

# Test complete workflow
python test_full_workflow.py
```

## Vector Database Setup

The system uses a Pinecone vector database with the Mindler Partner Platform documentation:

```bash
# Setup vector database
python -m src.vector_db.setup_vector_db --action setup

# Verify setup
python -m src.vector_db.setup_vector_db --action verify

# Reset database
python -m src.vector_db.setup_vector_db --action reset
```

## Benefits

1. **Improved Task Quality:** Vague prompts are automatically refined into detailed, actionable instructions
2. **Context-Aware Questions:** Questions are generated based on actual platform documentation
3. **Iterative Refinement:** Multiple rounds of clarification ensure comprehensive task descriptions
4. **Seamless Integration:** Works transparently with existing task creation workflow
5. **User Control:** Users can skip refinement or provide existing refinement sessions

## Troubleshooting

### Common Issues

1. **Vector database connection errors:** Check Pinecone API key and network connectivity
2. **Embedding generation failures:** Verify Google API key and quota
3. **Question generation issues:** Ensure OpenAI API key is valid
4. **Session not found:** Check session ID and ensure session hasn't expired

### Logs

Enable debug logging to troubleshoot issues:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

1. **Multi-language Support:** Support for prompts in different languages
2. **Custom Question Templates:** Allow customization of question generation
3. **Learning from History:** Improve questions based on successful refinements
4. **Integration with More LLMs:** Support for additional language models
5. **Advanced Analytics:** Track refinement success rates and patterns
