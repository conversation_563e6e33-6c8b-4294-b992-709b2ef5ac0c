#!/usr/bin/env python3
"""
Test script for the prompt refinement system.
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.prompt_refinement.refinement_manager import RefinementManager


async def test_refinement():
    """Test the refinement system with sample prompts."""
    
    manager = RefinementManager()
    
    # Test cases
    test_prompts = [
        "test the platform",  # Very vague
        "check login functionality",  # Somewhat vague
        "Navigate to the Mindler Partner Platform login page, enter valid credentials, and verify successful authentication by checking for the dashboard page",  # Already detailed
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n{'='*60}")
        print(f"TEST {i}: {prompt}")
        print('='*60)
        
        # Start refinement
        result = manager.start_refinement_session(prompt)
        print(f"Initial analysis: {result}")
        
        if result.get("needs_refinement"):
            session_id = result["session_id"]
            questions = result["clarifying_questions"]
            
            print(f"\nClarifying questions:")
            for j, question in enumerate(questions, 1):
                print(f"{j}. {question}")
            
            # Simulate user answers
            sample_answers = [
                "The login page at /partner/login",
                "Enter username and password, then click the login button",
                "Check that the dashboard page loads with the partner's name displayed"
            ]
            
            print(f"\nSimulated answers:")
            for j, answer in enumerate(sample_answers[:len(questions)], 1):
                print(f"{j}. {answer}")
            
            # Provide answers
            answer_result = manager.provide_answers(session_id, sample_answers[:len(questions)])
            print(f"\nRefinement result: {answer_result}")
            
            # Get final refined prompt
            if answer_result.get("state") == "completed":
                final_prompt = manager.complete_session(session_id)
                print(f"\nFinal refined prompt: {final_prompt}")
        else:
            print("Prompt already sufficiently detailed!")


if __name__ == "__main__":
    asyncio.run(test_refinement())
