"""
Vector Database Manager Tab for Gradio Web Interface
Provides a comprehensive interface for managing Pinecone vector databases.
"""

import gradio as gr
import os
import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.vector_db.pinecone_client import PineconeVectorDBManager

logger = logging.getLogger(__name__)


class VectorDBManagerInterface:
    """Web interface for vector database management."""
    
    def __init__(self):
        """Initialize the interface."""
        self.db_manager = None
        self._initialize_manager()
    
    def _initialize_manager(self):
        """Initialize the database manager."""
        try:
            self.db_manager = PineconeVectorDBManager()
            return f"✅ Connected to Pinecone index: {self.db_manager.index_name}"
        except Exception as e:
            logger.error(f"Failed to initialize Pinecone manager: {e}")
            return f"❌ Failed to connect: {str(e)}"
    
    def get_index_stats(self) -> <PERSON><PERSON>[str, str]:
        """Get current index statistics."""
        try:
            if not self.db_manager:
                return "❌ Database not connected", ""
            
            stats = self.db_manager.get_index_stats()
            
            stats_text = f"""
## Index Statistics: {stats['index_name']}

- **Total Vectors:** {stats['total_vector_count']:,}
- **Dimension:** {stats['dimension']}
- **Index Fullness:** {stats['index_fullness']:.2%}
- **Last Updated:** {stats['timestamp']}

### Namespaces:
"""
            
            if stats['namespaces']:
                for ns, ns_stats in stats['namespaces'].items():
                    ns_name = ns if ns else 'default'
                    stats_text += f"- **{ns_name}:** {ns_stats.get('vector_count', 0):,} vectors\n"
            else:
                stats_text += "- No namespaces found\n"
            
            return stats_text, "Stats refreshed successfully"
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return f"❌ Error: {str(e)}", "Failed to get stats"
    
    def list_documents(self, namespace: str = "") -> Tuple[str, str]:
        """List all documents in the database."""
        try:
            if not self.db_manager:
                return "❌ Database not connected", ""
            
            documents = self.db_manager.get_document_list(namespace=namespace)
            
            if not documents:
                return "No documents found in the database.", "No documents found"
            
            docs_text = f"## Documents in Database ({len(documents)} total)\n\n"
            
            for doc in documents:
                docs_text += f"""
### 📄 {doc['document_name']}
- **Chunks:** {doc['chunk_count']}
- **Total Length:** {doc['total_length']:,} characters
- **Type:** {doc.get('file_type', 'Unknown')}
- **Created:** {doc.get('created_at', 'Unknown')[:19] if doc.get('created_at') else 'Unknown'}
- **Namespace:** {doc['namespace']}
- **Source:** {doc.get('source_file', 'Unknown')}

---
"""
            
            return docs_text, f"Found {len(documents)} documents"
            
        except Exception as e:
            logger.error(f"Error listing documents: {e}")
            return f"❌ Error: {str(e)}", "Failed to list documents"
    
    def search_documents(self, query: str, top_k: int = 5, namespace: str = "") -> Tuple[str, str]:
        """Search for documents."""
        try:
            if not self.db_manager:
                return "❌ Database not connected", ""
            
            if not query.strip():
                return "Please enter a search query.", "No query provided"
            
            results = self.db_manager.search(query, top_k=top_k, namespace=namespace)
            
            if not results:
                return f"No results found for query: '{query}'", "No results found"
            
            results_text = f"## Search Results for: '{query}'\n\n"
            results_text += f"Found {len(results)} results:\n\n"
            
            for i, result in enumerate(results, 1):
                results_text += f"""
### {i}. Score: {result['score']:.4f}
**Document:** {result['metadata'].get('document_name', 'Unknown')}  
**ID:** `{result['id']}`  
**Namespace:** {result['namespace']}

**Text Preview:**
{result['text'][:300]}{'...' if len(result['text']) > 300 else ''}

---
"""
            
            return results_text, f"Found {len(results)} results"
            
        except Exception as e:
            logger.error(f"Error searching: {e}")
            return f"❌ Error: {str(e)}", "Search failed"
    
    def add_file_to_db(self, file_path: str, document_name: str = "", namespace: str = "", 
                       chunk_size: int = 800, chunk_overlap: int = 100) -> Tuple[str, str]:
        """Add a file to the database."""
        try:
            if not self.db_manager:
                return "❌ Database not connected", ""
            
            if not file_path or not os.path.exists(file_path):
                return "❌ Please provide a valid file path", "Invalid file path"
            
            result = self.db_manager.add_file_to_db(
                file_path=file_path,
                document_name=document_name if document_name else None,
                namespace=namespace,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap
            )
            
            if result['success']:
                success_text = f"""
## ✅ File Added Successfully

- **File:** {result['document_name']}
- **Path:** {result['file_path']}
- **Chunks Created:** {result['chunks_created']}
- **Vectors Upserted:** {result['upsert_result']['upserted_count']}
- **Namespace:** {result['upsert_result']['namespace']}

The file has been processed and added to the vector database.
"""
                return success_text, "File added successfully"
            else:
                return f"❌ Failed to add file: {result.get('error', 'Unknown error')}", "Failed to add file"
                
        except Exception as e:
            logger.error(f"Error adding file: {e}")
            return f"❌ Error: {str(e)}", "Failed to add file"
    
    def delete_document(self, document_name: str, namespace: str = "") -> Tuple[str, str]:
        """Delete a document from the database."""
        try:
            if not self.db_manager:
                return "❌ Database not connected", ""
            
            if not document_name.strip():
                return "Please provide a document name to delete.", "No document name provided"
            
            result = self.db_manager.delete_document(document_name, namespace=namespace)
            
            if result['success']:
                return f"✅ Successfully deleted document: {document_name}", "Document deleted"
            else:
                return f"❌ Failed to delete document: {result.get('error', 'Unknown error')}", "Failed to delete"
                
        except Exception as e:
            logger.error(f"Error deleting document: {e}")
            return f"❌ Error: {str(e)}", "Failed to delete document"
    
    def export_database(self, output_path: str, namespace: str = "") -> Tuple[str, str]:
        """Export database to a file."""
        try:
            if not self.db_manager:
                return "❌ Database not connected", ""
            
            if not output_path.strip():
                return "Please provide an output file path.", "No output path provided"
            
            # Ensure the output path has .json extension
            if not output_path.endswith('.json'):
                output_path += '.json'
            
            result = self.db_manager.export_vectors_to_file(output_path, namespace=namespace)
            
            if result['success']:
                export_text = f"""
## ✅ Export Completed

- **Output File:** {result['output_path']}
- **Vectors Exported:** {result['exported_count']}
- **Namespace:** {result['namespace']}

The database has been exported to the specified file.
"""
                return export_text, "Export completed"
            else:
                return f"❌ Failed to export: {result.get('error', 'Unknown error')}", "Export failed"
                
        except Exception as e:
            logger.error(f"Error exporting database: {e}")
            return f"❌ Error: {str(e)}", "Export failed"
    
    def clear_database(self, namespace: str = "") -> Tuple[str, str]:
        """Clear the database."""
        try:
            if not self.db_manager:
                return "❌ Database not connected", ""
            
            success = self.db_manager.delete_all_vectors(namespace=namespace)
            
            if success:
                ns_text = f"namespace '{namespace}'" if namespace else "entire database"
                return f"✅ Successfully cleared {ns_text}", "Database cleared"
            else:
                return "❌ Failed to clear database", "Clear failed"
                
        except Exception as e:
            logger.error(f"Error clearing database: {e}")
            return f"❌ Error: {str(e)}", "Clear failed"


def create_vector_db_manager_tab():
    """Create the vector database manager tab."""
    
    # Initialize the interface
    interface = VectorDBManagerInterface()
    
    with gr.Column():
        gr.Markdown("# 🗄️ Vector Database Manager")
        gr.Markdown("Manage your Pinecone vector database with full CRUD operations.")
        
        # Connection status
        with gr.Row():
            connection_status = gr.Markdown(interface._initialize_manager())
            refresh_connection_btn = gr.Button("🔄 Refresh Connection", size="sm")
        
        # Main tabs
        with gr.Tabs():
            # Database Overview Tab
            with gr.TabItem("📊 Overview"):
                with gr.Row():
                    refresh_stats_btn = gr.Button("🔄 Refresh Stats", variant="primary")
                
                stats_display = gr.Markdown("Click 'Refresh Stats' to view database statistics.")
                stats_status = gr.Textbox(label="Status", interactive=False, visible=False)
            
            # Documents Tab
            with gr.TabItem("📄 Documents"):
                with gr.Row():
                    docs_namespace = gr.Textbox(label="Namespace (optional)", placeholder="Leave empty for default")
                    list_docs_btn = gr.Button("📋 List Documents", variant="primary")
                
                docs_display = gr.Markdown("Click 'List Documents' to view all documents in the database.")
                docs_status = gr.Textbox(label="Status", interactive=False, visible=False)
            
            # Search Tab
            with gr.TabItem("🔍 Search"):
                with gr.Row():
                    search_query = gr.Textbox(label="Search Query", placeholder="Enter your search query...")
                    search_top_k = gr.Number(label="Max Results", value=5, minimum=1, maximum=50)
                
                with gr.Row():
                    search_namespace = gr.Textbox(label="Namespace (optional)", placeholder="Leave empty for default")
                    search_btn = gr.Button("🔍 Search", variant="primary")
                
                search_results = gr.Markdown("Enter a query and click 'Search' to find relevant documents.")
                search_status = gr.Textbox(label="Status", interactive=False, visible=False)
            
            # Add Files Tab
            with gr.TabItem("➕ Add Files"):
                with gr.Column():
                    file_path_input = gr.Textbox(label="File Path", placeholder="Enter the path to your file...")
                    doc_name_input = gr.Textbox(label="Document Name (optional)", placeholder="Leave empty to use filename")
                    
                    with gr.Row():
                        add_namespace = gr.Textbox(label="Namespace (optional)", placeholder="Leave empty for default")
                        chunk_size = gr.Number(label="Chunk Size", value=800, minimum=100, maximum=2000)
                        chunk_overlap = gr.Number(label="Chunk Overlap", value=100, minimum=0, maximum=500)
                    
                    add_file_btn = gr.Button("📁 Add File", variant="primary")
                
                add_file_result = gr.Markdown("Select a file and click 'Add File' to upload it to the database.")
                add_file_status = gr.Textbox(label="Status", interactive=False, visible=False)
            
            # Delete Tab
            with gr.TabItem("🗑️ Delete"):
                gr.Markdown("⚠️ **Warning:** Deletion operations cannot be undone!")
                
                with gr.Row():
                    delete_doc_name = gr.Textbox(label="Document Name", placeholder="Enter document name to delete...")
                    delete_namespace = gr.Textbox(label="Namespace (optional)", placeholder="Leave empty for default")
                
                with gr.Row():
                    delete_doc_btn = gr.Button("🗑️ Delete Document", variant="stop")
                    clear_db_btn = gr.Button("🧹 Clear Database", variant="stop")
                
                delete_result = gr.Markdown("Enter a document name and click 'Delete Document' to remove it.")
                delete_status = gr.Textbox(label="Status", interactive=False, visible=False)
            
            # Export Tab
            with gr.TabItem("📤 Export"):
                with gr.Row():
                    export_path = gr.Textbox(label="Output File Path", placeholder="Enter path for export file (e.g., ./backup.json)")
                    export_namespace = gr.Textbox(label="Namespace (optional)", placeholder="Leave empty for default")
                
                export_btn = gr.Button("📤 Export Database", variant="primary")
                
                export_result = gr.Markdown("Enter an output path and click 'Export Database' to save your data.")
                export_status = gr.Textbox(label="Status", interactive=False, visible=False)
        
        # Event handlers
        refresh_connection_btn.click(
            fn=interface._initialize_manager,
            outputs=[connection_status]
        )
        
        refresh_stats_btn.click(
            fn=interface.get_index_stats,
            outputs=[stats_display, stats_status]
        )
        
        list_docs_btn.click(
            fn=interface.list_documents,
            inputs=[docs_namespace],
            outputs=[docs_display, docs_status]
        )
        
        search_btn.click(
            fn=interface.search_documents,
            inputs=[search_query, search_top_k, search_namespace],
            outputs=[search_results, search_status]
        )
        
        add_file_btn.click(
            fn=interface.add_file_to_db,
            inputs=[file_path_input, doc_name_input, add_namespace, chunk_size, chunk_overlap],
            outputs=[add_file_result, add_file_status]
        )
        
        delete_doc_btn.click(
            fn=interface.delete_document,
            inputs=[delete_doc_name, delete_namespace],
            outputs=[delete_result, delete_status]
        )
        
        clear_db_btn.click(
            fn=interface.clear_database,
            inputs=[delete_namespace],
            outputs=[delete_result, delete_status]
        )
        
        export_btn.click(
            fn=interface.export_database,
            inputs=[export_path, export_namespace],
            outputs=[export_result, export_status]
        )
