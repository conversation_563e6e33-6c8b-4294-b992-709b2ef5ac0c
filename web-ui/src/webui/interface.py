import gradio as gr
import os

from src.webui.webui_manager import Webui<PERSON>anager
from src.webui.components.agent_settings_tab import create_agent_settings_tab
from src.webui.components.browser_settings_tab import create_browser_settings_tab
from src.webui.components.browser_use_agent_tab import create_browser_use_agent_tab
from src.webui.components.deep_research_agent_tab import create_deep_research_agent_tab
from src.webui.components.load_save_config_tab import create_load_save_config_tab
from src.utils import config

theme_map = {
    "Default": gr.themes.Default(),
    "Soft": gr.themes.Soft(),
    "Monochrome": gr.themes.Monochrome(),
    "Glass": gr.themes.Glass(),
    "Origin": gr.themes.Origin(),
    "Citrus": gr.themes.Citrus(),
    "Ocean": gr.themes.Ocean(),
    "Base": gr.themes.Base()
}


def create_ui(theme_name="Ocean"):
    css = """
    .gradio-container {
        width: 70vw !important; 
        max-width: 70% !important; 
        margin-left: auto !important;
        margin-right: auto !important;
        padding-top: 10px !important;
    }
    .header-text {
        text-align: center;
        margin-bottom: 20px;
    }
    .tab-header-text {
        text-align: center;
    }
    .theme-section {
        margin-bottom: 10px;
        padding: 15px;
        border-radius: 10px;
    }
    footer {
        display: none !important;
    }
    """

    # dark mode in default
    js_func = """
    function refresh() {
        const url = new URL(window.location);

        if (url.searchParams.get('__theme') !== 'dark') {
            url.searchParams.set('__theme', 'dark');
            window.location.href = url.href;
        }
    }
    """

    ui_manager = WebuiManager()

    with gr.Blocks(
            title="UI testing with DrCode", theme=theme_map[theme_name], css=css, js=js_func,
    ) as demo:
        with gr.Row():
            gr.Markdown(
                """
                <div style="display: flex; align-items: center; justify-content: center; gap: 24px; margin-bottom: 10px;">
                    <img src="https://cdn.drcode.ai/assets/public/logo.webp" alt="DrCode" style="height: 30px;">
                    <span style="font-size: 1.6rem; font-weight: bold; color: white;">Seamless UI testing with DrCode</span>
                </div>
                """,
                elem_classes=["header-text"],
            )

        with gr.Tabs(selected=0) as tabs:
            # Hidden tabs for functionality but not visible in menu
            with gr.TabItem("⚙️ Agent Settings", visible=False):
                create_agent_settings_tab(ui_manager)

            with gr.TabItem("🌐 Browser Settings", visible=False):
                create_browser_settings_tab(ui_manager)

            # with gr.TabItem("🧪 QA Testing Settings"):
            #     create_qa_settings_tab()

            with gr.TabItem("Run Agent"):
                create_browser_use_agent_tab(ui_manager)

    return demo
