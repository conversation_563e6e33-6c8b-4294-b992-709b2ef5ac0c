"""
Prompt Analysis and Refinement System

This module analyzes user prompts against the vector database to determine
if they need clarification and generates appropriate questions.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Tuple
import openai
from dotenv import load_dotenv

from src.vector_db.pinecone_client import PineconeVectorDB

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class PromptAnalyzer:
    """Analyzes prompts and determines if they need refinement."""
    
    def __init__(self):
        """Initialize the prompt analyzer."""
        self.vector_db = PineconeVectorDB()
        self.vector_db.create_index_if_not_exists()
        
        # Initialize OpenAI client
        self.openai_client = openai.OpenAI(
            api_key=os.getenv("OPENAI_API_KEY")
        )
        
        # Thresholds for determining prompt quality
        self.min_similarity_threshold = 0.6  # Minimum similarity to consider relevant
        self.vague_prompt_indicators = [
            "test", "check", "verify", "make sure", "ensure", "general",
            "basic", "simple", "quick", "help", "assist", "do something"
        ]
        
    def analyze_prompt(self, user_prompt: str) -> Dict[str, Any]:
        """
        Analyze a user prompt to determine if it needs refinement.
        
        Args:
            user_prompt: The user's input prompt
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            # Search vector database for relevant context
            search_results = self.vector_db.search(user_prompt, top_k=5)
            
            # Analyze prompt specificity
            specificity_score = self._calculate_specificity_score(user_prompt)
            
            # Check if prompt has sufficient context
            has_sufficient_context = self._has_sufficient_context(user_prompt, search_results)
            
            # Determine if refinement is needed
            needs_refinement = (
                specificity_score < 0.6 or 
                not has_sufficient_context or
                len(user_prompt.split()) < 5
            )
            
            return {
                "needs_refinement": needs_refinement,
                "specificity_score": specificity_score,
                "has_sufficient_context": has_sufficient_context,
                "relevant_context": search_results,
                "analysis_details": {
                    "prompt_length": len(user_prompt.split()),
                    "vague_indicators_found": self._find_vague_indicators(user_prompt),
                    "context_relevance_scores": [r["score"] for r in search_results]
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing prompt: {e}")
            return {
                "needs_refinement": True,
                "error": str(e),
                "specificity_score": 0.0,
                "has_sufficient_context": False,
                "relevant_context": []
            }
    
    def _calculate_specificity_score(self, prompt: str) -> float:
        """Calculate how specific a prompt is (0.0 to 1.0)."""
        words = prompt.lower().split()
        
        # Penalize vague indicators
        vague_count = sum(1 for word in words if word in self.vague_prompt_indicators)
        vague_penalty = min(vague_count * 0.2, 0.8)
        
        # Reward specific terms
        specific_terms = [
            "login", "signup", "register", "authenticate", "dashboard",
            "profile", "assessment", "coupon", "generate", "create",
            "download", "report", "api", "endpoint", "integration",
            "partner", "student", "admin", "platform", "mindler"
        ]
        
        specific_count = sum(1 for word in words if word in specific_terms)
        specific_bonus = min(specific_count * 0.1, 0.4)
        
        # Base score from length (longer prompts tend to be more specific)
        length_score = min(len(words) / 20, 0.6)
        
        final_score = max(0.0, min(1.0, length_score + specific_bonus - vague_penalty))
        return final_score
    
    def _has_sufficient_context(self, prompt: str, search_results: List[Dict]) -> bool:
        """Check if the prompt has sufficient context from the knowledge base."""
        if not search_results:
            return False
        
        # Check if top results have good similarity scores
        top_scores = [r["score"] for r in search_results[:3]]
        return any(score >= self.min_similarity_threshold for score in top_scores)
    
    def _find_vague_indicators(self, prompt: str) -> List[str]:
        """Find vague indicators in the prompt."""
        words = prompt.lower().split()
        return [word for word in words if word in self.vague_prompt_indicators]
    
    def generate_clarifying_questions(self, user_prompt: str, analysis: Dict[str, Any]) -> List[str]:
        """
        Generate clarifying questions based on prompt analysis.
        
        Args:
            user_prompt: Original user prompt
            analysis: Analysis results from analyze_prompt
            
        Returns:
            List of clarifying questions
        """
        try:
            # Prepare context from vector database
            context_text = ""
            if analysis.get("relevant_context"):
                context_text = "\n".join([
                    f"- {result['text'][:200]}..."
                    for result in analysis["relevant_context"][:3]
                ])
            
            # Create prompt for GPT to generate questions
            system_prompt = """You are an AI assistant helping to refine user prompts for UI testing automation. 
            
Your task is to generate 2-3 specific clarifying questions that will help make a vague prompt more actionable for browser automation.

Focus on:
1. Specific UI elements or pages to interact with
2. Exact actions to perform (click, type, navigate, etc.)
3. Expected outcomes or verification steps
4. Any specific data or credentials needed

Keep questions concise and actionable."""

            user_message = f"""
Original prompt: "{user_prompt}"

Available context from knowledge base:
{context_text}

Analysis details:
- Specificity score: {analysis.get('specificity_score', 0):.2f}
- Vague indicators found: {analysis.get('analysis_details', {}).get('vague_indicators_found', [])}
- Prompt length: {analysis.get('analysis_details', {}).get('prompt_length', 0)} words

Generate 2-3 specific clarifying questions to make this prompt more actionable for browser automation.
Return only the questions, one per line, without numbering or bullets.
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.7,
                max_tokens=300
            )
            
            questions = response.choices[0].message.content.strip().split('\n')
            questions = [q.strip() for q in questions if q.strip()]
            
            return questions[:3]  # Limit to 3 questions
            
        except Exception as e:
            logger.error(f"Error generating clarifying questions: {e}")
            # Fallback questions
            return [
                "What specific page or section of the platform should I navigate to?",
                "What exact actions should I perform (click, type, select, etc.)?",
                "How should I verify that the task was completed successfully?"
            ]
    
    def refine_prompt_with_answers(self, original_prompt: str, questions: List[str], answers: List[str]) -> str:
        """
        Refine the original prompt using the answers to clarifying questions.
        
        Args:
            original_prompt: Original user prompt
            questions: List of clarifying questions
            answers: List of answers to the questions
            
        Returns:
            Refined prompt
        """
        try:
            # Create Q&A pairs
            qa_pairs = []
            for q, a in zip(questions, answers):
                if a.strip():
                    qa_pairs.append(f"Q: {q}\nA: {a}")
            
            qa_text = "\n\n".join(qa_pairs)
            
            system_prompt = """You are an AI assistant that refines user prompts for browser automation testing.

Your task is to combine the original prompt with the clarifying information to create a detailed, actionable prompt for browser automation.

The refined prompt should:
1. Be specific about what pages/elements to interact with
2. Include exact steps to perform
3. Specify how to verify success
4. Be clear and unambiguous for automation

Return only the refined prompt, nothing else."""

            user_message = f"""
Original prompt: "{original_prompt}"

Clarifying information:
{qa_text}

Create a refined, detailed prompt that incorporates this information for browser automation.
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.3,
                max_tokens=500
            )
            
            refined_prompt = response.choices[0].message.content.strip()
            return refined_prompt
            
        except Exception as e:
            logger.error(f"Error refining prompt: {e}")
            # Fallback: combine original prompt with answers
            combined = f"{original_prompt}\n\nAdditional details:\n"
            for q, a in zip(questions, answers):
                if a.strip():
                    combined += f"- {q} {a}\n"
            return combined
