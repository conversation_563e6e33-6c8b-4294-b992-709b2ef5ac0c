"""
Refinement Manager for Human-Agent Loop

This module manages the iterative process of prompt refinement through
human-agent interaction until the prompt is sufficiently detailed.
"""

import logging
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum

from src.prompt_refinement.analyzer import PromptAnalyzer
from src.prompt_refinement.config import RefinementConfig

logger = logging.getLogger(__name__)


class RefinementState(Enum):
    """States of the refinement process."""
    INITIAL = "initial"
    ANALYZING = "analyzing"
    WAITING_FOR_CLARIFICATION = "waiting_for_clarification"
    REFINING = "refining"
    COMPLETED = "completed"
    ERROR = "error"


class RefinementSession:
    """Represents a single prompt refinement session."""
    
    def __init__(self, session_id: str, original_prompt: str):
        self.session_id = session_id
        self.original_prompt = original_prompt
        self.state = RefinementState.INITIAL
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
        # Refinement data
        self.analysis_result = None
        self.clarifying_questions = []
        self.user_answers = []
        self.refined_prompt = None
        self.iteration_count = 0
        self.max_iterations = RefinementConfig.MAX_ITERATIONS
        
        # History
        self.history = []
    
    def add_to_history(self, action: str, data: Any):
        """Add an action to the session history."""
        self.history.append({
            "timestamp": datetime.now(),
            "action": action,
            "data": data
        })
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary."""
        return {
            "session_id": self.session_id,
            "original_prompt": self.original_prompt,
            "state": self.state.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "analysis_result": self.analysis_result,
            "clarifying_questions": self.clarifying_questions,
            "user_answers": self.user_answers,
            "refined_prompt": self.refined_prompt,
            "iteration_count": self.iteration_count,
            "max_iterations": self.max_iterations,
            "history": [
                {
                    "timestamp": h["timestamp"].isoformat(),
                    "action": h["action"],
                    "data": h["data"]
                }
                for h in self.history
            ]
        }


class RefinementManager:
    """Manages the prompt refinement process."""
    
    def __init__(self):
        """Initialize the refinement manager."""
        self.analyzer = PromptAnalyzer()
        self.active_sessions: Dict[str, RefinementSession] = {}
    
    def start_refinement_session(self, user_prompt: str) -> Dict[str, Any]:
        """
        Start a new refinement session.
        
        Args:
            user_prompt: The user's initial prompt
            
        Returns:
            Session information and next steps
        """
        try:
            session_id = str(uuid.uuid4())
            session = RefinementSession(session_id, user_prompt)
            
            # Store session
            self.active_sessions[session_id] = session
            
            # Analyze the prompt
            session.state = RefinementState.ANALYZING
            session.add_to_history("session_started", {"prompt": user_prompt})
            
            analysis_result = self.analyzer.analyze_prompt(user_prompt)
            session.analysis_result = analysis_result
            session.add_to_history("prompt_analyzed", analysis_result)
            
            # Check if refinement is needed
            if not analysis_result.get("needs_refinement", True):
                # Prompt is already good enough
                session.state = RefinementState.COMPLETED
                session.refined_prompt = user_prompt
                session.add_to_history("refinement_completed", {"refined_prompt": user_prompt})
                
                return {
                    "session_id": session_id,
                    "needs_refinement": False,
                    "refined_prompt": user_prompt,
                    "state": session.state.value,
                    "message": "Prompt is already sufficiently detailed."
                }
            
            # Generate clarifying questions
            questions = self.analyzer.generate_clarifying_questions(user_prompt, analysis_result)
            session.clarifying_questions = questions
            session.state = RefinementState.WAITING_FOR_CLARIFICATION
            session.add_to_history("questions_generated", {"questions": questions})
            
            return {
                "session_id": session_id,
                "needs_refinement": True,
                "clarifying_questions": questions,
                "state": session.state.value,
                "analysis": {
                    "specificity_score": analysis_result.get("specificity_score", 0),
                    "has_sufficient_context": analysis_result.get("has_sufficient_context", False),
                    "relevant_context_count": len(analysis_result.get("relevant_context", []))
                },
                "message": "Please answer the clarifying questions to refine your prompt."
            }
            
        except Exception as e:
            logger.error(f"Error starting refinement session: {e}")
            return {
                "session_id": None,
                "needs_refinement": True,
                "error": str(e),
                "state": "error",
                "message": "Failed to start refinement session."
            }
    
    def provide_answers(self, session_id: str, answers: List[str]) -> Dict[str, Any]:
        """
        Provide answers to clarifying questions.
        
        Args:
            session_id: The refinement session ID
            answers: List of answers to the clarifying questions
            
        Returns:
            Updated session information
        """
        try:
            if session_id not in self.active_sessions:
                return {
                    "error": "Session not found",
                    "state": "error"
                }
            
            session = self.active_sessions[session_id]
            
            if session.state != RefinementState.WAITING_FOR_CLARIFICATION:
                return {
                    "error": f"Session is in {session.state.value} state, expected waiting_for_clarification",
                    "state": session.state.value
                }
            
            # Store answers
            session.user_answers = answers
            session.add_to_history("answers_provided", {"answers": answers})
            
            # Refine the prompt
            session.state = RefinementState.REFINING
            refined_prompt = self.analyzer.refine_prompt_with_answers(
                session.original_prompt,
                session.clarifying_questions,
                answers
            )
            
            session.refined_prompt = refined_prompt
            session.iteration_count += 1
            session.add_to_history("prompt_refined", {"refined_prompt": refined_prompt})
            
            # Analyze the refined prompt to see if it needs further refinement
            refined_analysis = self.analyzer.analyze_prompt(refined_prompt)
            
            # Check if we should continue refining or complete
            should_continue = (
                refined_analysis.get("needs_refinement", False) and
                session.iteration_count < session.max_iterations
            )
            
            if should_continue:
                # Generate new questions for further refinement
                new_questions = self.analyzer.generate_clarifying_questions(refined_prompt, refined_analysis)
                session.clarifying_questions = new_questions
                session.state = RefinementState.WAITING_FOR_CLARIFICATION
                session.add_to_history("additional_questions_generated", {"questions": new_questions})
                
                return {
                    "session_id": session_id,
                    "needs_further_refinement": True,
                    "refined_prompt": refined_prompt,
                    "clarifying_questions": new_questions,
                    "state": session.state.value,
                    "iteration": session.iteration_count,
                    "message": "Prompt has been refined, but could benefit from additional clarification."
                }
            else:
                # Refinement complete
                session.state = RefinementState.COMPLETED
                session.add_to_history("refinement_completed", {"final_prompt": refined_prompt})
                
                return {
                    "session_id": session_id,
                    "needs_further_refinement": False,
                    "refined_prompt": refined_prompt,
                    "state": session.state.value,
                    "iteration": session.iteration_count,
                    "message": "Prompt refinement completed successfully."
                }
            
        except Exception as e:
            logger.error(f"Error providing answers: {e}")
            if session_id in self.active_sessions:
                self.active_sessions[session_id].state = RefinementState.ERROR
            return {
                "error": str(e),
                "state": "error",
                "message": "Failed to process answers."
            }
    
    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get the current status of a refinement session."""
        if session_id not in self.active_sessions:
            return {
                "error": "Session not found",
                "state": "error"
            }
        
        session = self.active_sessions[session_id]
        return session.to_dict()
    
    def complete_session(self, session_id: str) -> Optional[str]:
        """
        Complete a refinement session and return the final refined prompt.
        
        Args:
            session_id: The refinement session ID
            
        Returns:
            The refined prompt if session is completed, None otherwise
        """
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        if session.state == RefinementState.COMPLETED:
            refined_prompt = session.refined_prompt
            # Clean up session
            del self.active_sessions[session_id]
            return refined_prompt
        
        return None
    
    def cleanup_old_sessions(self, max_age_hours: int = None):
        """Clean up old sessions."""
        if max_age_hours is None:
            max_age_hours = RefinementConfig.SESSION_TIMEOUT_HOURS

        current_time = datetime.now()
        sessions_to_remove = []
        
        for session_id, session in self.active_sessions.items():
            age_hours = (current_time - session.created_at).total_seconds() / 3600
            if age_hours > max_age_hours:
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            del self.active_sessions[session_id]
            logger.info(f"Cleaned up old session: {session_id}")
    
    def list_active_sessions(self) -> List[Dict[str, Any]]:
        """List all active sessions."""
        return [session.to_dict() for session in self.active_sessions.values()]
