"""
Configuration settings for the prompt refinement system.
"""

import os
from typing import List


class RefinementConfig:
    """Configuration class for prompt refinement system."""
    
    # Scoring thresholds
    MIN_SPECIFICITY_THRESHOLD = float(os.getenv("REFINEMENT_MIN_SPECIFICITY", "0.6"))
    SPECIFICITY_THRESHOLD = float(os.getenv("REFINEMENT_SPECIFICITY_THRESHOLD", "0.7"))  # For auto-refinement completion
    MIN_SIMILARITY_THRESHOLD = float(os.getenv("REFINEMENT_MIN_SIMILARITY", "0.6"))
    SEMANTIC_SCORE_THRESHOLD = float(os.getenv("REFINEMENT_SEMANTIC_THRESHOLD", "0.6"))  # For auto-refinement completion
    MIN_PROMPT_LENGTH = int(os.getenv("REFINEMENT_MIN_PROMPT_LENGTH", "5"))
    
    # Session management
    MAX_ITERATIONS = int(os.getenv("REFINEMENT_MAX_ITERATIONS", "3"))
    SESSION_TIMEOUT_HOURS = int(os.getenv("REFINEMENT_SESSION_TIMEOUT_HOURS", "24"))
    
    # Vector search settings
    VECTOR_SEARCH_TOP_K = int(os.getenv("REFINEMENT_VECTOR_SEARCH_TOP_K", "5"))
    CONTEXT_CHUNKS_FOR_QUESTIONS = int(os.getenv("REFINEMENT_CONTEXT_CHUNKS", "3"))
    
    # Question generation
    MAX_QUESTIONS_PER_ITERATION = int(os.getenv("REFINEMENT_MAX_QUESTIONS", "3"))
    QUESTION_GENERATION_MODEL = os.getenv("REFINEMENT_QUESTION_MODEL", "gpt-4o-mini")
    QUESTION_GENERATION_TEMPERATURE = float(os.getenv("REFINEMENT_QUESTION_TEMPERATURE", "0.7"))
    
    # Prompt refinement
    PROMPT_REFINEMENT_MODEL = os.getenv("REFINEMENT_PROMPT_MODEL", "gpt-4o-mini")
    PROMPT_REFINEMENT_TEMPERATURE = float(os.getenv("REFINEMENT_PROMPT_TEMPERATURE", "0.3"))
    
    # Vague indicators - words that suggest a prompt needs refinement
    VAGUE_INDICATORS = [
        "test", "check", "verify", "make sure", "ensure", "general",
        "basic", "simple", "quick", "help", "assist", "do something",
        "try", "see if", "look at", "examine", "review"
    ]
    
    # Specific terms - words that suggest a prompt is already detailed
    SPECIFIC_TERMS = [
        "login", "signup", "register", "authenticate", "dashboard",
        "profile", "form", "button", "click", "navigate", "page",
        "download", "upload", "submit", "verify", "validate",
        "api", "endpoint", "integration", "user", "admin", "settings",
        "menu", "search", "filter", "table", "modal", "popup",
        "input", "field", "dropdown", "checkbox", "radio", "select",
        "header", "footer", "sidebar", "content", "section"
    ]
    
    # System prompts for LLM interactions
    QUESTION_GENERATION_SYSTEM_PROMPT = """You are an AI assistant helping to refine user prompts for UI testing automation. 

Your task is to generate 2-3 specific clarifying questions that will help make a vague prompt more actionable for browser automation.

Focus on:
1. Specific UI elements or pages to interact with
2. Exact actions to perform (click, type, navigate, etc.)
3. Expected outcomes or verification steps
4. Any specific data or credentials needed
5. Do not ask for technical details (like CSS selectors or XPath)

Keep questions concise and actionable."""

    PROMPT_REFINEMENT_SYSTEM_PROMPT = """You are an AI assistant that refines user prompts for browser automation testing.

Your task is to combine the original prompt with the clarifying information to create a detailed, actionable prompt for browser automation.

The refined prompt should:
1. Be specific about what pages/elements to interact with
2. Include exact steps to perform
3. Specify how to verify success
4. Be clear and unambiguous for automation

Return only the refined prompt, nothing else."""

    @classmethod
    def get_fallback_questions(cls) -> List[str]:
        """Get fallback questions when LLM generation fails."""
        return [
            "What specific page or section should I navigate to?",
            "What exact actions should I perform (click, type, select, etc.)?",
            "How should I verify that the task was completed successfully?"
        ]
    
    @classmethod
    def get_supported_file_extensions(cls) -> List[str]:
        """Get list of supported document file extensions."""
        return ['.docx', '.pdf', '.txt', '.md']
    
    @classmethod
    def get_document_chunk_settings(cls) -> dict:
        """Get document chunking configuration."""
        return {
            'chunk_size': int(os.getenv("DOCUMENT_CHUNK_SIZE", "800")),
            'chunk_overlap': int(os.getenv("DOCUMENT_CHUNK_OVERLAP", "100"))
        }
