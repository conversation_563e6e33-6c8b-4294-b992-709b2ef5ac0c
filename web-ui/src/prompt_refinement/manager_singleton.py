"""
Singleton pattern for RefinementManager to ensure single instance across the application.
"""

from .refinement_manager import RefinementManager


class RefinementManagerSingleton:
    """Singleton wrapper for RefinementManager."""
    
    _instance = None
    _manager = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._manager = RefinementManager()
        return cls._instance
    
    def get_manager(self) -> RefinementManager:
        """Get the singleton RefinementManager instance."""
        return self._manager


# Global function to get the singleton manager
def get_refinement_manager() -> RefinementManager:
    """Get the singleton RefinementManager instance."""
    singleton = RefinementManagerSingleton()
    return singleton.get_manager()
