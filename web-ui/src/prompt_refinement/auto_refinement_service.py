"""
Auto-refinement service that uses vector database to answer clarifying questions automatically.
"""

import logging
from typing import List, Dict, Any, Optional
from ..vector_db.pinecone_client import PineconeVectorDBManager
from .analyzer import PromptAnalyzer
from .config import RefinementConfig
import openai
import os

logger = logging.getLogger(__name__)


class AutoRefinementService:
    """Service for automatic prompt refinement using vector database."""
    
    def __init__(self):
        """Initialize the auto-refinement service."""
        self.analyzer = PromptAnalyzer()
        self.config = RefinementConfig()

        # Initialize OpenAI client for answer generation
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Initialize vector DB (but don't fail if it's not available)
        self.vector_db_available = True
        try:
            self.vector_db = PineconeVectorDBManager()
            logger.info("Vector database initialized successfully")
        except Exception as e:
            logger.warning(f"Vector database not available: {e}")
            self.vector_db_available = False
            self.vector_db = None
    
    def search_knowledge_base(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search the knowledge base for relevant information."""
        if not self.vector_db_available or not self.vector_db:
            logger.info("Vector database not available, returning empty results")
            return []

        try:
            results = self.vector_db.search(query, top_k=top_k)
            logger.info(f"Found {len(results)} results for query: {query[:100]}...")
            return results
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return []
    
    def generate_answer_from_context(self, question: str, context_results: List[Dict[str, Any]]) -> str:
        """Generate an answer to a question using context from vector database."""
        try:
            # Prepare context from search results
            if context_results:
                context_texts = []
                for result in context_results:
                    context_texts.append(f"[Score: {result['score']:.3f}] {result['text']}")
                context = "\n\n".join(context_texts)
            else:
                context = "No specific context found in knowledge base."

            system_prompt = """You are an expert in browser automation and UI testing.
Answer the clarifying question about browser automation tasks with specific, actionable details.

Provide reasonable defaults based on common browser automation practices. Focus on:
- Concrete URLs and page elements (No CSS Selectors or XPath)
- Realistic test data and credentials
- Clear verification steps"""

            user_message = f"""
Question: {question}

Context from knowledge base:
{context}

Provide a specific, actionable answer that would help create a detailed browser automation prompt.
Include concrete examples like URLs, test data, etc.
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.3,
                max_tokens=200
            )

            answer = response.choices[0].message.content.strip()
            logger.info(f"Generated answer for question: {question[:50]}...")
            return answer

        except Exception as e:
            logger.error(f"Error generating answer: {e}")
            # Fallback answer based on question type
            if "url" in question.lower() or "page" in question.lower():
                return "Use the main application page at https://example.com/login"
            elif "credential" in question.lower() or "username" in question.lower():
                return "Use test credentials: username '<EMAIL>' and password 'testpass123'"
            elif "selector" in question.lower() or "element" in question.lower():
                return "Use standard selectors like #username, #password, .login-button"
            else:
                return "Follow standard browser automation best practices for this scenario."
    
    def auto_refine_prompt(self, original_prompt: str, max_iterations: int = 3) -> Dict[str, Any]:
        """
        Automatically refine a prompt using vector database for context.
        
        Args:
            original_prompt: The original user prompt
            max_iterations: Maximum number of refinement iterations
            
        Returns:
            Dictionary with refinement results and iteration logs
        """
        logger.info(f"Starting auto-refinement for prompt: {original_prompt[:100]}...")
        
        # Initialize tracking
        current_prompt = original_prompt
        iteration_logs = []
        
        for iteration in range(max_iterations):
            logger.info(f"Auto-refinement iteration {iteration + 1}/{max_iterations}")
            
            # Analyze current prompt
            analysis = self.analyzer.analyze_prompt(current_prompt)
            specificity_score = analysis.get("specificity_score", 0.0)
            semantic_score = analysis.get("semantic_score", 0.0)
            
            iteration_log = {
                "iteration": iteration + 1,
                "prompt": current_prompt,
                "specificity_score": specificity_score,
                "semantic_score": semantic_score,
                "analysis": analysis
            }
            
            logger.info(f"Iteration {iteration + 1} - Specificity: {specificity_score:.3f}, Semantic: {semantic_score:.3f}")
            
            # Check if refinement is needed
            if (specificity_score >= self.config.SPECIFICITY_THRESHOLD and 
                semantic_score >= self.config.SEMANTIC_SCORE_THRESHOLD):
                iteration_log["refinement_needed"] = False
                iteration_log["reason"] = "Thresholds met"
                iteration_logs.append(iteration_log)
                logger.info(f"Refinement complete - thresholds met at iteration {iteration + 1}")
                break
            
            # Generate clarifying questions
            questions = self.analyzer.generate_clarifying_questions(current_prompt, analysis)
            iteration_log["questions"] = questions
            
            if not questions:
                iteration_log["refinement_needed"] = False
                iteration_log["reason"] = "No questions generated"
                iteration_logs.append(iteration_log)
                logger.info(f"Refinement complete - no questions generated at iteration {iteration + 1}")
                break
            
            # Auto-answer questions using vector database
            answers = []
            question_contexts = []
            
            for question in questions:
                # Search knowledge base for relevant context
                search_results = self.search_knowledge_base(question, top_k=3)
                question_contexts.append({
                    "question": question,
                    "search_results": search_results
                })
                
                # Generate answer from context
                answer = self.generate_answer_from_context(question, search_results)
                answers.append(answer)
                
                logger.info(f"Q: {question[:50]}... A: {answer[:50]}...")
            
            iteration_log["answers"] = answers
            iteration_log["question_contexts"] = question_contexts
            iteration_log["refinement_needed"] = True
            
            # Refine prompt with generated answers
            refined_prompt = self.analyzer.refine_prompt_with_answers(
                current_prompt, questions, answers
            )
            
            iteration_log["refined_prompt"] = refined_prompt
            iteration_logs.append(iteration_log)
            
            # Update current prompt for next iteration
            current_prompt = refined_prompt
            
            logger.info(f"Iteration {iteration + 1} complete - prompt refined")
        
        # Final analysis
        final_analysis = self.analyzer.analyze_prompt(current_prompt)
        final_specificity = final_analysis.get("specificity_score", 0.0)
        final_semantic = final_analysis.get("semantic_score", 0.0)
        
        result = {
            "success": True,
            "original_prompt": original_prompt,
            "final_prompt": current_prompt,
            "iterations_completed": len(iteration_logs),
            "max_iterations": max_iterations,
            "final_specificity_score": final_specificity,
            "final_semantic_score": final_semantic,
            "thresholds_met": (final_specificity >= self.config.SPECIFICITY_THRESHOLD and 
                             final_semantic >= self.config.SEMANTIC_SCORE_THRESHOLD),
            "iteration_logs": iteration_logs,
            "final_analysis": final_analysis
        }
        
        logger.info(f"Auto-refinement completed - {len(iteration_logs)} iterations, "
                   f"Final scores: Specificity={final_specificity:.3f}, Semantic={final_semantic:.3f}")
        
        return result
