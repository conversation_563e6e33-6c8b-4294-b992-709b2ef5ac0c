"""
Auto-refinement service that uses vector database to answer clarifying questions automatically.
"""

import logging
from typing import List, Dict, Any, Optional
from ..vector_db.pinecone_client import PineconeVectorDBManager
from .analyzer import PromptAnalyzer
from .config import RefinementConfig
import openai
import os

logger = logging.getLogger(__name__)


class AutoRefinementService:
    """Service for automatic prompt refinement using vector database."""
    
    def __init__(self):
        """Initialize the auto-refinement service."""
        self.analyzer = PromptAnalyzer()
        self.config = RefinementConfig()

        # Initialize OpenAI client for answer generation
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Initialize vector DB (but don't fail if it's not available)
        self.vector_db_available = True
        try:
            self.vector_db = PineconeVectorDBManager()
            logger.info("Vector database initialized successfully")
        except Exception as e:
            logger.warning(f"Vector database not available: {e}")
            self.vector_db_available = False
            self.vector_db = None
    
    def search_knowledge_base(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """Search the knowledge base for relevant information."""
        if not self.vector_db_available or not self.vector_db:
            logger.info("Vector database not available, returning empty results")
            return []

        try:
            results = self.vector_db.search(query, top_k=top_k)
            logger.info(f"Found {len(results)} results for query: {query[:100]}...")
            return results
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return []
    
    def generate_answer_from_context(self, question: str, context_results: List[Dict[str, Any]]) -> str:
        """Generate an answer to a question using context from vector database."""
        try:
            # Check if we have sufficient context with good relevance scores
            relevant_results = [r for r in context_results if r.get('score', 0) > 0.7]

            if not relevant_results:
                logger.info(f"No relevant context found for question: {question[:50]}...")
                return self._get_conservative_fallback_answer(question)

            # Prepare context from high-relevance search results
            context_texts = []
            for result in relevant_results[:5]:  # Use top 5 relevant results
                context_texts.append(f"[Relevance: {result['score']:.3f}] {result['text']}")

            context = "\n\n".join(context_texts)

            system_prompt = """You are an expert in browser automation and UI testing.
Answer the clarifying question ONLY based on the provided context from the knowledge base.

IMPORTANT RULES:
1. If the context doesn't contain specific information to answer the question, respond with "I don't have enough specific information in the knowledge base to answer this question accurately."
2. Do NOT make up or hallucinate any details not present in the context
3. Do NOT provide generic examples unless they are explicitly mentioned in the context
4. Focus on concrete URLs and page elements (No CSS Selectors or XPath)
5. Only use test data and credentials if they are mentioned in the context"""

            user_message = f"""
Question: {question}

Context from knowledge base:
{context}

Based ONLY on the above context, provide a specific answer. If the context doesn't contain enough information, clearly state that you don't have sufficient information.
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.1,  # Lower temperature to reduce hallucination
                max_tokens=150
            )

            answer = response.choices[0].message.content.strip()

            # Check if the model indicated insufficient information
            if "don't have enough" in answer.lower() or "insufficient" in answer.lower():
                logger.info(f"Model indicated insufficient context for question: {question[:50]}...")
                return self._get_conservative_fallback_answer(question)

            logger.info(f"Generated context-based answer for question: {question[:50]}...")
            return answer

        except Exception as e:
            logger.error(f"Error generating answer: {e}")
            return self._get_conservative_fallback_answer(question)

    def _get_conservative_fallback_answer(self, question: str) -> str:
        """Provide conservative fallback answers when context is insufficient."""
        question_lower = question.lower()

        if any(word in question_lower for word in ["url", "page", "website", "site"]):
            return "Please specify the exact URL or page to be tested"
        elif any(word in question_lower for word in ["credential", "username", "password", "login"]):
            return "Please provide the specific test credentials to be used"
        elif any(word in question_lower for word in ["element", "button", "field", "form"]):
            return "Please specify the exact elements to interact with on the page"
        elif any(word in question_lower for word in ["verify", "check", "confirm", "validate"]):
            return "Please specify what exactly should be verified after the action"
        elif any(word in question_lower for word in ["data", "input", "enter", "fill"]):
            return "Please provide the specific test data to be entered"
        else:
            return "Please provide more specific details for this requirement"
    
    def auto_refine_prompt(self, original_prompt: str) -> Dict[str, Any]:
        """
        Automatically refine a prompt using vector database for context.
        Single iteration to prevent over-refinement and hallucination.

        Args:
            original_prompt: The original user prompt

        Returns:
            Dictionary with refinement results and iteration logs
        """
        logger.info(f"Starting auto-refinement for prompt: {original_prompt[:100]}...")

        # Initialize tracking - single iteration only
        current_prompt = original_prompt
        iteration_logs = []
        max_iterations = 1  # Fixed at 1 to prevent over-refinement

        for iteration in range(max_iterations):
            logger.info(f"Auto-refinement iteration {iteration + 1}/{max_iterations}")
            
            # Analyze current prompt
            analysis = self.analyzer.analyze_prompt(current_prompt)
            specificity_score = analysis.get("specificity_score", 0.0)
            semantic_score = analysis.get("semantic_score", 0.0)
            
            iteration_log = {
                "iteration": iteration + 1,
                "prompt": current_prompt,
                "specificity_score": specificity_score,
                "semantic_score": semantic_score,
                "analysis": analysis
            }
            
            logger.info(f"Iteration {iteration + 1} - Specificity: {specificity_score:.3f}, Semantic: {semantic_score:.3f}")
            
            # Check if refinement is needed
            if (specificity_score >= self.config.SPECIFICITY_THRESHOLD and 
                semantic_score >= self.config.SEMANTIC_SCORE_THRESHOLD):
                iteration_log["refinement_needed"] = False
                iteration_log["reason"] = "Thresholds met"
                iteration_logs.append(iteration_log)
                logger.info(f"Refinement complete - thresholds met at iteration {iteration + 1}")
                break
            
            # Generate clarifying questions
            questions = self.analyzer.generate_clarifying_questions(current_prompt, analysis)
            iteration_log["questions"] = questions
            
            if not questions:
                iteration_log["refinement_needed"] = False
                iteration_log["reason"] = "No questions generated"
                iteration_logs.append(iteration_log)
                logger.info(f"Refinement complete - no questions generated at iteration {iteration + 1}")
                break
            
            # Auto-answer questions using vector database
            answers = []
            question_contexts = []
            
            for question in questions:
                # Search knowledge base for relevant context with more chunks
                search_results = self.search_knowledge_base(question, top_k=10)
                question_contexts.append({
                    "question": question,
                    "search_results": search_results
                })

                # Generate answer from context
                answer = self.generate_answer_from_context(question, search_results)
                answers.append(answer)

                logger.info(f"Q: {question[:50]}... A: {answer[:50]}...")
            
            iteration_log["answers"] = answers
            iteration_log["question_contexts"] = question_contexts
            iteration_log["refinement_needed"] = True
            
            # Refine prompt with generated answers
            refined_prompt = self.analyzer.refine_prompt_with_answers(
                current_prompt, questions, answers
            )
            
            iteration_log["refined_prompt"] = refined_prompt
            iteration_logs.append(iteration_log)
            
            # Update current prompt for next iteration
            current_prompt = refined_prompt
            
            logger.info(f"Iteration {iteration + 1} complete - prompt refined")
        
        # Final analysis
        final_analysis = self.analyzer.analyze_prompt(current_prompt)
        final_specificity = final_analysis.get("specificity_score", 0.0)
        final_semantic = final_analysis.get("semantic_score", 0.0)
        
        result = {
            "success": True,
            "original_prompt": original_prompt,
            "final_prompt": current_prompt,
            "iterations_completed": len(iteration_logs),
            "max_iterations": max_iterations,
            "final_specificity_score": final_specificity,
            "final_semantic_score": final_semantic,
            "thresholds_met": (final_specificity >= self.config.SPECIFICITY_THRESHOLD and 
                             final_semantic >= self.config.SEMANTIC_SCORE_THRESHOLD),
            "iteration_logs": iteration_logs,
            "final_analysis": final_analysis
        }
        
        logger.info(f"Auto-refinement completed - {len(iteration_logs)} iterations, "
                   f"Final scores: Specificity={final_specificity:.3f}, Semantic={final_semantic:.3f}")
        
        return result
