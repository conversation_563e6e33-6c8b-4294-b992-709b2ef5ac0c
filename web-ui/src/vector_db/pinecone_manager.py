#!/usr/bin/env python3
"""
Comprehensive Pinecone Vector Database Manager CLI
Provides full CRUD operations for managing Pinecone vector databases.
"""

import os
import sys
import argparse
import logging
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

try:
    from tabulate import tabulate
except ImportError:
    print("Warning: tabulate not installed. Install with: pip install tabulate")
    def tabulate(data, headers=None, tablefmt='grid'):
        """Fallback tabulate function."""
        if not data:
            return "No data"

        if headers:
            result = " | ".join(headers) + "\n"
            result += "-" * len(result) + "\n"
        else:
            result = ""

        for row in data:
            result += " | ".join(str(cell) for cell in row) + "\n"

        return result

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv(project_root / ".env")

from src.vector_db.pinecone_client import PineconeVectorDBManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PineconeManagerCLI:
    """Command-line interface for Pinecone vector database management."""
    
    def __init__(self):
        """Initialize the CLI manager."""
        try:
            self.db_manager = PineconeVectorDBManager()
            logger.info(f"Connected to Pinecone index: {self.db_manager.index_name}")
        except Exception as e:
            logger.error(f"Failed to initialize Pinecone manager: {e}")
            sys.exit(1)
    
    def list_indexes(self):
        """List all available indexes."""
        try:
            indexes = self.db_manager.list_indexes()
            
            if not indexes:
                print("No indexes found.")
                return
            
            # Format for display
            table_data = []
            for index in indexes:
                table_data.append([
                    index['name'],
                    index['dimension'],
                    index['metric'],
                    index['status'].get('ready', 'Unknown'),
                    index['host'][:50] + '...' if len(index['host']) > 50 else index['host']
                ])
            
            headers = ['Name', 'Dimension', 'Metric', 'Ready', 'Host']
            print("\n" + "="*80)
            print("PINECONE INDEXES")
            print("="*80)
            print(tabulate(table_data, headers=headers, tablefmt='grid'))
            
        except Exception as e:
            logger.error(f"Error listing indexes: {e}")
    
    def create_index(self, name: str, dimension: int = None, metric: str = "cosine"):
        """Create a new index."""
        try:
            if dimension is None:
                print("🔍 Auto-detecting dimension from embedding model...")
            success = self.db_manager.create_index(name, dimension, metric)
            if success:
                print(f"✅ Successfully created index: {name}")
            else:
                print(f"❌ Failed to create index: {name} (may already exist)")
        except Exception as e:
            logger.error(f"Error creating index: {e}")
    
    def delete_index(self, name: str):
        """Delete an index."""
        try:
            # Confirm deletion
            response = input(f"⚠️  Are you sure you want to delete index '{name}'? This cannot be undone. (yes/no): ")
            if response.lower() != 'yes':
                print("Operation cancelled.")
                return
            
            success = self.db_manager.delete_index(name)
            if success:
                print(f"✅ Successfully deleted index: {name}")
            else:
                print(f"❌ Failed to delete index: {name} (may not exist)")
        except Exception as e:
            logger.error(f"Error deleting index: {e}")
    
    def switch_index(self, name: str):
        """Switch to a different index."""
        try:
            success = self.db_manager.switch_index(name)
            if success:
                print(f"✅ Switched to index: {name}")
            else:
                print(f"❌ Failed to switch to index: {name}")
        except Exception as e:
            logger.error(f"Error switching index: {e}")
    
    def show_stats(self):
        """Show current index statistics."""
        try:
            stats = self.db_manager.get_index_stats()
            
            print("\n" + "="*60)
            print(f"INDEX STATISTICS: {stats['index_name']}")
            print("="*60)
            print(f"Total Vectors: {stats['total_vector_count']:,}")
            print(f"Dimension: {stats['dimension']}")
            print(f"Index Fullness: {stats['index_fullness']:.2%}")
            print(f"Last Updated: {stats['timestamp']}")
            
            if stats['namespaces']:
                print("\nNamespaces:")
                for ns, ns_stats in stats['namespaces'].items():
                    ns_name = ns if ns else 'default'
                    print(f"  - {ns_name}: {ns_stats.get('vector_count', 0):,} vectors")
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
    
    def list_documents(self, namespace: str = ""):
        """List all documents in the database."""
        try:
            documents = self.db_manager.get_document_list(namespace=namespace)
            
            if not documents:
                print("No documents found.")
                return
            
            # Format for display
            table_data = []
            for doc in documents:
                table_data.append([
                    doc['document_name'][:40] + '...' if len(doc['document_name']) > 40 else doc['document_name'],
                    doc['chunk_count'],
                    f"{doc['total_length']:,}",
                    doc.get('file_type', 'Unknown'),
                    doc.get('created_at', 'Unknown')[:19] if doc.get('created_at') else 'Unknown',
                    doc['namespace']
                ])
            
            headers = ['Document Name', 'Chunks', 'Total Length', 'Type', 'Created', 'Namespace']
            print("\n" + "="*120)
            print("DOCUMENTS IN DATABASE")
            print("="*120)
            print(tabulate(table_data, headers=headers, tablefmt='grid'))
            
        except Exception as e:
            logger.error(f"Error listing documents: {e}")
    
    def search_documents(self, query: str, top_k: int = 5, namespace: str = ""):
        """Search for documents."""
        try:
            results = self.db_manager.search(query, top_k=top_k, namespace=namespace)
            
            if not results:
                print("No results found.")
                return
            
            print("\n" + "="*100)
            print(f"SEARCH RESULTS FOR: '{query}'")
            print("="*100)
            
            for i, result in enumerate(results, 1):
                print(f"\n{i}. Score: {result['score']:.4f} | ID: {result['id']}")
                print(f"   Document: {result['metadata'].get('document_name', 'Unknown')}")
                print(f"   Text: {result['text'][:200]}...")
                if len(result['text']) > 200:
                    print("   [Text truncated]")
                print("-" * 80)
            
        except Exception as e:
            logger.error(f"Error searching: {e}")
    
    def add_file(self, file_path: str, document_name: Optional[str] = None, 
                 namespace: str = "", chunk_size: int = 800, chunk_overlap: int = 100):
        """Add a local file to the database."""
        try:
            if not os.path.exists(file_path):
                print(f"❌ File not found: {file_path}")
                return
            
            print(f"📄 Adding file: {file_path}")
            result = self.db_manager.add_file_to_db(
                file_path=file_path,
                document_name=document_name,
                namespace=namespace,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap
            )
            
            if result['success']:
                print(f"✅ Successfully added file: {result['document_name']}")
                print(f"   Created {result['chunks_created']} chunks")
                print(f"   Upserted {result['upsert_result']['upserted_count']} vectors")
            else:
                print(f"❌ Failed to add file: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"Error adding file: {e}")
    
    def delete_document(self, document_name: str, namespace: str = ""):
        """Delete a document from the database."""
        try:
            # Confirm deletion
            response = input(f"⚠️  Are you sure you want to delete document '{document_name}'? This cannot be undone. (yes/no): ")
            if response.lower() != 'yes':
                print("Operation cancelled.")
                return
            
            result = self.db_manager.delete_document(document_name, namespace=namespace)
            
            if result['success']:
                print(f"✅ Successfully deleted document: {document_name}")
            else:
                print(f"❌ Failed to delete document: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"Error deleting document: {e}")
    
    def export_data(self, output_path: str, namespace: str = ""):
        """Export database data to a file."""
        try:
            print(f"📤 Exporting data to: {output_path}")
            result = self.db_manager.export_vectors_to_file(output_path, namespace=namespace)
            
            if result['success']:
                print(f"✅ Successfully exported {result['exported_count']} vectors")
                print(f"   Output file: {result['output_path']}")
            else:
                print(f"❌ Failed to export data: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"Error exporting data: {e}")
    
    def clear_database(self, namespace: str = ""):
        """Clear all data from the database."""
        try:
            # Confirm deletion
            ns_text = f"namespace '{namespace}'" if namespace else "entire database"
            response = input(f"⚠️  Are you sure you want to clear the {ns_text}? This cannot be undone. (yes/no): ")
            if response.lower() != 'yes':
                print("Operation cancelled.")
                return
            
            success = self.db_manager.delete_all_vectors(namespace=namespace)
            
            if success:
                print(f"✅ Successfully cleared {ns_text}")
            else:
                print(f"❌ Failed to clear {ns_text}")
                
        except Exception as e:
            logger.error(f"Error clearing database: {e}")


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Pinecone Vector Database Manager",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s list-indexes                    # List all indexes
  %(prog)s stats                          # Show current index stats
  %(prog)s list-docs                      # List all documents
  %(prog)s search "API documentation"     # Search for documents
  %(prog)s add-file ./doc.docx            # Add a file to database
  %(prog)s delete-doc "document_name"     # Delete a document
  %(prog)s export ./backup.json           # Export database
  %(prog)s clear --namespace test         # Clear test namespace
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List indexes
    subparsers.add_parser('list-indexes', help='List all available indexes')
    
    # Create index
    create_parser = subparsers.add_parser('create-index', help='Create a new index')
    create_parser.add_argument('name', help='Index name')
    create_parser.add_argument('--dimension', type=int, default=None, help='Vector dimension (default: auto-detect from embedding model)')
    create_parser.add_argument('--metric', default='cosine', help='Distance metric (default: cosine)')
    
    # Delete index
    delete_idx_parser = subparsers.add_parser('delete-index', help='Delete an index')
    delete_idx_parser.add_argument('name', help='Index name to delete')
    
    # Switch index
    switch_parser = subparsers.add_parser('switch-index', help='Switch to a different index')
    switch_parser.add_argument('name', help='Index name to switch to')
    
    # Show stats
    subparsers.add_parser('stats', help='Show index statistics')
    
    # List documents
    list_docs_parser = subparsers.add_parser('list-docs', help='List all documents')
    list_docs_parser.add_argument('--namespace', default='', help='Namespace to list from')
    
    # Search
    search_parser = subparsers.add_parser('search', help='Search for documents')
    search_parser.add_argument('query', help='Search query')
    search_parser.add_argument('--top-k', type=int, default=5, help='Number of results (default: 5)')
    search_parser.add_argument('--namespace', default='', help='Namespace to search in')
    
    # Add file
    add_file_parser = subparsers.add_parser('add-file', help='Add a file to the database')
    add_file_parser.add_argument('file_path', help='Path to the file')
    add_file_parser.add_argument('--name', help='Document name (default: filename)')
    add_file_parser.add_argument('--namespace', default='', help='Namespace for the document')
    add_file_parser.add_argument('--chunk-size', type=int, default=800, help='Chunk size (default: 800)')
    add_file_parser.add_argument('--chunk-overlap', type=int, default=100, help='Chunk overlap (default: 100)')
    
    # Delete document
    delete_doc_parser = subparsers.add_parser('delete-doc', help='Delete a document')
    delete_doc_parser.add_argument('document_name', help='Name of the document to delete')
    delete_doc_parser.add_argument('--namespace', default='', help='Namespace of the document')
    
    # Export
    export_parser = subparsers.add_parser('export', help='Export database to file')
    export_parser.add_argument('output_path', help='Output file path')
    export_parser.add_argument('--namespace', default='', help='Namespace to export')
    
    # Clear database
    clear_parser = subparsers.add_parser('clear', help='Clear database')
    clear_parser.add_argument('--namespace', default='', help='Namespace to clear (default: entire database)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize CLI manager
    cli = PineconeManagerCLI()
    
    # Execute commands
    try:
        if args.command == 'list-indexes':
            cli.list_indexes()
        elif args.command == 'create-index':
            cli.create_index(args.name, args.dimension, args.metric)
        elif args.command == 'delete-index':
            cli.delete_index(args.name)
        elif args.command == 'switch-index':
            cli.switch_index(args.name)
        elif args.command == 'stats':
            cli.show_stats()
        elif args.command == 'list-docs':
            cli.list_documents(args.namespace)
        elif args.command == 'search':
            cli.search_documents(args.query, args.top_k, args.namespace)
        elif args.command == 'add-file':
            cli.add_file(args.file_path, args.name, args.namespace, args.chunk_size, args.chunk_overlap)
        elif args.command == 'delete-doc':
            cli.delete_document(args.document_name, args.namespace)
        elif args.command == 'export':
            cli.export_data(args.output_path, args.namespace)
        elif args.command == 'clear':
            cli.clear_database(args.namespace)
        else:
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
