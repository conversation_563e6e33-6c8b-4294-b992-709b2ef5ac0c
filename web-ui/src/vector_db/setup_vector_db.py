"""
Setup script for initializing Pinecone vector database with Mindler Partner Platform document.
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv(Path(__file__).parent.parent.parent / ".env")

from src.vector_db.pinecone_client import PineconeVectorDB
from src.vector_db.document_processor import DocumentProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_vector_database():
    """Initialize vector database and upload documents."""
    try:
        logger.info("Starting vector database setup...")
        
        # Initialize components
        vector_db = PineconeVectorDB()
        doc_processor = DocumentProcessor(chunk_size=800, chunk_overlap=100)
        
        # Create index
        logger.info("Creating/connecting to Pinecone index...")
        vector_db.create_index_if_not_exists()
        
        # Process the Mindler Partner Platform document
        doc_path = project_root.parent / "vector-db-setup" / "Mindler Partner Platform.docx"
        
        if not doc_path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        
        logger.info(f"Processing document: {doc_path}")
        documents = doc_processor.process_document(
            str(doc_path), 
            "Mindler Partner Platform"
        )
        
        logger.info(f"Generated {len(documents)} document chunks")
        
        # Upload to vector database
        logger.info("Uploading documents to vector database...")
        vector_db.upsert_documents(documents)
        
        # Verify upload
        stats = vector_db.get_index_stats()
        logger.info(f"Index stats: {stats}")
        
        # Test search functionality
        logger.info("Testing search functionality...")
        test_queries = [
            "What is Mindler Partner Platform?",
            "How to integrate with the platform?",
            "What are the main features?",
            "API documentation",
            "authentication requirements"
        ]
        
        for query in test_queries:
            results = vector_db.search(query, top_k=3)
            logger.info(f"Query: '{query}' - Found {len(results)} results")
            if results:
                logger.info(f"Top result score: {results[0]['score']:.4f}")
                logger.info(f"Top result preview: {results[0]['text'][:200]}...")
        
        logger.info("Vector database setup completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error setting up vector database: {e}")
        return False


def verify_setup():
    """Verify that the vector database is working correctly."""
    try:
        logger.info("Verifying vector database setup...")
        
        vector_db = PineconeVectorDB()
        vector_db.create_index_if_not_exists()
        
        # Check index stats
        stats = vector_db.get_index_stats()
        total_vectors = stats.get('total_vector_count', 0)
        
        if total_vectors == 0:
            logger.warning("No vectors found in the index")
            return False
        
        logger.info(f"Index contains {total_vectors} vectors")
        
        # Test search with a simple query
        results = vector_db.search("platform features", top_k=1)
        
        if not results:
            logger.warning("Search returned no results")
            return False
        
        logger.info(f"Search test successful - found {len(results)} results")
        logger.info(f"Sample result: {results[0]['text'][:100]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"Error verifying setup: {e}")
        return False


def reset_database():
    """Reset the vector database by deleting all vectors."""
    try:
        logger.info("Resetting vector database...")
        
        vector_db = PineconeVectorDB()
        vector_db.create_index_if_not_exists()
        vector_db.delete_all()
        
        logger.info("Vector database reset completed")
        return True
        
    except Exception as e:
        logger.error(f"Error resetting database: {e}")
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Vector Database Setup")
    parser.add_argument(
        "--action", 
        choices=["setup", "verify", "reset"], 
        default="setup",
        help="Action to perform"
    )
    
    args = parser.parse_args()
    
    if args.action == "setup":
        success = setup_vector_database()
    elif args.action == "verify":
        success = verify_setup()
    elif args.action == "reset":
        success = reset_database()
    
    sys.exit(0 if success else 1)
