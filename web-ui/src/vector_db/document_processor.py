"""
Document processor for extracting and chunking text from various document formats.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from docx import Document
import re
import sys
from pathlib import Path

# Add project root to path for config import
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.prompt_refinement.config import RefinementConfig

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Process documents for vector database ingestion."""
    
    def __init__(self, chunk_size: Optional[int] = None, chunk_overlap: Optional[int] = None):
        """
        Initialize document processor.

        Args:
            chunk_size: Maximum size of text chunks (defaults to config value)
            chunk_overlap: Overlap between consecutive chunks (defaults to config value)
        """
        chunk_settings = RefinementConfig.get_document_chunk_settings()
        self.chunk_size = chunk_size or chunk_settings['chunk_size']
        self.chunk_overlap = chunk_overlap or chunk_settings['chunk_overlap']
    
    def extract_text_from_txt(self, file_path: str) -> str:
        """
        Extract text from a TXT file.

        Args:
            file_path: Path to the TXT file

        Returns:
            Extracted text content
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except UnicodeDecodeError:
            # Try with different encoding if UTF-8 fails
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    return file.read()
            except Exception as e:
                logger.error(f"Error reading TXT file with latin-1 encoding: {e}")
                raise
        except Exception as e:
            logger.error(f"Error extracting text from TXT: {e}")
            raise

    def extract_text_from_md(self, file_path: str) -> str:
        """
        Extract text from a Markdown file.

        Args:
            file_path: Path to the MD file

        Returns:
            Extracted text content
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            # Basic markdown processing - remove some markdown syntax
            # Keep headers but clean them up
            content = re.sub(r'^#{1,6}\s+', '', content, flags=re.MULTILINE)  # Remove header markers
            content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # Remove bold markers
            content = re.sub(r'\*(.*?)\*', r'\1', content)  # Remove italic markers
            content = re.sub(r'`(.*?)`', r'\1', content)  # Remove inline code markers
            content = re.sub(r'```.*?```', '', content, flags=re.DOTALL)  # Remove code blocks
            content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)  # Convert links to text
            content = re.sub(r'^\s*[-*+]\s+', '', content, flags=re.MULTILINE)  # Remove list markers
            content = re.sub(r'^\s*\d+\.\s+', '', content, flags=re.MULTILINE)  # Remove numbered list markers

            return content

        except Exception as e:
            logger.error(f"Error extracting text from Markdown: {e}")
            raise

    def extract_text_from_docx(self, file_path: str) -> str:
        """
        Extract text from a DOCX file.
        
        Args:
            file_path: Path to the DOCX file
            
        Returns:
            Extracted text content
        """
        try:
            doc = Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())
            
            # Also extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))
            
            return "\n\n".join(text_content)
            
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            raise
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize text content.
        
        Args:
            text: Raw text content
            
        Returns:
            Cleaned text
        """
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\"\'\/]', '', text)
        
        # Remove multiple consecutive punctuation
        text = re.sub(r'([.!?]){2,}', r'\1', text)
        
        return text.strip()
    
    def chunk_text(self, text: str) -> List[str]:
        """
        Split text into overlapping chunks.
        
        Args:
            text: Text to chunk
            
        Returns:
            List of text chunks
        """
        if len(text) <= self.chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + self.chunk_size
            
            # If we're not at the end, try to break at a sentence boundary
            if end < len(text):
                # Look for sentence endings within the last 200 characters
                sentence_end = text.rfind('.', start, end)
                if sentence_end > start + self.chunk_size // 2:
                    end = sentence_end + 1
                else:
                    # Look for word boundaries
                    word_end = text.rfind(' ', start, end)
                    if word_end > start + self.chunk_size // 2:
                        end = word_end
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position with overlap
            start = end - self.chunk_overlap
            if start >= len(text):
                break
        
        return chunks
    
    def process_document(self, file_path: str, document_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Process a document into chunks ready for vector database.
        
        Args:
            file_path: Path to the document file
            document_name: Optional name for the document
            
        Returns:
            List of document chunks with metadata
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Document not found: {file_path}")
        
        # Determine document name
        if not document_name:
            document_name = os.path.basename(file_path)
        
        # Extract text based on file extension
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == '.docx':
            raw_text = self.extract_text_from_docx(file_path)
        elif file_ext == '.txt':
            raw_text = self.extract_text_from_txt(file_path)
        elif file_ext == '.md':
            raw_text = self.extract_text_from_md(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}. Supported formats: .docx, .txt, .md")
        
        # Clean text
        cleaned_text = self.clean_text(raw_text)
        
        # Chunk text
        chunks = self.chunk_text(cleaned_text)
        
        # Create document objects
        documents = []
        for i, chunk in enumerate(chunks):
            documents.append({
                'text': chunk,
                'metadata': {
                    'document_name': document_name,
                    'chunk_index': i,
                    'total_chunks': len(chunks),
                    'file_path': file_path,
                    'file_type': file_ext
                }
            })
        
        logger.info(f"Processed document '{document_name}' into {len(documents)} chunks")
        return documents
    
    def extract_key_sections(self, text: str) -> Dict[str, str]:
        """
        Extract key sections from document text for better organization.
        
        Args:
            text: Document text
            
        Returns:
            Dictionary of section names to content
        """
        sections = {}
        
        # Common section patterns
        section_patterns = [
            r'(?i)^(introduction|overview|summary)[\s\:]*\n(.*?)(?=\n[A-Z][^a-z]*\n|\Z)',
            r'(?i)^(features?|functionality|capabilities)[\s\:]*\n(.*?)(?=\n[A-Z][^a-z]*\n|\Z)',
            r'(?i)^(requirements?|prerequisites?)[\s\:]*\n(.*?)(?=\n[A-Z][^a-z]*\n|\Z)',
            r'(?i)^(installation|setup|configuration)[\s\:]*\n(.*?)(?=\n[A-Z][^a-z]*\n|\Z)',
            r'(?i)^(usage|how to|instructions?)[\s\:]*\n(.*?)(?=\n[A-Z][^a-z]*\n|\Z)',
            r'(?i)^(api|endpoints?)[\s\:]*\n(.*?)(?=\n[A-Z][^a-z]*\n|\Z)',
            r'(?i)^(troubleshooting|issues?|problems?)[\s\:]*\n(.*?)(?=\n[A-Z][^a-z]*\n|\Z)',
        ]
        
        for pattern in section_patterns:
            matches = re.finditer(pattern, text, re.MULTILINE | re.DOTALL)
            for match in matches:
                section_name = match.group(1).lower()
                section_content = match.group(2).strip()
                if section_content:
                    sections[section_name] = section_content
        
        return sections
