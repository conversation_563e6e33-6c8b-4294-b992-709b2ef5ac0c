# 🗄️ Pinecone Vector Database Manager

A comprehensive management system for Pinecone vector databases with full CRUD operations, CLI tools, and web interface.

## 🚀 Features

### Core Capabilities
- **Full CRUD Operations**: Create, read, update, and delete vectors and documents
- **Index Management**: Create, delete, and switch between multiple indexes
- **Document Processing**: Automatic chunking and embedding of various file formats (.docx, .txt, .md)
- **Advanced Search**: Semantic search with filtering and namespace support
- **Data Export/Import**: Backup and restore your vector data
- **Namespace Support**: Organize data with namespaces
- **Batch Operations**: Efficient bulk operations for large datasets
- **Dimension Compatibility**: Automatic detection and validation of vector dimensions

### Interfaces
- **CLI Tool**: Command-line interface for all operations
- **Web Interface**: User-friendly Gradio-based web UI
- **Python API**: Programmatic access for integration

## 📋 Prerequisites

### Environment Variables
Set these in your `.env` file:

```bash
PINECONE_API_KEY=your_pinecone_api_key
GOOGLE_API_KEY=your_google_api_key
PINECONE_INDEX_NAME=your_index_name  # Optional, defaults to "ui-testing-knowledge-base-768"
PINECONE_DIMENSION=768               # Optional, defaults to 768 (must match embedding model dimension)
PINECONE_CLOUD=aws                   # Optional, defaults to "aws"
PINECONE_REGION=us-east-1           # Optional, defaults to "us-east-1"
```

### Dependencies
Install required packages:

```bash
pip install -r requirements.txt
```

## 🖥️ CLI Usage

### Basic Commands

```bash
# Show help
python -m src.vector_db.pinecone_manager --help

# Show database statistics
python -m src.vector_db.pinecone_manager stats

# List all documents
python -m src.vector_db.pinecone_manager list-docs

# Search for documents
python -m src.vector_db.pinecone_manager search "API documentation"

# Add a file to the database
python -m src.vector_db.pinecone_manager add-file ./document.docx

# Delete a document
python -m src.vector_db.pinecone_manager delete-doc "document_name"

# Export database
python -m src.vector_db.pinecone_manager export ./backup.json

# Clear database (with confirmation)
python -m src.vector_db.pinecone_manager clear
```

### Advanced Commands

```bash
# Index management
python -m src.vector_db.pinecone_manager list-indexes
python -m src.vector_db.pinecone_manager create-index "new-index" --dimension 1536
python -m src.vector_db.pinecone_manager switch-index "other-index"
python -m src.vector_db.pinecone_manager delete-index "old-index"

# Namespace operations
python -m src.vector_db.pinecone_manager list-docs --namespace "test"
python -m src.vector_db.pinecone_manager search "query" --namespace "test" --top-k 10
python -m src.vector_db.pinecone_manager add-file ./doc.docx --namespace "test"
python -m src.vector_db.pinecone_manager clear --namespace "test"

# Custom chunking
python -m src.vector_db.pinecone_manager add-file ./doc.docx --chunk-size 1000 --chunk-overlap 200
```

## 🌐 Web Interface

Start the web interface and navigate to the "🗄️ Vector DB Manager" tab:

```bash
python src/webui/interface.py
```

### Web Interface Features
- **Overview**: View database statistics and connection status
- **Documents**: Browse and manage all documents
- **Search**: Interactive search with real-time results
- **Add Files**: Upload files with custom settings
- **Delete**: Remove documents or clear database
- **Export**: Download database backups

## 🐍 Python API

### Basic Usage

```python
from src.vector_db.pinecone_client import PineconeVectorDBManager

# Initialize manager
manager = PineconeVectorDBManager()

# Get database statistics
stats = manager.get_index_stats()
print(f"Total vectors: {stats['total_vector_count']}")

# List all documents
documents = manager.get_document_list()
for doc in documents:
    print(f"Document: {doc['document_name']} ({doc['chunk_count']} chunks)")

# Search for documents
results = manager.search("API documentation", top_k=5)
for result in results:
    print(f"Score: {result['score']:.4f} - {result['text'][:100]}...")

# Add a file
result = manager.add_file_to_db("./document.docx", document_name="My Document")
if result['success']:
    print(f"Added {result['chunks_created']} chunks")

# Delete a document
result = manager.delete_document("My Document")
if result['success']:
    print("Document deleted successfully")
```

### Advanced Operations

```python
# Index management
indexes = manager.list_indexes()
manager.create_index("new-index", dimension=1536)
manager.switch_index("new-index")
manager.delete_index("old-index")

# Namespace operations
documents = manager.get_document_list(namespace="test")
results = manager.search("query", namespace="test")
manager.delete_all_vectors(namespace="test")

# Custom document processing
documents = [
    {
        'text': 'Your document text here',
        'metadata': {
            'document_name': 'Custom Doc',
            'category': 'important'
        }
    }
]
result = manager.upsert_documents(documents, namespace="custom")

# Export data
result = manager.export_vectors_to_file("./backup.json", namespace="test")
```

## 🔧 Configuration

### Chunking Settings
Customize how documents are split into chunks:

```python
# Default settings
chunk_size = 800      # Characters per chunk
chunk_overlap = 100   # Overlap between chunks

# For technical documents (larger chunks)
chunk_size = 1200
chunk_overlap = 200

# For short content (smaller chunks)
chunk_size = 400
chunk_overlap = 50
```

### Search Settings
Optimize search performance:

```python
# Basic search
results = manager.search("query", top_k=5)

# Advanced search with filtering
results = manager.search(
    "query",
    top_k=10,
    filter_dict={"document_name": "specific_doc"},
    namespace="production"
)
```

## 🔄 Migration from Old Setup

If you were using the old `setup_vector_db.py`, run the migration script:

```bash
python -m src.vector_db.migrate_to_new_manager
```

This script will:
- Check your environment configuration
- Test connection to Pinecone
- Verify existing data compatibility
- Show usage examples for the new system

## 📊 Monitoring and Maintenance

### Regular Maintenance Tasks

```bash
# Check database health
python -m src.vector_db.pinecone_manager stats

# Backup your data
python -m src.vector_db.pinecone_manager export ./backups/backup_$(date +%Y%m%d).json

# Clean up test data
python -m src.vector_db.pinecone_manager clear --namespace "test"
```

### Performance Tips
1. **Batch Operations**: Use batch upserts for large datasets
2. **Namespace Organization**: Use namespaces to organize different data types
3. **Regular Backups**: Export data regularly for disaster recovery
4. **Monitor Usage**: Check index stats to track growth and usage

## 🐛 Troubleshooting

### Common Issues

**Dimension Mismatch Error**
```bash
# Check dimension compatibility
python -m src.vector_db.check_dimension_compatibility

# The error looks like: "Vector dimension 768 does not match the dimension of the index 512"
# This happens when your embedding model produces different dimensions than your index expects
```

**Connection Errors**
```bash
# Check environment variables
python -c "import os; print('PINECONE_API_KEY:', bool(os.getenv('PINECONE_API_KEY')))"

# Test connection
python -m src.vector_db.migrate_to_new_manager
```

**Search Returns No Results**
- Check if documents exist: `python -m src.vector_db.pinecone_manager list-docs`
- Verify namespace: Use `--namespace` flag if needed
- Try broader search terms

**File Upload Fails**
- Check file format (supports .docx, .txt, .md)
- Verify file path exists
- Check file permissions
- Run dimension compatibility check if you get dimension errors

### Debug Mode
Enable detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🤝 Contributing

To extend the manager:

1. **Add New File Formats**: Extend `DocumentProcessor` class
2. **Custom Embeddings**: Modify `get_embedding()` method
3. **New CLI Commands**: Add to `pinecone_manager.py`
4. **Web Interface Features**: Extend `vector_db_manager_tab.py`

## 📝 License

This project is part of the UI Testing Backend system. See the main project license for details.
