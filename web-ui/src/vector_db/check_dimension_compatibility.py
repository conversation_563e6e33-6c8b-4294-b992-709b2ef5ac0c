#!/usr/bin/env python3
"""
Dimension Compatibility Checker for Pinecone Vector Database
This script helps diagnose and fix dimension mismatch issues.
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv(project_root / ".env")

from src.vector_db.pinecone_client import PineconeVectorDBManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_embedding_dimension():
    """Check the actual dimension of embeddings from the model."""
    try:
        print("🔍 Checking embedding model dimension...")
        
        # Create a temporary manager just to test embeddings
        import google.generativeai as genai
        
        google_api_key = os.getenv("GOOGLE_API_KEY")
        if not google_api_key:
            print("❌ GOOGLE_API_KEY not found in environment variables")
            return None
        
        genai.configure(api_key=google_api_key)
        
        # Test embedding
        result = genai.embed_content(
            model="models/embedding-001",
            content="test text for dimension check",
            task_type="retrieval_document"
        )
        
        embedding_dimension = len(result['embedding'])
        print(f"✅ Embedding model produces {embedding_dimension}-dimensional vectors")
        return embedding_dimension
        
    except Exception as e:
        print(f"❌ Error checking embedding dimension: {e}")
        return None


def check_index_dimension():
    """Check the dimension of the current Pinecone index."""
    try:
        print("🔍 Checking Pinecone index dimension...")
        
        manager = PineconeVectorDBManager()
        stats = manager.get_index_stats()
        
        index_dimension = stats['dimension']
        print(f"✅ Pinecone index '{manager.index_name}' has {index_dimension} dimensions")
        return index_dimension, manager.index_name
        
    except Exception as e:
        print(f"❌ Error checking index dimension: {e}")
        return None, None


def suggest_fixes(embedding_dim, index_dim, index_name):
    """Suggest fixes for dimension mismatch."""
    print("\n" + "="*60)
    print("🔧 SUGGESTED FIXES")
    print("="*60)
    
    if embedding_dim == index_dim:
        print("✅ No dimension mismatch detected! Your setup should work correctly.")
        return
    
    print(f"⚠️  DIMENSION MISMATCH DETECTED:")
    print(f"   - Embedding model: {embedding_dim} dimensions")
    print(f"   - Pinecone index:  {index_dim} dimensions")
    print()
    
    print("🔧 Option 1: Create a new index with correct dimension")
    print(f"   python -m src.vector_db.pinecone_manager create-index 'ui-testing-knowledge-base-{embedding_dim}'")
    print(f"   python -m src.vector_db.pinecone_manager switch-index 'ui-testing-knowledge-base-{embedding_dim}'")
    print()
    
    print("🔧 Option 2: Update environment variables")
    print("   Add to your .env file:")
    print(f"   PINECONE_INDEX_NAME=ui-testing-knowledge-base-{embedding_dim}")
    print(f"   PINECONE_DIMENSION={embedding_dim}")
    print()
    
    print("🔧 Option 3: Use a different embedding model")
    print("   (This would require code changes to use a model that produces")
    print(f"   {index_dim}-dimensional vectors)")
    print()
    
    print("⚠️  IMPORTANT: If you have existing data in the current index,")
    print("   you'll need to export it first, then re-import after fixing the dimension.")
    print(f"   python -m src.vector_db.pinecone_manager export ./backup_{index_name}.json")


def check_environment_variables():
    """Check relevant environment variables."""
    print("\n🔍 Checking environment variables...")
    
    vars_to_check = [
        "PINECONE_API_KEY",
        "GOOGLE_API_KEY", 
        "PINECONE_INDEX_NAME",
        "PINECONE_DIMENSION"
    ]
    
    for var in vars_to_check:
        value = os.getenv(var)
        if value:
            if var in ["PINECONE_API_KEY", "GOOGLE_API_KEY"]:
                # Don't show full API keys
                display_value = f"{value[:8]}...{value[-4:]}" if len(value) > 12 else "***"
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: Not set")


def main():
    """Main function to check dimension compatibility."""
    print("🔍 PINECONE DIMENSION COMPATIBILITY CHECKER")
    print("="*50)
    print("This script helps diagnose dimension mismatch issues.")
    print()
    
    # Check environment variables
    check_environment_variables()
    
    # Check embedding dimension
    embedding_dim = check_embedding_dimension()
    if embedding_dim is None:
        print("❌ Cannot proceed without embedding dimension information")
        return False
    
    # Check index dimension
    index_dim, index_name = check_index_dimension()
    if index_dim is None:
        print("❌ Cannot proceed without index dimension information")
        return False
    
    # Suggest fixes if needed
    suggest_fixes(embedding_dim, index_dim, index_name)
    
    # Final recommendations
    print("\n" + "="*60)
    print("📋 NEXT STEPS")
    print("="*60)
    
    if embedding_dim == index_dim:
        print("1. Your setup is correct! Try adding documents again.")
        print("2. If you still get errors, check your API keys and network connection.")
    else:
        print("1. Choose one of the suggested fixes above")
        print("2. If you have existing data, export it first")
        print("3. Create/switch to a compatible index")
        print("4. Re-import your data if needed")
        print("5. Test with a small document first")
    
    print("\n🔧 Test your fix with:")
    print("   python -m src.vector_db.pinecone_manager stats")
    print("   python -m src.vector_db.pinecone_manager add-file ./test_document.txt")
    
    return embedding_dim == index_dim


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nCheck cancelled by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
