"""
Pinecone Vector Database Manager with Comprehensive CRUD Operations
"""

import os
import logging
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
import google.generativeai as genai
from pinecone import Pinecone, ServerlessSpec
import time
import hashlib

logger = logging.getLogger(__name__)


class PineconeVectorDBManager:
    """Comprehensive Pinecone vector database manager with full CRUD operations."""

    def __init__(self):
        """Initialize Pinecone client and Gemini embeddings."""
        # Initialize Pinecone
        self.pinecone_api_key = os.getenv("PINECONE_API_KEY")
        if not self.pinecone_api_key:
            raise ValueError("PINECONE_API_KEY environment variable is required")

        self.pc = Pinecone(api_key=self.pinecone_api_key)

        # Initialize Gemini
        self.google_api_key = os.getenv("GOOGLE_API_KEY")
        if not self.google_api_key:
            raise ValueError("GOOGLE_API_KEY environment variable is required")

        genai.configure(api_key=self.google_api_key)

        # Index configuration - can be customized via environment variables
        self.index_name = os.getenv("PINECONE_INDEX_NAME", "ui-testing-knowledge-base-768")
        self.dimension = int(os.getenv("PINECONE_DIMENSION", "768"))  # Must match embedding model dimension
        self.metric = "cosine"
        self.cloud = os.getenv("PINECONE_CLOUD", "aws")
        self.region = os.getenv("PINECONE_REGION", "us-east-1")

        # Validate dimension compatibility
        self._validate_dimension_compatibility()

        self.index = None
        self._connect_to_index()

    def _validate_dimension_compatibility(self):
        """Validate that the configured dimension matches the embedding model."""
        try:
            # Test embedding to get actual dimension
            test_embedding = self.get_embedding("test")
            actual_dimension = len(test_embedding)

            if actual_dimension != self.dimension:
                logger.warning(
                    f"Dimension mismatch detected! "
                    f"Embedding model produces {actual_dimension}D vectors, "
                    f"but index is configured for {self.dimension}D. "
                    f"This will cause upsert failures."
                )

                # Auto-correct the dimension to match the embedding model
                logger.info(f"Auto-correcting dimension from {self.dimension} to {actual_dimension}")
                self.dimension = actual_dimension

        except Exception as e:
            logger.warning(f"Could not validate dimension compatibility: {e}")
            logger.info("Proceeding with configured dimension. If upserts fail, check dimension settings.")

    def _connect_to_index(self):
        """Connect to existing index or create if it doesn't exist."""
        try:
            # Check if index exists
            existing_indexes = [index.name for index in self.pc.list_indexes()]

            if self.index_name not in existing_indexes:
                logger.info(f"Creating index: {self.index_name}")
                self.pc.create_index(
                    name=self.index_name,
                    dimension=self.dimension,
                    metric=self.metric,
                    spec=ServerlessSpec(
                        cloud=self.cloud,
                        region=self.region
                    )
                )

                # Wait for index to be ready
                while not self.pc.describe_index(self.index_name).status['ready']:
                    logger.info("Waiting for index to be ready...")
                    time.sleep(1)

            self.index = self.pc.Index(self.index_name)
            logger.info(f"Connected to index: {self.index_name}")

        except Exception as e:
            logger.error(f"Error creating/connecting to index: {e}")
            raise

    # ===== BACKWARD COMPATIBILITY =====

    def create_index_if_not_exists(self):
        """
        Backward compatibility method for create_index_if_not_exists.
        This method is deprecated, use _connect_to_index() instead.
        """
        logger.warning("create_index_if_not_exists() is deprecated. The index connection is now automatic.")
        if not self.index:
            self._connect_to_index()

    # ===== INDEX MANAGEMENT =====

    def list_indexes(self) -> List[Dict[str, Any]]:
        """List all available indexes."""
        try:
            indexes = []
            for index_info in self.pc.list_indexes():
                index_details = {
                    'name': index_info.name,
                    'dimension': index_info.dimension,
                    'metric': index_info.metric,
                    'host': index_info.host,
                    'status': index_info.status
                }
                indexes.append(index_details)
            return indexes
        except Exception as e:
            logger.error(f"Error listing indexes: {e}")
            raise

    def create_index(self, name: str, dimension: int = None, metric: str = "cosine") -> bool:
        """Create a new index."""
        try:
            # Use embedding model dimension if not specified
            if dimension is None:
                test_embedding = self.get_embedding("test")
                dimension = len(test_embedding)
                logger.info(f"Using embedding model dimension: {dimension}")

            existing_indexes = [index.name for index in self.pc.list_indexes()]

            if name in existing_indexes:
                logger.warning(f"Index '{name}' already exists")
                return False

            self.pc.create_index(
                name=name,
                dimension=dimension,
                metric=metric,
                spec=ServerlessSpec(
                    cloud=self.cloud,
                    region=self.region
                )
            )

            # Wait for index to be ready
            while not self.pc.describe_index(name).status['ready']:
                logger.info(f"Waiting for index '{name}' to be ready...")
                time.sleep(1)

            logger.info(f"Successfully created index: {name}")
            return True

        except Exception as e:
            logger.error(f"Error creating index '{name}': {e}")
            raise

    def delete_index(self, name: str) -> bool:
        """Delete an index."""
        try:
            existing_indexes = [index.name for index in self.pc.list_indexes()]

            if name not in existing_indexes:
                logger.warning(f"Index '{name}' does not exist")
                return False

            self.pc.delete_index(name)
            logger.info(f"Successfully deleted index: {name}")
            return True

        except Exception as e:
            logger.error(f"Error deleting index '{name}': {e}")
            raise

    def switch_index(self, name: str) -> bool:
        """Switch to a different index."""
        try:
            existing_indexes = [index.name for index in self.pc.list_indexes()]

            if name not in existing_indexes:
                logger.error(f"Index '{name}' does not exist")
                return False

            self.index_name = name
            self.index = self.pc.Index(name)
            logger.info(f"Switched to index: {name}")
            return True

        except Exception as e:
            logger.error(f"Error switching to index '{name}': {e}")
            raise

    # ===== EMBEDDING OPERATIONS =====

    def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using Gemini."""
        try:
            result = genai.embed_content(
                model="models/embedding-001",
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Error getting embedding: {e}")
            raise

    def generate_id(self, text: str, prefix: str = "") -> str:
        """Generate a unique ID for text content."""
        content_hash = hashlib.md5(text.encode()).hexdigest()
        if prefix:
            return f"{prefix}_{content_hash}"
        return content_hash

    # ===== DOCUMENT MANAGEMENT =====

    def get_index_stats(self) -> Dict[str, Any]:
        """Get comprehensive index statistics."""
        if not self.index:
            raise ValueError("No index connected")

        try:
            stats = self.index.describe_index_stats()

            # Add additional metadata
            enhanced_stats = {
                'index_name': self.index_name,
                'total_vector_count': stats.get('total_vector_count', 0),
                'dimension': stats.get('dimension', self.dimension),
                'index_fullness': stats.get('index_fullness', 0.0),
                'namespaces': stats.get('namespaces', {}),
                'timestamp': datetime.now().isoformat()
            }

            return enhanced_stats

        except Exception as e:
            logger.error(f"Error getting index stats: {e}")
            raise

    def list_vectors(self, namespace: str = "", limit: int = 100, prefix: str = "") -> List[Dict[str, Any]]:
        """List vectors in the index with their metadata."""
        if not self.index:
            raise ValueError("No index connected")

        try:
            # Note: Pinecone doesn't have a direct "list all vectors" API
            # This is a workaround using query with a dummy vector
            dummy_vector = [0.0] * self.dimension

            results = self.index.query(
                vector=dummy_vector,
                top_k=limit,
                include_metadata=True,
                namespace=namespace,
                filter={"document_name": {"$exists": True}} if not prefix else {"document_name": {"$regex": f"^{prefix}"}}
            )

            vectors = []
            for match in results.get('matches', []):
                vector_info = {
                    'id': match['id'],
                    'score': match.get('score', 0.0),
                    'metadata': match.get('metadata', {}),
                    'namespace': namespace or 'default'
                }
                vectors.append(vector_info)

            return vectors

        except Exception as e:
            logger.error(f"Error listing vectors: {e}")
            return []

    def get_vector_by_id(self, vector_id: str, namespace: str = "") -> Optional[Dict[str, Any]]:
        """Retrieve a specific vector by ID."""
        if not self.index:
            raise ValueError("No index connected")

        try:
            result = self.index.fetch(ids=[vector_id], namespace=namespace)

            if vector_id in result.get('vectors', {}):
                vector_data = result['vectors'][vector_id]
                return {
                    'id': vector_id,
                    'values': vector_data.get('values', []),
                    'metadata': vector_data.get('metadata', {}),
                    'namespace': namespace or 'default'
                }

            return None

        except Exception as e:
            logger.error(f"Error fetching vector '{vector_id}': {e}")
            return None

    def delete_vectors(self, vector_ids: List[str], namespace: str = "") -> bool:
        """Delete specific vectors by their IDs."""
        if not self.index:
            raise ValueError("No index connected")

        try:
            self.index.delete(ids=vector_ids, namespace=namespace)
            logger.info(f"Deleted {len(vector_ids)} vectors from namespace '{namespace or 'default'}'")
            return True

        except Exception as e:
            logger.error(f"Error deleting vectors: {e}")
            return False

    def delete_by_filter(self, filter_dict: Dict[str, Any], namespace: str = "") -> bool:
        """Delete vectors matching a metadata filter."""
        if not self.index:
            raise ValueError("No index connected")

        try:
            self.index.delete(filter=filter_dict, namespace=namespace)
            logger.info(f"Deleted vectors matching filter {filter_dict} from namespace '{namespace or 'default'}'")
            return True

        except Exception as e:
            logger.error(f"Error deleting vectors by filter: {e}")
            return False

    def delete_all_vectors(self, namespace: str = "") -> bool:
        """Delete all vectors from the index or namespace."""
        if not self.index:
            raise ValueError("No index connected")

        try:
            if namespace:
                self.index.delete(delete_all=True, namespace=namespace)
                logger.info(f"Deleted all vectors from namespace '{namespace}'")
            else:
                self.index.delete(delete_all=True)
                logger.info("Deleted all vectors from index")
            return True

        except Exception as e:
            logger.error(f"Error deleting all vectors: {e}")
            return False

    def upsert_documents(self, documents: List[Dict[str, Any]], namespace: str = "", batch_size: int = 100) -> Dict[str, Any]:
        """
        Upsert documents to the vector database.

        Args:
            documents: List of documents with 'text' and 'metadata' keys
            namespace: Optional namespace for the vectors
            batch_size: Number of vectors to upsert in each batch

        Returns:
            Dictionary with upsert results and statistics
        """
        if not self.index:
            raise ValueError("No index connected")

        if not documents:
            return {'success': False, 'error': 'No documents provided'}

        vectors = []
        failed_docs = []

        for i, doc in enumerate(documents):
            try:
                text = doc['text']
                metadata = doc.get('metadata', {})
                custom_id = doc.get('id')  # Allow custom IDs

                logger.info(f"Processing document {i+1}/{len(documents)}: {text[:100]}...")

                # Generate embedding
                embedding = self.get_embedding(text)
                logger.info(f"Generated embedding with dimension: {len(embedding)}")

                # Generate or use custom ID
                if custom_id:
                    doc_id = custom_id
                else:
                    doc_id = self.generate_id(text, metadata.get('document_name', ''))

                logger.info(f"Using ID: {doc_id}")

                # Add timestamp and processing info to metadata
                enhanced_metadata = {
                    'text': text,
                    'created_at': datetime.now().isoformat(),
                    'text_length': len(text),
                    **metadata
                }

                vectors.append({
                    'id': doc_id,
                    'values': embedding,
                    'metadata': enhanced_metadata
                })

            except Exception as e:
                logger.error(f"Failed to process document {i+1}: {e}")
                failed_docs.append({'index': i, 'error': str(e), 'text': doc.get('text', '')[:100]})

        if not vectors:
            return {'success': False, 'error': 'No valid documents to upsert', 'failed_docs': failed_docs}

        logger.info(f"Created {len(vectors)} vectors for upsert")

        # Upsert in batches
        upserted_count = 0
        failed_batches = []

        for i in range(0, len(vectors), batch_size):
            batch = vectors[i:i + batch_size]
            batch_num = i//batch_size + 1
            total_batches = (len(vectors)-1)//batch_size + 1

            try:
                logger.info(f"Upserting batch {batch_num}/{total_batches} with {len(batch)} vectors")
                response = self.index.upsert(vectors=batch, namespace=namespace)
                upserted_count += response.get('upserted_count', len(batch))
                logger.info(f"Successfully upserted batch {batch_num}/{total_batches}")

            except Exception as e:
                logger.error(f"Failed to upsert batch {batch_num}: {e}")
                failed_batches.append({'batch': batch_num, 'error': str(e)})

        # Wait for eventual consistency
        logger.info("Waiting for vectors to be indexed...")
        time.sleep(2)

        return {
            'success': len(failed_batches) == 0,
            'total_documents': len(documents),
            'processed_vectors': len(vectors),
            'upserted_count': upserted_count,
            'failed_docs': failed_docs,
            'failed_batches': failed_batches,
            'namespace': namespace or 'default'
        }

    def search(self, query: str, top_k: int = 5, filter_dict: Optional[Dict] = None,
               namespace: str = "", include_values: bool = False) -> List[Dict[str, Any]]:
        """
        Search for similar documents.

        Args:
            query: Search query
            top_k: Number of results to return
            filter_dict: Optional metadata filter
            namespace: Optional namespace to search in
            include_values: Whether to include vector values in results

        Returns:
            List of matching documents with scores
        """
        if not self.index:
            raise ValueError("No index connected")

        try:
            # Get query embedding
            query_embedding = self.get_embedding(query)

            # Search
            results = self.index.query(
                vector=query_embedding,
                top_k=top_k,
                include_metadata=True,
                include_values=include_values,
                filter=filter_dict,
                namespace=namespace
            )

            # Format results
            formatted_results = []
            for match in results.get('matches', []):
                result = {
                    'id': match['id'],
                    'score': match['score'],
                    'text': match['metadata'].get('text', ''),
                    'metadata': {k: v for k, v in match['metadata'].items() if k != 'text'},
                    'namespace': namespace or 'default'
                }

                if include_values:
                    result['values'] = match.get('values', [])

                formatted_results.append(result)

            return formatted_results

        except Exception as e:
            logger.error(f"Error searching: {e}")
            raise

    # ===== FILE OPERATIONS =====

    def add_file_to_db(self, file_path: str, document_name: Optional[str] = None,
                       namespace: str = "", chunk_size: int = 800, chunk_overlap: int = 100) -> Dict[str, Any]:
        """
        Add a local file to the vector database.

        Args:
            file_path: Path to the local file
            document_name: Optional name for the document
            namespace: Optional namespace for the vectors
            chunk_size: Size of text chunks
            chunk_overlap: Overlap between chunks

        Returns:
            Dictionary with operation results
        """
        try:
            from .document_processor import DocumentProcessor

            if not os.path.exists(file_path):
                return {'success': False, 'error': f'File not found: {file_path}'}

            # Initialize document processor
            doc_processor = DocumentProcessor(chunk_size=chunk_size, chunk_overlap=chunk_overlap)

            # Process the document
            documents = doc_processor.process_document(file_path, document_name)

            if not documents:
                return {'success': False, 'error': 'No content extracted from file'}

            # Add file metadata
            for doc in documents:
                doc['metadata']['source_file'] = file_path
                doc['metadata']['added_at'] = datetime.now().isoformat()

            # Upsert to database
            result = self.upsert_documents(documents, namespace=namespace)

            return {
                'success': result['success'],
                'file_path': file_path,
                'document_name': document_name or os.path.basename(file_path),
                'chunks_created': len(documents),
                'upsert_result': result
            }

        except Exception as e:
            logger.error(f"Error adding file to database: {e}")
            return {'success': False, 'error': str(e)}

    def export_vectors_to_file(self, output_path: str, namespace: str = "",
                              filter_dict: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Export vectors to a JSON file.

        Args:
            output_path: Path for the output file
            namespace: Optional namespace to export from
            filter_dict: Optional filter for vectors to export

        Returns:
            Dictionary with export results
        """
        try:
            # Get all vectors (this is a simplified approach)
            vectors = self.list_vectors(namespace=namespace, limit=10000)

            if filter_dict:
                # Apply additional filtering if needed
                filtered_vectors = []
                for vector in vectors:
                    metadata = vector.get('metadata', {})
                    if all(metadata.get(k) == v for k, v in filter_dict.items()):
                        filtered_vectors.append(vector)
                vectors = filtered_vectors

            # Prepare export data
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'index_name': self.index_name,
                'namespace': namespace or 'default',
                'total_vectors': len(vectors),
                'vectors': vectors
            }

            # Write to file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Exported {len(vectors)} vectors to {output_path}")

            return {
                'success': True,
                'output_path': output_path,
                'exported_count': len(vectors),
                'namespace': namespace or 'default'
            }

        except Exception as e:
            logger.error(f"Error exporting vectors: {e}")
            return {'success': False, 'error': str(e)}

    # ===== UTILITY METHODS =====

    def get_document_list(self, namespace: str = "") -> List[Dict[str, Any]]:
        """Get a list of unique documents in the database."""
        try:
            vectors = self.list_vectors(namespace=namespace, limit=10000)

            # Group by document name
            documents = {}
            for vector in vectors:
                metadata = vector.get('metadata', {})
                doc_name = metadata.get('document_name', 'Unknown')

                if doc_name not in documents:
                    documents[doc_name] = {
                        'document_name': doc_name,
                        'chunk_count': 0,
                        'total_length': 0,
                        'created_at': metadata.get('created_at'),
                        'source_file': metadata.get('source_file'),
                        'file_type': metadata.get('file_type'),
                        'namespace': namespace or 'default'
                    }

                documents[doc_name]['chunk_count'] += 1
                documents[doc_name]['total_length'] += metadata.get('text_length', 0)

            return list(documents.values())

        except Exception as e:
            logger.error(f"Error getting document list: {e}")
            return []

    def delete_document(self, document_name: str, namespace: str = "") -> Dict[str, Any]:
        """Delete all chunks of a specific document."""
        try:
            # Delete by document name filter
            filter_dict = {"document_name": document_name}
            success = self.delete_by_filter(filter_dict, namespace=namespace)

            return {
                'success': success,
                'document_name': document_name,
                'namespace': namespace or 'default'
            }

        except Exception as e:
            logger.error(f"Error deleting document '{document_name}': {e}")
            return {'success': False, 'error': str(e)}


# Backward compatibility alias
PineconeVectorDB = PineconeVectorDBManager
