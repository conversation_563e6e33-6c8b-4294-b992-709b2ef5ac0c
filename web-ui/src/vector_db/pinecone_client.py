"""
Pinecone Vector Database Client with Gemini Embeddings
"""

import os
import logging
from typing import List, Dict, Any, Optional
import google.generativeai as genai
from pinecone import Pinecone, ServerlessSpec
import time
import hashlib

logger = logging.getLogger(__name__)


class PineconeVectorDB:
    """Pinecone vector database client with Gemini embeddings."""
    
    def __init__(self):
        """Initialize Pinecone client and Gemini embeddings."""
        # Initialize Pinecone
        self.pinecone_api_key = os.getenv("PINECONE_API_KEY")
        if not self.pinecone_api_key:
            raise ValueError("PINECONE_API_KEY environment variable is required")
        
        self.pc = Pinecone(api_key=self.pinecone_api_key)
        
        # Initialize Gemini
        self.google_api_key = os.getenv("GOOGLE_API_KEY")
        if not self.google_api_key:
            raise ValueError("GOOGLE_API_KEY environment variable is required")
        
        genai.configure(api_key=self.google_api_key)
        
        # Index configuration
        self.index_name = "mindler-partner-platform"
        self.dimension = 768  # Gemini embedding dimension
        self.metric = "cosine"
        self.cloud = "aws"
        self.region = "us-east-1"
        
        self.index = None
        
    def create_index_if_not_exists(self):
        """Create Pinecone index if it doesn't exist."""
        try:
            # Check if index exists
            existing_indexes = [index.name for index in self.pc.list_indexes()]
            
            if self.index_name not in existing_indexes:
                logger.info(f"Creating index: {self.index_name}")
                self.pc.create_index(
                    name=self.index_name,
                    dimension=self.dimension,
                    metric=self.metric,
                    spec=ServerlessSpec(
                        cloud=self.cloud,
                        region=self.region
                    )
                )
                
                # Wait for index to be ready
                while not self.pc.describe_index(self.index_name).status['ready']:
                    logger.info("Waiting for index to be ready...")
                    time.sleep(1)
                    
            self.index = self.pc.Index(self.index_name)
            logger.info(f"Connected to index: {self.index_name}")
            
        except Exception as e:
            logger.error(f"Error creating/connecting to index: {e}")
            raise
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using Gemini."""
        try:
            result = genai.embed_content(
                model="models/embedding-001",
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Error getting embedding: {e}")
            raise
    
    def generate_id(self, text: str) -> str:
        """Generate a unique ID for text content."""
        return hashlib.md5(text.encode()).hexdigest()
    
    def upsert_documents(self, documents: List[Dict[str, Any]]):
        """
        Upsert documents to the vector database.
        
        Args:
            documents: List of documents with 'text' and 'metadata' keys
        """
        if not self.index:
            self.create_index_if_not_exists()
        
        vectors = []
        for i, doc in enumerate(documents):
            text = doc['text']
            metadata = doc.get('metadata', {})

            logger.info(f"Processing document {i+1}/{len(documents)}: {text[:100]}...")

            # Generate embedding
            embedding = self.get_embedding(text)
            logger.info(f"Generated embedding with dimension: {len(embedding)}")

            # Generate unique ID
            doc_id = self.generate_id(text)
            logger.info(f"Generated ID: {doc_id}")

            vectors.append({
                'id': doc_id,
                'values': embedding,
                'metadata': {
                    'text': text,
                    **metadata
                }
            })

        logger.info(f"Created {len(vectors)} vectors for upsert")
        
        # Upsert in batches
        batch_size = 100
        for i in range(0, len(vectors), batch_size):
            batch = vectors[i:i + batch_size]
            logger.info(f"Upserting batch {i//batch_size + 1}/{(len(vectors)-1)//batch_size + 1} with {len(batch)} vectors")
            response = self.index.upsert(vectors=batch)
            logger.info(f"Upsert response: {response}")
            logger.info(f"Upserted batch {i//batch_size + 1}/{(len(vectors)-1)//batch_size + 1}")

        # Wait a bit for eventual consistency
        logger.info("Waiting for vectors to be indexed...")
        time.sleep(5)
    
    def search(self, query: str, top_k: int = 5, filter_dict: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        Search for similar documents.
        
        Args:
            query: Search query
            top_k: Number of results to return
            filter_dict: Optional metadata filter
            
        Returns:
            List of matching documents with scores
        """
        if not self.index:
            self.create_index_if_not_exists()
        
        # Get query embedding
        query_embedding = self.get_embedding(query)
        
        # Search
        results = self.index.query(
            vector=query_embedding,
            top_k=top_k,
            include_metadata=True,
            filter=filter_dict
        )
        
        # Format results
        formatted_results = []
        for match in results['matches']:
            formatted_results.append({
                'id': match['id'],
                'score': match['score'],
                'text': match['metadata']['text'],
                'metadata': {k: v for k, v in match['metadata'].items() if k != 'text'}
            })
        
        return formatted_results
    
    def get_index_stats(self) -> Dict[str, Any]:
        """Get index statistics."""
        if not self.index:
            self.create_index_if_not_exists()
        
        return self.index.describe_index_stats()
    
    def delete_all(self):
        """Delete all vectors from the index."""
        if not self.index:
            self.create_index_if_not_exists()
        
        self.index.delete(delete_all=True)
        logger.info("Deleted all vectors from index")
