from typing import List, Optional, Dict, Any, cast
from datetime import datetime
from uuid import uuid4
import json
from ..models.schemas import Message, MessageCreate, MessageUpdate, MessageRole
from ..helpers.redis_client import get_redis_client


class MessageRepository:
    """Redis-backed message repository."""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self.MESSAGE_SET_KEY = "messages"
    
    def create_message(self, message_data: MessageCreate) -> Message:
        """Create a new message."""
        message_id = str(uuid4())
        message_dict = {
            "message_id": message_id,
            "chat_id": message_data.chat_id,
            "content": message_data.content,
            "role": message_data.role.value,
            "metadata": json.dumps(message_data.metadata) if message_data.metadata else "{}",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Store message in Redis
        self.redis_client.hset(f"message:{message_id}", mapping=message_dict)
        self.redis_client.sadd(self.MESSAGE_SET_KEY, message_id)
        self.redis_client.sadd(f"chat:{message_data.chat_id}:messages", message_id)
        
        # Update chat message count
        from ..repository.chat_repository import chat_repository
        chat_repository.increment_message_count(message_data.chat_id)
        
        return Message(**self._convert_message_dict(message_dict))
    
    def get_message(self, message_id: str) -> Optional[Message]:
        """Get a message by ID."""
        message_dict = cast(Dict[str, str], self.redis_client.hgetall(f"message:{message_id}"))
        if message_dict:
            return Message(**self._convert_message_dict(message_dict))
        return None
    
    def get_chat_messages(self, chat_id: str, skip: int = 0, limit: int = 100) -> List[Message]:
        """Get all messages in a chat."""
        message_ids = cast(set, self.redis_client.smembers(f"chat:{chat_id}:messages"))
        messages = [self.get_message(message_id) for message_id in message_ids]
        messages = [m for m in messages if m is not None]
        
        # Sort by created_at (oldest first for conversation flow)
        messages.sort(key=lambda x: x.created_at)
        return messages[skip:skip + limit]
    
    def update_message(self, message_id: str, message_update: MessageUpdate) -> Optional[Message]:
        """Update a message."""
        message_key = f"message:{message_id}"
        if not self.redis_client.exists(message_key):
            return None
        
        message_dict = cast(Dict[str, str], self.redis_client.hgetall(message_key))
        update_data = message_update.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            if value is not None:
                if field == "metadata":
                    message_dict[field] = json.dumps(value)
                else:
                    message_dict[field] = str(value)
        
        message_dict["updated_at"] = datetime.now().isoformat()
        self.redis_client.hset(message_key, mapping=message_dict)
        
        return Message(**self._convert_message_dict(message_dict))
    
    def delete_message(self, message_id: str) -> bool:
        """Delete a message."""
        message_key = f"message:{message_id}"
        if not self.redis_client.exists(message_key):
            return False
        
        # Get message details before deletion
        message = self.get_message(message_id)
        if not message:
            return False
        
        # Remove from chat's message set
        self.redis_client.srem(f"chat:{message.chat_id}:messages", message_id)
        
        # Delete the message itself
        self.redis_client.delete(message_key)
        self.redis_client.srem(self.MESSAGE_SET_KEY, message_id)
        
        # Update chat message count
        from ..repository.chat_repository import chat_repository
        chat_repository.decrement_message_count(message.chat_id)
        
        return True
    
    def delete_chat_messages(self, chat_id: str) -> int:
        """Delete all messages in a chat. Returns number of messages deleted."""
        message_ids = cast(set, self.redis_client.smembers(f"chat:{chat_id}:messages"))
        deleted_count = 0
        
        for message_id in message_ids:
            message_key = f"message:{message_id}"
            if self.redis_client.exists(message_key):
                self.redis_client.delete(message_key)
                self.redis_client.srem(self.MESSAGE_SET_KEY, message_id)
                deleted_count += 1
        
        # Clear the chat's message set
        self.redis_client.delete(f"chat:{chat_id}:messages")
        
        return deleted_count
    
    def get_chat_message_count(self, chat_id: str) -> int:
        """Get total number of messages in a chat."""
        result = self.redis_client.scard(f"chat:{chat_id}:messages")
        return int(result) if isinstance(result, int) else 0
    
    def get_total_message_count(self) -> int:
        """Get total number of messages across all chats."""
        result = self.redis_client.scard(self.MESSAGE_SET_KEY)
        return int(result) if isinstance(result, int) else 0
    
    def _convert_message_dict(self, message_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Redis message dictionary to Message model format."""
        metadata = {}
        try:
            if message_dict.get("metadata"):
                metadata = json.loads(message_dict["metadata"])
        except (json.JSONDecodeError, TypeError):
            metadata = {}
        
        return {
            "message_id": message_dict["message_id"],
            "chat_id": message_dict["chat_id"],
            "content": message_dict["content"],
            "role": MessageRole(message_dict["role"]),
            "metadata": metadata if metadata else None,
            "created_at": datetime.fromisoformat(message_dict["created_at"]),
            "updated_at": datetime.fromisoformat(message_dict["updated_at"]) if message_dict.get("updated_at") else None
        }


# Global repository instance
message_repository = MessageRepository()
