from typing import List, Optional, Dict, Any, cast
from datetime import datetime
from uuid import uuid4
from ..models.schemas import Chat, ChatCreate, ChatUpdate, ChatWithMessages, Task, Message
from ..helpers.redis_client import get_redis_client
from ..repository.message_repository import message_repository


class ChatRepository:
    """Redis-backed chat repository."""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self.CHAT_SET_KEY = "chats"
    
    def create_chat(self, chat_data: ChatCreate) -> Chat:
        """Create a new chat. Use UUID as chat_id, but store in sorted sets by timestamp."""
        timestamp = int(datetime.now().timestamp())
        chat_id = str(uuid4())
        chat_dict = {
            "chat_id": chat_id,
            "user_id": chat_data.user_id,
            "project_id": chat_data.project_id or "",
            "title": chat_data.title or "",
            "created_at": datetime.fromtimestamp(timestamp).isoformat(),
            "updated_at": datetime.fromtimestamp(timestamp).isoformat(),
            "message_count": "0"
        }
        # Store chat in Redis hash
        self.redis_client.hset(f"chat:{chat_id}", mapping=chat_dict)
        # Add to global sorted set by time
        self.redis_client.zadd(self.CHAT_SET_KEY, {chat_id: timestamp})
        # Add to user's sorted set by time
        self.redis_client.zadd(f"user:{chat_data.user_id}:chats", {chat_id: timestamp})
        return Chat(**self._convert_chat_dict(chat_dict))
    
    def get_chat(self, chat_id: str) -> Optional[Chat]:
        """Get a chat by ID."""
        chat_dict = cast(Dict[str, str], self.redis_client.hgetall(f"chat:{chat_id}"))
        if chat_dict:
            return Chat(**self._convert_chat_dict(chat_dict))
        return None
    
    def get_all_chats(self, skip: int = 0, limit: int = 100) -> List[Chat]:
        """Get all chats with pagination, sorted by time (most recent first)."""
        chat_ids = self.redis_client.zrevrange(self.CHAT_SET_KEY, skip, skip + limit - 1)
        if not isinstance(chat_ids, list):
            return []
        chats = [self.get_chat(chat_id) for chat_id in chat_ids]
        return [c for c in chats if c is not None]
    
    def get_user_chats(self, user_id: str, skip: int = 0, limit: int = 100) -> List[Chat]:
        """Get all chats for a specific user, sorted by time (most recent first)."""
        chat_ids = self.redis_client.zrevrange(f"user:{user_id}:chats", skip, skip + limit - 1)
        if not isinstance(chat_ids, list):
            return []
        chats = [self.get_chat(chat_id) for chat_id in chat_ids]
        return [c for c in chats if c is not None]
    
    def get_project_chats(self, project_id: str, skip: int = 0, limit: int = 100) -> List[Chat]:
        """Get all chats for a specific project, sorted by time (most recent first)."""
        chat_ids = self.redis_client.zrevrange(self.CHAT_SET_KEY, skip, skip + limit - 1)
        if not isinstance(chat_ids, list):
            return []
        chats = [self.get_chat(chat_id) for chat_id in chat_ids]
        return [c for c in chats if c is not None and c.project_id == project_id]
    
    def get_chat_with_messages(self, chat_id: str, message_limit: int = 100) -> Optional[ChatWithMessages]:
        """Get a chat with its messages and tasks."""
        chat = self.get_chat(chat_id)
        if not chat:
            return None
        
        # Get messages for this chat
        messages = message_repository.get_chat_messages(chat_id, limit=message_limit)
        
        # Get tasks for this chat (import here to avoid circular import)
        from ..repository.task_repository import task_repository
        tasks = task_repository.get_chat_tasks(chat_id)
        
        return ChatWithMessages(
            chat_id=chat.chat_id,
            user_id=chat.user_id,
            project_id=chat.project_id,
            title=chat.title,
            created_at=chat.created_at,
            updated_at=chat.updated_at,
            message_count=chat.message_count,
            messages=messages,
            tasks=tasks
        )
    
    def update_chat(self, chat_id: str, chat_update: ChatUpdate) -> Optional[Chat]:
        """Update a chat."""
        chat_key = f"chat:{chat_id}"
        if not self.redis_client.exists(chat_key):
            return None
        
        chat_dict = cast(Dict[str, str], self.redis_client.hgetall(chat_key))
        update_data = chat_update.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            if value is not None:
                chat_dict[field] = str(value)
        
        chat_dict["updated_at"] = datetime.now().isoformat()
        self.redis_client.hset(chat_key, mapping=chat_dict)
        
        return Chat(**self._convert_chat_dict(chat_dict))
    
    def delete_chat(self, chat_id: str) -> bool:
        """Delete a chat and all its associated data."""
        chat_key = f"chat:{chat_id}"
        if not self.redis_client.exists(chat_key):
            return False
        
        # Get chat details before deletion
        chat = self.get_chat(chat_id)
        if not chat:
            return False
        
        # Delete all messages in this chat
        message_repository.delete_chat_messages(chat_id)
        
        # Delete all tasks in this chat (import here to avoid circular import)
        from ..repository.task_repository import task_repository
        tasks = task_repository.get_chat_tasks(chat_id)
        for task in tasks:
            task_repository.delete_task(task.id)
        
        # Remove chat from user's chat set
        self.redis_client.zrem(f"user:{chat.user_id}:chats", chat_id)
        
        # Delete the chat itself
        self.redis_client.delete(chat_key)
        self.redis_client.zrem(self.CHAT_SET_KEY, chat_id)
        
        return True
    
    def get_chat_count(self) -> int:
        """Get total number of chats."""
        result = self.redis_client.zcard(self.CHAT_SET_KEY)
        return int(result) if isinstance(result, int) else 0

    def get_user_chat_count(self, user_id: str) -> int:
        """Get total number of chats for a user."""
        result = self.redis_client.zcard(f"user:{user_id}:chats")
        return int(result) if isinstance(result, int) else 0
    
    def increment_message_count(self, chat_id: str) -> None:
        """Increment the message count for a chat."""
        chat_key = f"chat:{chat_id}"
        if self.redis_client.exists(chat_key):
            self.redis_client.hincrby(chat_key, "message_count", 1)
            self.redis_client.hset(chat_key, "updated_at", datetime.now().isoformat())
    
    def decrement_message_count(self, chat_id: str) -> None:
        """Decrement the message count for a chat."""
        chat_key = f"chat:{chat_id}"
        if self.redis_client.exists(chat_key):
            raw_count = self.redis_client.hget(chat_key, "message_count")
            try:
                current_count = int(raw_count) if raw_count and isinstance(raw_count, (int, str)) and str(raw_count).isdigit() else 0
            except Exception:
                current_count = 0
            new_count = max(0, current_count - 1)
            self.redis_client.hset(chat_key, "message_count", str(new_count))
            self.redis_client.hset(chat_key, "updated_at", datetime.now().isoformat())
    
    def _convert_chat_dict(self, chat_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Redis chat dictionary to Chat model format."""
        return {
            "chat_id": chat_dict["chat_id"],
            "user_id": chat_dict["user_id"],
            "project_id": chat_dict.get("project_id", "") or None,
            "title": chat_dict.get("title", "") or None,
            "created_at": datetime.fromisoformat(chat_dict["created_at"]),
            "updated_at": datetime.fromisoformat(chat_dict["updated_at"]) if chat_dict.get("updated_at") else None,
            "message_count": int(chat_dict.get("message_count", 0))
        }


# Global repository instance
chat_repository = ChatRepository()
