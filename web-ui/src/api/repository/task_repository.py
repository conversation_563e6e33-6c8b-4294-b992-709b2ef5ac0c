from typing import List, Optional, Dict, Any, cast
from datetime import datetime
from uuid import uuid4
from enum import Enum
from ..models.schemas import *
from ..helpers.redis_client import get_redis_client


class TaskRepository:
    """Redis-backed task repository."""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self.TASK_SET_KEY = "tasks"

    def create_task(self, task_data: TaskCreate) -> Task:
        """Create a new task."""
        task_id = str(uuid4())
        chat_id = getattr(task_data, 'chat_id', None) or str(uuid4())
        timestamp = int(datetime.now().timestamp())
        task_dict = {
            "id": task_id,
            "title": task_data.title,
            "description": task_data.description or "",
            "priority": task_data.priority.value if isinstance(task_data.priority, TaskPriority) else str(task_data.priority),
            "status": TaskStatusEnum.PENDING.value,
            "created_at": datetime.fromtimestamp(timestamp).isoformat(),
            "updated_at": datetime.fromtimestamp(timestamp).isoformat(),
            "user_id": getattr(task_data, 'user_id', None),
            "project_id": getattr(task_data, 'project_id', None),
            "chat_id": chat_id
        }
        self.redis_client.hset(f"task:{task_id}", mapping=task_dict)
        self.redis_client.zadd(self.TASK_SET_KEY, {task_id: timestamp})
        self.add_task_to_chat(chat_id, task_id, timestamp)
        return Task(**self._convert_task_dict(task_dict))

    def get_task(self, task_id: str) -> Optional[Task]:
        task_dict = cast(Dict[str, str], self.redis_client.hgetall(f"task:{task_id}"))
        if task_dict:
            return Task(**self._convert_task_dict(task_dict))
        return None
    
    def get_detailed_task(self, task_id: str) -> Optional[TaskDetailed]:
        task_dict = cast(Dict[str, str], self.redis_client.hgetall(f"task:{task_id}"))
        if task_dict:
            return TaskDetailed(**self._convert_task_dict(task_dict))
        return None

    def get_all_tasks(self, skip: int = 0, limit: int = 100) -> List[Task]:
        task_ids = self.redis_client.zrevrange(self.TASK_SET_KEY, skip, skip + limit - 1)
        if not isinstance(task_ids, list):
            return []
        tasks = [self.get_task(task_id) for task_id in task_ids]
        tasks = [t for t in tasks if t is not None]
        return tasks

    def update_task(self, task_id: str, task_update: TaskUpdate) -> Optional[Task]:
        task_key = f"task:{task_id}"
        if not self.redis_client.exists(task_key):
            return None
        task_dict = cast(Dict[str, str], self.redis_client.hgetall(task_key))
        update_data = task_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if value is not None:
                if isinstance(value, Enum):
                    value = value.value
                task_dict[field] = value
        task_dict["updated_at"] = datetime.now().isoformat()
        self.redis_client.hset(task_key, mapping=task_dict)
        return Task(**self._convert_task_dict(task_dict))

    def delete_task(self, task_id: str) -> bool:
        task_key = f"task:{task_id}"
        if self.redis_client.exists(task_key):
            # Remove from chat's task set if present
            task = self.get_task(task_id)
            chat_id = getattr(task, 'chat_id', None) if task else None
            if chat_id:
                self.redis_client.zrem(f"chat:{chat_id}:tasks", task_id)
            self.redis_client.delete(task_key)
            self.redis_client.zrem(self.TASK_SET_KEY, task_id)
            return True
        return False

    def get_task_count(self) -> int:
        result = self.redis_client.scard(self.TASK_SET_KEY)
        return int(result) if isinstance(result, int) else 0

    def get_tasks_by_status(self, status: TaskStatusEnum) -> List[Task]:
        task_ids = self.redis_client.zrevrange(self.TASK_SET_KEY, 0, -1)
        if not isinstance(task_ids, list):
            return []
        tasks = [self.get_task(task_id) for task_id in task_ids]
        tasks = [t for t in tasks if t is not None and t.status == status]
        return tasks

    def get_chat_tasks(self, chat_id: str) -> List[Task]:
        """Get all tasks for a specific chat."""
        task_ids = self.redis_client.zrevrange(f"chat:{chat_id}:tasks", 0, -1)
        if not isinstance(task_ids, list):
            return []
        tasks = [self.get_task(task_id) for task_id in task_ids]
        return [t for t in tasks if t is not None]

    def add_task_to_chat(self, chat_id: str, task_id: str, timestamp: Optional[int] = None):
        """Add a task to a chat using ZADD with timestamp as score."""
        if timestamp is None:
            timestamp = int(datetime.now().timestamp())
        self.redis_client.zadd(f"chat:{chat_id}:tasks", {task_id: timestamp})

    def _convert_task_dict(self, task_dict: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "id": task_dict["id"],
            "title": task_dict["title"],
            "description": task_dict.get("description", ""),
            "priority": TaskPriority(task_dict["priority"]) if "priority" in task_dict and task_dict["priority"] else TaskPriority.MEDIUM,
            "status": TaskStatusEnum(task_dict["status"]),
            "created_at": datetime.fromisoformat(task_dict["created_at"]),
            "updated_at": datetime.fromisoformat(task_dict["updated_at"]),
            "user_id": task_dict.get("user_id"),
            "project_id": task_dict.get("project_id"),
            "chat_id": task_dict.get("chat_id"),
            "playwright_script": task_dict.get("playwright_script", None),
            "json_data": task_dict.get("json_data", None)
        }


# Global repository instance
task_repository = TaskRepository()
