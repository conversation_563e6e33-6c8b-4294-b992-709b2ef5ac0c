from typing import List, Optional, Dict, Any, cast
from datetime import datetime
from uuid import uuid4
from ..models.schemas import ScheduledTask, ScheduledTaskCreate, ScheduledTaskUpdate
from ..helpers.redis_client import get_redis_client
import json


class ScheduledTaskRepository:
    """Redis-backed scheduled task repository."""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self.SCHEDULED_TASK_SET_KEY = "scheduled_tasks"
        self.SCHEDULED_TASK_PREFIX = "scheduled_task:"

    def create_scheduled_task(self, scheduled_task_data: ScheduledTaskCreate) -> ScheduledTask:
        """Create a new scheduled task."""
        scheduled_task_id = str(uuid4())
        timestamp = datetime.now()
        
        scheduled_task_dict = {
            "id": scheduled_task_id,
            "task_id": scheduled_task_data.task_id,
            "cron_expression": scheduled_task_data.cron_expression,
            "is_active": str(scheduled_task_data.is_active),
            "description": scheduled_task_data.description or "",
            "created_at": timestamp.isoformat(),
            "updated_at": timestamp.isoformat(),
            "last_run": "",
            "next_run": ""
        }
        
        # Store in Redis hash
        self.redis_client.hset(
            f"{self.SCHEDULED_TASK_PREFIX}{scheduled_task_id}", 
            mapping=scheduled_task_dict
        )
        
        # Add to set for easy retrieval
        self.redis_client.zadd(
            self.SCHEDULED_TASK_SET_KEY, 
            {scheduled_task_id: timestamp.timestamp()}
        )
        
        return ScheduledTask(**self._convert_scheduled_task_dict(scheduled_task_dict))

    def get_scheduled_task(self, scheduled_task_id: str) -> Optional[ScheduledTask]:
        """Get a scheduled task by ID."""
        scheduled_task_dict = cast(
            Dict[str, str], 
            self.redis_client.hgetall(f"{self.SCHEDULED_TASK_PREFIX}{scheduled_task_id}")
        )
        if scheduled_task_dict:
            return ScheduledTask(**self._convert_scheduled_task_dict(scheduled_task_dict))
        return None

    def get_all_scheduled_tasks(self, skip: int = 0, limit: int = 100) -> List[ScheduledTask]:
        """Get all scheduled tasks with pagination."""
        # Get task IDs from sorted set (most recent first)
        scheduled_task_ids = self.redis_client.zrevrange(
            self.SCHEDULED_TASK_SET_KEY, 
            skip, 
            skip + limit - 1
        )
        
        if not isinstance(scheduled_task_ids, list):
            return []
        
        scheduled_tasks = []
        for scheduled_task_id in scheduled_task_ids:
            scheduled_task = self.get_scheduled_task(scheduled_task_id)
            if scheduled_task:
                scheduled_tasks.append(scheduled_task)
        
        return scheduled_tasks

    def get_active_scheduled_tasks(self) -> List[ScheduledTask]:
        """Get all active scheduled tasks."""
        all_scheduled_tasks = self.get_all_scheduled_tasks(skip=0, limit=1000)  # Get all
        return [task for task in all_scheduled_tasks if task.is_active]

    def update_scheduled_task(self, scheduled_task_id: str, update_data: ScheduledTaskUpdate) -> Optional[ScheduledTask]:
        """Update a scheduled task."""
        # Check if scheduled task exists
        existing_task = self.get_scheduled_task(scheduled_task_id)
        if not existing_task:
            return None
        
        # Prepare update data
        update_dict = {}
        if update_data.cron_expression is not None:
            update_dict["cron_expression"] = update_data.cron_expression
        if update_data.is_active is not None:
            update_dict["is_active"] = str(update_data.is_active)
        if update_data.description is not None:
            update_dict["description"] = update_data.description
        
        update_dict["updated_at"] = datetime.now().isoformat()
        
        # Update in Redis
        self.redis_client.hset(
            f"{self.SCHEDULED_TASK_PREFIX}{scheduled_task_id}", 
            mapping=update_dict
        )
        
        # Return updated task
        return self.get_scheduled_task(scheduled_task_id)

    def update_scheduled_task_run_times(self, scheduled_task_id: str, last_run: datetime, next_run: Optional[datetime] = None) -> Optional[ScheduledTask]:
        """Update the last run and next run times for a scheduled task."""
        # Check if scheduled task exists
        existing_task = self.get_scheduled_task(scheduled_task_id)
        if not existing_task:
            return None
        
        update_dict = {
            "last_run": last_run.isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        if next_run:
            update_dict["next_run"] = next_run.isoformat()
        
        # Update in Redis
        self.redis_client.hset(
            f"{self.SCHEDULED_TASK_PREFIX}{scheduled_task_id}", 
            mapping=update_dict
        )
        
        # Return updated task
        return self.get_scheduled_task(scheduled_task_id)

    def delete_scheduled_task(self, scheduled_task_id: str) -> bool:
        """Delete a scheduled task."""
        scheduled_task_key = f"{self.SCHEDULED_TASK_PREFIX}{scheduled_task_id}"
        if self.redis_client.exists(scheduled_task_key):
            # Remove from hash
            self.redis_client.delete(scheduled_task_key)
            # Remove from set
            self.redis_client.zrem(self.SCHEDULED_TASK_SET_KEY, scheduled_task_id)
            return True
        return False

    def get_total_count(self) -> int:
        """Get total count of scheduled tasks."""
        result = self.redis_client.zcard(self.SCHEDULED_TASK_SET_KEY)
        return int(result) if isinstance(result, int) else 0

    def _convert_scheduled_task_dict(self, scheduled_task_dict: Dict[str, str]) -> Dict[str, Any]:
        """Convert Redis dict to proper types for ScheduledTask model."""
        converted = {}
        for key, value in scheduled_task_dict.items():
            if key in ["is_active"]:
                converted[key] = value.lower() == "true"
            elif key in ["created_at", "updated_at", "last_run", "next_run"]:
                if value and value != "":
                    try:
                        converted[key] = datetime.fromisoformat(value)
                    except ValueError:
                        converted[key] = None
                else:
                    converted[key] = None
            else:
                converted[key] = value
        return converted


# Create singleton instance
scheduled_task_repository = ScheduledTaskRepository()
