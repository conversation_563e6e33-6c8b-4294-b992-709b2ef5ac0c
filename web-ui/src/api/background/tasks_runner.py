import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import json
import base64
import os
import sys

from src.agent.browser_use.browser_use_agent import BrowserUseAgent

from ..models.schemas import *
from ..helpers.event_bus import publish_chat_message, disconnect_sse_chat
from src.utils.default_settings import *
from src.utils import llm_provider
from src.browser.custom_browser import CustomBrowser

from langchain_core.language_models.chat_models import BaseChatModel
from browser_use.browser.views import BrowserState
from browser_use.agent.views import AgentHistoryList, AgentOutput

# --- Logger ---
logger = logging.getLogger("tasks_runner")

# --- Background Task Execution ---
async def execute_task(chat_id: str, task_id: str, task_request: TaskCreate):
    """Execute a browser automation task in the background"""
    try: 
        print(f"Executing task {task_id} for chat {chat_id} with request {task_request}")
        start_time = datetime.now()
        
        from ..services.task_service import task_service
        from ..services.message_service import message_service
        await publish_chat_message(chat_id, {"status": TaskStatusEnum.INITIALIZING, "message": "Task is initializing", "start_time": start_time})
        await task_service.update_task_status(task_id, TaskStatusEnum.INITIALIZING)
        
        # Initialize LLM
        llm = await _initialize_llm(
            provider=DEFAULT_LLM_PROVIDER,
            model_name=DEFAULT_LLM_MODEL,
            temperature=DEFAULT_LLM_TEMPERATURE,
            base_url=None,
            api_key=None,
            num_ctx=None,
        )
        
        if not llm:
            await publish_chat_message(chat_id, {"status": TaskStatusEnum.FAILED, "message": "Failed to initialize LLM", "error": "Failed to initialize LLM"})
            await task_service.update_task_status(task_id, TaskStatusEnum.FAILED)
            return
        
        
        from browser_use.browser.browser import BrowserConfig as BUBrowserConfig
        from browser_use.browser.context import BrowserContextConfig
        
        browser_config=BUBrowserConfig(
                headless=True,
                disable_security=False,
                browser_binary_path=None,
                extra_browser_args=[],
                new_context_config=BrowserContextConfig(
                    window_width=1920,
                    window_height=1080,
                )
            )

        # Initialize browser
        browser = CustomBrowser(config=browser_config)
        browser_context = await browser.new_context()
        await publish_chat_message(chat_id, {"status": TaskStatusEnum.RUNNING, "message": "Browser initialized, agent is running", "start_time": start_time})
        await task_service.update_task_status(task_id, TaskStatusEnum.RUNNING)

        # Initialize controller
        # controller = CustomController()
        # await controller.setup_mcp_client(task_request.mcp_config)
        
        async def step_callback_wrapper(state: BrowserState, output: AgentOutput, step_num: int):
            update = await _handle_new_step(state, output, step_num)
            await publish_chat_message(chat_id, update)
            await message_service.create_message(MessageCreate(
                chat_id=chat_id,
                content=json.dumps(update),
                role=MessageRole.AGENT,
            ))
        
        async def done_callback_wrapper(history: AgentHistoryList):
            await _handle_agent_completed(chat_id, task_id, history, task_request)
        
        # Create and run agent
        agent = BrowserUseAgent(
            task=f"Task {task_request.title}\n\n{task_request.description}",
            llm=llm,
            browser_context=browser_context,
            browser=browser,
            # controller=controller,
            retry_delay=10,
            max_failures=2,
            
            register_new_step_callback=step_callback_wrapper,
            register_done_callback=done_callback_wrapper,
            # use_vision=agent_config.use_vision,
            # max_actions_per_step=10,
            # max_input_tokens=128000,
            # max_output_tokens=128000,
            # max_steps=100,
            # max_actions=10,
            # use_vision=True,
            # tool_calling_method="auto",
            source="structured_api"
        )
        # Setup file paths similar to webui
        save_agent_history_path = "./tmp/agent_history"
        os.makedirs(os.path.join(save_agent_history_path, task_id), exist_ok=True)
        
        history_file = os.path.join(save_agent_history_path, task_id, f"{task_id}.json")
        gif_path = os.path.join(save_agent_history_path, task_id, f"{task_id}.gif")
        
        # Store agent reference for potential cancellation
        agent.state.agent_id = task_id
        # agent.broadcast_task_update = broadcast_task_update
        agent.settings.generate_gif = gif_path
        
        # Execute the task
        await agent.run(max_steps=DEFAULT_MAX_STEPS)
                
        # Save agent history
        agent.save_history(history_file)
        
        history = agent.state.history
        
        urls = [u for u in history.urls() if u is not None]
        final_result = history.final_result()
        errors = [e for e in history.errors() if e is not None]
        total_input_tokens = int(history.total_input_tokens())
        total_duration_seconds = float(history.total_duration_seconds())
        history.save_as_playwright_script(history_file+"_playwright.py", browser_config=browser_config) ## Playwright script saving
        user_prompt = task_request.title + "\n" + (task_request.description or "")
        # action_results = history.action_results()
        
        # Process files and encode to base64 (similar to webui _save_to_drcode_api)
        json_content = history.model_dump_json()
        gif_content = None
        playwright_script = None
        
        if os.path.exists(gif_path):
            with open(gif_path, 'rb') as f:
                gif_content = base64.b64encode(f.read()).decode('utf-8')
        if os.path.exists(history_file+"_playwright.py"):
            with open(history_file+"_playwright.py", 'r') as f:
                playwright_script = f.read()
                
        from .qa_summary_report import _process_qa_summary_async
        summary, summary_token_usage = await _process_qa_summary_async(history, task_request, json.loads(json_content), user_prompt)
        
        await publish_chat_message(chat_id, {"status": TaskStatusEnum.COMPLETED, "message": "Task completed successfully.", "duration": total_duration_seconds, "total_input_tokens": total_input_tokens})
        await task_service.update_task(task_id, TaskUpdate(
            json_data=json_content,
            gif_data=gif_content,
            urls_visited=json.dumps(urls),
            errors=json.dumps(errors),
            final_result=final_result, 
            total_input_tokens=total_input_tokens,
            total_duration_seconds=total_duration_seconds,
            summary=summary,
            summary_token_usage=json.dumps(summary_token_usage),
            playwright_script=playwright_script,
            status=TaskStatusEnum.COMPLETED
        ))
        await message_service.create_message(MessageCreate(
            chat_id=chat_id,
            content=json.dumps({
                "status": TaskStatusEnum.COMPLETED,
                "message": "Task completed successfully.",
                "duration": total_duration_seconds,
                "total_input_tokens": total_input_tokens,
                "summary": summary,
                "summary_token_usage": summary_token_usage,
                "playwright_script": playwright_script,
                "gif": gif_content,
            }),
            role=MessageRole.AGENT,
        ))
        disconnect_sse_chat(chat_id)
        from .save_data_to_drcode_api import upload_gif_to_drcode_api, post_ui_testing_session, patch_ui_testing_json
        gif_result = await upload_gif_to_drcode_api(task_request.project_id or "", gif_content or "")
        print(gif_result.get("data", {}).get("url", ""))
        ui_testing_result = await post_ui_testing_session(
            project_id=task_request.project_id or "", 
            user_id=task_request.user_id or "", 
            task_id=task_id, 
            user_prompt=task_request.title + "\n" + (task_request.description or ""), 
            duration=total_duration_seconds, 
            tokens=total_input_tokens, 
            status=TaskStatusEnum.COMPLETED, 
            summary=summary,
            errors=errors, 
            urls_visited=urls, 
            gif_url=gif_result.get("data", {}).get("url", "")
            # playwright_script=playwright_script
        )
        await patch_ui_testing_json(ui_testing_result.get("data", {}).get("id", ""), json_content)
          
    except Exception as e:
        print(f"Error executing task {task_id}: {e}")
        await publish_chat_message(chat_id, {"status": TaskStatusEnum.FAILED, "message": f"Error executing task: {e}", "error": str(e)})
        raise e



async def _initialize_llm(
        provider: Optional[str],
        model_name: Optional[str],
        temperature: float,
        base_url: Optional[str],
        api_key: Optional[str],
        num_ctx: Optional[int] = None,
) -> Optional[BaseChatModel]:
    """Initializes the LLM based on settings. Returns None if provider/model is missing."""
    if not provider or not model_name:
        logger.info("LLM Provider or Model Name not specified, LLM will be None.")
        return None
    try:
        # Use your actual LLM provider logic here
        logger.info(
            f"Initializing LLM: Provider={provider}, Model={model_name}, Temp={temperature}"
        )
        # Example using a placeholder function
        llm = llm_provider.get_llm_model(
            provider=provider,
            model_name=model_name,
            temperature=temperature,
            base_url=base_url or None,
            api_key=api_key or None,
            # Add other relevant params like num_ctx for ollama
            num_ctx=num_ctx if provider == "ollama" else None,
        )
        return llm
    except Exception as e:
        logger.error(f"Failed to initialize LLM: {e}", exc_info=True)
        return None


async def _handle_new_step(state: BrowserState, output: AgentOutput, step_num: int):
    """Callback for each step taken by the agent, including screenshot display."""

    step_num -= 1
    logger.info(f"Step {step_num} completed.")
    
    # --- Format Agent Output ---
    formatted_output = _format_agent_output(output)  # Use the updated function

    def tab_to_dict(tab):
        if hasattr(tab, "model_dump"):
            return tab.model_dump(exclude_none=True)
        elif hasattr(tab, "__dict__"):
            return {k: v for k, v in tab.__dict__.items() if not k.startswith('_')}
        else:
            return str(tab)

    update = {
        "status": "running",
        "message": f"Step {step_num} completed.",
        "url": state.url,
        "title": state.title,
        "tabs": [tab_to_dict(tab) for tab in state.tabs],
        "screenshot": state.screenshot,
        "pixels_above": state.pixels_above,
        "pixels_below": state.pixels_below,
        "browser_errors": state.browser_errors,
        "action": formatted_output
    }

    return update


async def _handle_agent_completed(chat_id: str, task_id: str, history: AgentHistoryList, task_request: TaskCreate):
    """Callback for when the agent completes its work."""
    duration = history.total_duration_seconds()
    total_input_tokens = history.total_input_tokens()
    errors = history.errors()
    final_result = history.final_result()
    
    if errors and any(errors):
        status = TaskStatusEnum.AGENT_COMPLETED_WITH_ERRORS
    else:
        status = TaskStatusEnum.AGENT_COMPLETED
    
    from ..services.task_service import task_service
    await publish_chat_message(chat_id, {
        "status": status, 
        "message": "Agent work completed successfully. Summary will be generated in a few seconds.", 
        "duration": duration,
        "total_input_tokens": total_input_tokens,
        "errors": errors,
        "final_result": final_result
        })
    await task_service.update_task_status(task_id, status)


def _clean_data(obj):
    """Recursively clean data to remove callables and special/private attributes."""
    if isinstance(obj, dict):
        return {k: _clean_data(v) for k, v in obj.items() if not k.startswith('_') and not callable(v)}
    elif isinstance(obj, list):
        return [_clean_data(i) for i in obj]
    elif isinstance(obj, (str, int, float, bool, type(None))):
        return obj
    # If it's a pydantic model or has a model_dump method, use it
    elif hasattr(obj, "model_dump"):
        return _clean_data(obj.model_dump(exclude_none=True))
    # If it's a custom object, try to use its __dict__
    elif hasattr(obj, "__dict__"):
        return _clean_data({k: v for k, v in obj.__dict__.items() if not k.startswith('_') and not callable(v)})
    else:
        return str(obj)  # fallback to string representation

def _format_agent_output(model_output: AgentOutput) -> dict:
    """Formats AgentOutput for display in the chatbot using JSON."""
    content = None
    if model_output:
        try:
            state_dump = model_output.current_state.model_dump(exclude_none=True)
            content = {
                "memory": _clean_data(state_dump.get("memory")),
                "next_goal": _clean_data(state_dump.get("next_goal")),
                "evaluation_previous_goal": _clean_data(state_dump.get("evaluation_previous_goal")),
            }
        except AttributeError as ae:
            logger.error(
                f"AttributeError during model dump: {ae}. Check if 'action' or 'current_state' or their items support 'model_dump'."
            )
            content = {"error": f"Error: Could not format agent output (AttributeError: {ae}).\nRaw output: {str(model_output)}"}
        except Exception as e:
            logger.error(f"Error formatting agent output: {e}", exc_info=True)
            content = {"error": f"Error formatting agent output.\nRaw output:\n{str(model_output)}"}
    return content or {}


async def execute_playwright_script(task_id: str, script: str):
    """Execute a playwright script in the background"""
    from ..services.task_service import task_service
    
    logger.info(f"Executing playwright script for task {task_id}")
    await task_service.update_task_status(task_id, TaskStatusEnum.RUNNING)
    
    # script = script.replace("p.chromium.launch(headless=False)", "p.chromium.launch(headless=True)")
    
    # write_script_path = f"./tmp/playwright_scripts/{task_id}.py"
    # os.makedirs(os.path.dirname(write_script_path), exist_ok=True)
    # with open(write_script_path, 'w') as f:
    #     f.write(script)
    
    try:
        process = await asyncio.create_subprocess_exec(
            sys.executable, "-c", script,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        print(f"Playwright script for task {task_id} executed with return code {process.returncode}")
        
        if process.returncode == 0:
            await task_service.update_task_status(task_id, TaskStatusEnum.COMPLETED)
            logger.info(f"Playwright script for task {task_id} completed successfully.")
            # Optionally save stdout to the task
        else:
            await task_service.update_task_status(task_id, TaskStatusEnum.FAILED)
            error_message = stderr.decode()
            logger.error(f"Playwright script for task {task_id} failed: {error_message}")
            # Optionally save stderr to the task
            
    except Exception as e:
        logger.error(f"Error executing playwright script for task {task_id}: {str(e)}")
        await task_service.update_task_status(task_id, TaskStatusEnum.FAILED)
        # Optionally save exception to the task

