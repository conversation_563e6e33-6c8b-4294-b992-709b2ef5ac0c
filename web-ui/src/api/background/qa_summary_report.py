import asyncio
import json
import os
import time
import logging
from typing import Any

from browser_use.agent.views import AgentHistoryList
from ..models.schemas import TaskCreate

logger = logging.getLogger("qa_summary_report")

async def _process_qa_summary_async(history: AgentHistoryList, task_request: TaskCreate, session_data: dict, user_prompt: str) -> tuple[str, dict]:
    """Async function to process QA summary with OpenAI and update UI."""
    try:
        # Extract required fields for OpenAI
        openai_data = _extract_openai_fields(session_data)

        # Generate summary using OpenAI
        summary, summary_token_usage = await _generate_openai_summary(openai_data, user_prompt)

        logger.info(f"QA summary processing completed. Token usage: {summary_token_usage}")
        return summary, summary_token_usage


    except Exception as e:
        logger.error(f"Error processing QA summary: {e}", exc_info=True)
        # Update UI with error message
        error_summary = "## 🧪 QA TEST EXECUTION COMPLETED\n\n"
        error_summary += f"❌ **Error generating AI summary:** {str(e)}\n\n"
        error_summary += "**Execution Summary:**\n"
        error_summary += f"- Duration: {history.total_duration_seconds():.2f} seconds\n"
        error_summary += f"- Total Input Tokens: {history.total_input_tokens()}\n"

        errors = history.errors()
        if errors and any(errors):
            error_summary += f"- Status: ⚠️ Completed with errors\n"
        else:
            error_summary += f"- Status: ✅ Successfully completed\n"
        summary=error_summary
        return summary, {}


def _extract_openai_fields(session_data: Any) -> dict:
    """Extract model_output, result, and state.url fields from session data."""
    extracted_data = {
        "model_outputs": [],
        "results": [],
        "urls": []
    }

    if session_data and "history" in session_data:
        for entry in session_data["history"]:
            # Extract model_output
            if "model_output" in entry and entry["model_output"]:
                extracted_data["model_outputs"].append(entry["model_output"])

            # Extract result
            if "result" in entry and entry["result"]:
                extracted_data["results"].append(entry["result"])

            # Extract state.url
            if "state" in entry and entry["state"] and "url" in entry["state"]:
                url = entry["state"]["url"]
                if url and url not in extracted_data["urls"]:
                    extracted_data["urls"].append(url)

    return extracted_data

async def _generate_openai_summary(openai_data: Any, user_prompt: str) -> tuple[str, dict]:
    """Generate summary using OpenAI API."""
    import openai

    # Get OpenAI API key from environment
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OPENAI_API_KEY not found in environment variables")

    client = openai.AsyncOpenAI(api_key=api_key)

    # Prepare the prompt for OpenAI
    system_prompt = """You are a QA UI Testing Agent analyzer. Generate a comprehensive, human-readable QA testing report in markdown format based on the provided session data.

Your report should include:
1. **Test Overview** - What was being tested
2. **Test Execution Summary** - Key actions taken
3. **Results Analysis** - What was found/achieved
4. **Issues Identified** - Any problems or errors
5. **Recommendations** - Suggestions for improvement

Format the response as clean markdown with proper headers, bullet points, and sections."""

    user_message = f"""Please analyze this QA testing session and generate a detailed report:

**Original User Request:** {user_prompt}

**Session Data:**
- Model Outputs: {json.dumps(openai_data['model_outputs'], indent=2)}
- Results: {json.dumps(openai_data['results'], indent=2)}
- URLs Visited: {openai_data['urls']}

Generate a comprehensive QA testing report in markdown format."""

    try:
        response = await client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            temperature=0.3
        )

        summary = (response.choices[0].message.content or "") if response.choices and response.choices[0].message else ""
        usage = response.usage or {}
        token_usage = {
            "prompt_tokens": getattr(usage, "prompt_tokens", 0),
            "completion_tokens": getattr(usage, "completion_tokens", 0),
            "total_tokens": getattr(usage, "total_tokens", 0)
        }
        return summary, token_usage

    except Exception as e:
        logger.error(f"OpenAI API error: {e}")
        raise

