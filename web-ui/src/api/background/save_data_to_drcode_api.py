import os
import httpx
from typing import Any
import logging

drcode_api_base_url = os.getenv("DRCODE_API_URL", "https://devapi.drcode.ai/testgpt")

logger = logging.getLogger("save_data_to_drcode_api")

async def upload_gif_to_drcode_api(project_id: str, gif_data: str) -> dict:
    """
    Uploads a GIF to the DrCode API.
    Args:
        project_id (str): The project ID.
        gif_data (str): The GIF data (base64, with data URL prefix if present).
    Returns:
        dict: The API response as a dictionary.
    """
    url = f"{drcode_api_base_url}/api/uiTesting/uploadGif"
    payload = {
        "project_id": project_id,
        "gif_data": gif_data
    }
    if gif_data is None or gif_data == "":
        return {}
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload)
            response.raise_for_status()
            return response.json()
    except Exception as e:
        logger.error(f"Error uploading gif to DrCode API: {e}", exc_info=True)
        return {"error": str(e)}

async def post_ui_testing_session(
    project_id: str,
    user_id: str,
    task_id: str,
    user_prompt: str,
    duration: float,
    tokens: int,
    status: str,
    errors: list[str],
    urls_visited: list[str],
    gif_url: str,
    summary: str,
    # playwright_script: str,
    # steps: dict
) -> dict:
    """
    Posts UI testing session data to the DrCode API.
    """
    url = f"{drcode_api_base_url}/api/uiTesting"
    payload = {
        "project_id": project_id,
        "user_id": user_id,
        "task_id": task_id,
        "user_prompt": user_prompt,
        "duration": duration,
        "tokens": tokens,
        "status": status,
        "errors": errors,
        "urls_visited": urls_visited,
        "gif_url": gif_url,
        "summary": summary
        # "steps": steps
        # "playwright_script": playwright_script
    }
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload)
            response.raise_for_status()
            return response.json()
    except Exception as e:
        logger.error(f"Error posting UI testing session to DrCode API: {e}", exc_info=True)
        return {"error": str(e)}

async def patch_ui_testing_json(session_id: str, json_data: Any) -> dict:
    """
    PATCH JSON data to the DrCode API for a given session.
    Args:
        session_id (str): The session ID.
        json_data (dict): The JSON data to patch.
    Returns:
        dict: The API response as a dictionary.
    """
    url = f"{drcode_api_base_url}/api/uiTesting/{session_id}/json"
    payload = {"json_data": json_data}
    try:
        async with httpx.AsyncClient() as client:
            response = await client.patch(url, json=payload)
            response.raise_for_status()
            return response.json()
    except Exception as e:
        logger.error(f"Error patching UI testing JSON to DrCode API: {e}", exc_info=True)
        return {"error": str(e)}







