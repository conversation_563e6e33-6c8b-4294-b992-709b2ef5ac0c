from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON>NResponse
import uvicorn
from contextlib import asynccontextmanager

from .controllers.task_controller import router as task_router
from .controllers.health_controller import router as health_router
from .controllers.chat_controller import router as chat_router
from .controllers.sse_controller import router as sse_router
from .controllers.refinement_controller import router as refinement_router
from .services.task_scheduler_service import task_scheduler_service


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("🚀 API Server starting up...")
    
    # Start the task scheduler
    await task_scheduler_service.start()
    print("📅 Task scheduler started and loaded existing schedules")
    
    yield
    
    # Shutdown
    print("🛑 API Server shutting down...")
    
    # Stop the task scheduler
    await task_scheduler_service.stop()
    print("📅 Task scheduler stopped")


# Create FastAPI app with lifespan
app = FastAPI(
    title="DrCode Structured API",
    description="A simple structured API for DrCode UI Testing",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health_router, prefix="/api/v1")
app.include_router(task_router, prefix="/api/v1")
app.include_router(chat_router, prefix="/api/v1")
app.include_router(sse_router, prefix="/api/v1")
app.include_router(refinement_router, prefix="/api/v1")

@app.get("/")
async def root():
    return {
        "message": "Welcome to DrCode Structured API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal server error: {str(exc)}"}
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
