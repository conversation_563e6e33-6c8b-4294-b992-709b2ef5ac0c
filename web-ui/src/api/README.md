# DrCode Structured API

A simple, well-structured FastAPI application for task management.

## Features

- ✅ **RESTful API** with full CRUD operations for tasks
- 📚 **Auto-generated documentation** with Swagger UI and ReDoc
- 🏗️ **Clean architecture** with proper separation of concerns
- 🔍 **Health check endpoints** for monitoring
- 📊 **Task status management** with filtering capabilities
- 🚀 **Ready to run** with minimal setup

## Project Structure

```
structured-api/
├── main.py                 # FastAPI application entry point
├── controllers/            # API route handlers
│   ├── task_controller.py  # Task CRUD endpoints
│   └── health_controller.py # Health check endpoints
├── services/               # Business logic layer
│   └── task_service.py     # Task business operations
├── repository/             # Data access layer
│   └── task_repository.py  # In-memory task storage
├── models/                 # Data models and schemas
│   └── schemas.py          # Pydantic models
└── migrations/             # Database migrations (future use)
```

## API Endpoints

### Health Endpoints

- `GET /` - Welcome message
- `GET /api/v1/health` - Detailed health check
- `GET /api/v1/health/ping` - Simple ping

### Task Endpoints

- `POST /api/v1/tasks` - Create a new task
- `GET /api/v1/tasks` - Get all tasks (with pagination and filtering)
- `GET /api/v1/tasks/{id}` - Get a specific task
- `PUT /api/v1/tasks/{id}` - Update a task
- `DELETE /api/v1/tasks/{id}` - Delete a task
- `PATCH /api/v1/tasks/{id}/status` - Update task status only

## Running the API

### Quick Start

1. Navigate to the web-ui directory:

   ```bash
   cd web-ui
   ```

2. Install dependencies (if not already installed):

   ```bash
   pip install -r requirements.txt
   ```

3. Run the API:

   ```bash
   python src/run_api.py
   ```

4. Open your browser and visit:
   - **API Documentation**: http://localhost:8000/docs
   - **ReDoc Documentation**: http://localhost:8000/redoc
   - **Health Check**: http://localhost:8000/api/v1/health

### Alternative: Run directly

```bash
cd web-ui/src
python -m uvicorn structured-api.main:app --host 0.0.0.0 --port 8000 --reload
```

## Example Usage

### Create a Task

```bash
curl -X POST "http://localhost:8000/api/v1/tasks" \
     -H "Content-Type: application/json" \
     -d '{
       "title": "My First Task",
       "description": "This is a test task",
       "priority": "high"
     }'
```

### Get All Tasks

```bash
curl "http://localhost:8000/api/v1/tasks"
```

### Update Task Status

```bash
curl -X PATCH "http://localhost:8000/api/v1/tasks/1/status?status=completed"
```

## Task Schema

```json
{
  "id": 1,
  "title": "Task Title",
  "description": "Task Description",
  "priority": "medium", // low, medium, high
  "status": "pending", // pending, running, completed, failed
  "created_at": "2025-07-09T10:30:00",
  "updated_at": "2025-07-09T10:30:00"
}
```

## Development

The API is designed with a clean architecture pattern:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Contain business logic
- **Repository**: Handle data persistence (currently in-memory)
- **Models**: Define data structures and validation

This structure makes it easy to extend the API with new features or swap out the in-memory storage for a real database.

## Future Enhancements

- 🗄️ Add database persistence (PostgreSQL/SQLite)
- 🔐 Add authentication and authorization
- 📝 Add logging and monitoring
- 🧪 Add comprehensive testing
- 🐳 Add Docker containerization
- 📊 Add task analytics and reporting
