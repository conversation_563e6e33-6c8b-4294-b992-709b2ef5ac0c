"""
API-compatible wrapper for browser use agent functionality.

This module provides API-compatible versions of the webui functions
that can work with dictionary-based component data instead of Gradio components.
"""

import asyncio
import json
import logging
import os
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, AsyncGenerator

from src.webui.webui_manager import WebuiManager
from src.webui.components.browser_use_agent_tab import (
    run_agent_task as webui_run_agent_task,
    handle_stop as webui_handle_stop,
    handle_pause_resume as webui_handle_pause_resume,
    handle_clear as webui_handle_clear
)

logger = logging.getLogger(__name__)


class MockComponent:
    """Mock Gradio component for API compatibility."""
    
    def __init__(self, value=None):
        self.value = value
    
    def __repr__(self):
        return f"MockComponent(value={self.value})"


def create_mock_components_dict(webui_manager: WebuiManager, api_components_dict: Dict[str, Any]) -> Dict[Any, Any]:
    """
    Create a mock components dictionary that mimics Gradio component structure.
    
    Args:
        webui_manager: The WebUI manager instance
        api_components_dict: Dictionary with component IDs as keys and values
        
    Returns:
        Dictionary with mock components as keys and values
    """
    mock_components = {}
    
    # Create mock components for each API component
    for comp_id, value in api_components_dict.items():
        mock_comp = MockComponent(value)
        mock_components[mock_comp] = value
        
        # Also register in webui_manager if not exists
        if not hasattr(webui_manager, 'id_to_component'):
            webui_manager.id_to_component = {}
            webui_manager.component_to_id = {}
        
        webui_manager.id_to_component[comp_id] = mock_comp
        webui_manager.component_to_id[mock_comp] = comp_id
    
    return mock_components


async def api_run_agent_task(
    webui_manager: WebuiManager, 
    api_components_dict: Dict[str, Any]
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    API-compatible version of run_agent_task.
    
    Args:
        webui_manager: WebUI manager instance
        api_components_dict: Dictionary with component settings
        
    Yields:
        Updates as dictionaries instead of Gradio component updates
    """
    # Create mock components dictionary
    mock_components = create_mock_components_dict(webui_manager, api_components_dict)
    
    # Track the last update for yielding changes
    last_chat_length = len(getattr(webui_manager, 'bu_chat_history', []))
    last_status = None
    
    try:
        # Run the original webui function
        async for gradio_update in webui_run_agent_task(webui_manager, mock_components):
            # Convert Gradio updates to API format
            api_update = {}
            
            # Extract meaningful information from Gradio updates
            for component, update_info in gradio_update.items():
                comp_id = webui_manager.component_to_id.get(component)
                if comp_id:
                    if hasattr(update_info, 'value'):
                        api_update[comp_id] = update_info.value
                    elif isinstance(update_info, dict) and 'value' in update_info:
                        api_update[comp_id] = update_info['value']
            
            # Add chat history if changed
            current_chat = getattr(webui_manager, 'bu_chat_history', [])
            if len(current_chat) > last_chat_length:
                api_update['chat_history'] = current_chat
                last_chat_length = len(current_chat)
            
            # Add task status
            current_task = getattr(webui_manager, 'bu_current_task', None)
            if current_task:
                if current_task.done():
                    status = "completed" if not current_task.exception() else "error"
                else:
                    agent = getattr(webui_manager, 'bu_agent', None)
                    if agent:
                        if getattr(agent.state, 'stopped', False):
                            status = "stopped"
                        elif getattr(agent.state, 'paused', False):
                            status = "paused"
                        else:
                            status = "running"
                    else:
                        status = "running"
            else:
                status = "idle"
            
            if status != last_status:
                api_update['status'] = status
                last_status = status
            
            # Only yield if we have meaningful updates
            if api_update:
                yield api_update
                
    except Exception as e:
        logger.error(f"Error in api_run_agent_task: {e}")
        yield {
            'status': 'error',
            'error': str(e),
            'chat_history': getattr(webui_manager, 'bu_chat_history', [])
        }


async def api_handle_stop(webui_manager: WebuiManager) -> Dict[str, Any]:
    """API-compatible version of handle_stop."""
    try:
        gradio_update = await webui_handle_stop(webui_manager)
        
        # Convert to API format
        api_update = {
            'action': 'stop',
            'status': 'stopped',
            'message': 'Task stopped successfully'
        }
        
        # Add current state
        api_update['chat_history'] = getattr(webui_manager, 'bu_chat_history', [])
        
        return api_update
        
    except Exception as e:
        logger.error(f"Error in api_handle_stop: {e}")
        return {
            'action': 'stop',
            'status': 'error',
            'error': str(e)
        }


async def api_handle_pause_resume(webui_manager: WebuiManager) -> Dict[str, Any]:
    """API-compatible version of handle_pause_resume."""
    try:
        gradio_update = await webui_handle_pause_resume(webui_manager)
        
        # Determine if paused or resumed
        agent = getattr(webui_manager, 'bu_agent', None)
        if agent and hasattr(agent, 'state'):
            is_paused = getattr(agent.state, 'paused', False)
            action = 'pause' if is_paused else 'resume'
            status = 'paused' if is_paused else 'running'
        else:
            action = 'pause_resume'
            status = 'unknown'
        
        api_update = {
            'action': action,
            'status': status,
            'message': f'Task {action}d successfully'
        }
        
        # Add current state
        api_update['chat_history'] = getattr(webui_manager, 'bu_chat_history', [])
        
        return api_update
        
    except Exception as e:
        logger.error(f"Error in api_handle_pause_resume: {e}")
        return {
            'action': 'pause_resume',
            'status': 'error',
            'error': str(e)
        }


async def api_handle_clear(webui_manager: WebuiManager) -> Dict[str, Any]:
    """API-compatible version of handle_clear."""
    try:
        gradio_update = await webui_handle_clear(webui_manager)
        
        api_update = {
            'action': 'clear',
            'status': 'cleared',
            'message': 'Task cleared successfully',
            'chat_history': []  # Should be empty after clear
        }
        
        return api_update
        
    except Exception as e:
        logger.error(f"Error in api_handle_clear: {e}")
        return {
            'action': 'clear',
            'status': 'error',
            'error': str(e)
        }


def get_task_status_info(webui_manager: WebuiManager) -> Dict[str, Any]:
    """Get current task status information."""
    status_info = {
        'chat_history': getattr(webui_manager, 'bu_chat_history', []),
        'task_id': getattr(webui_manager, 'bu_agent_task_id', None),
        'status': 'idle',
        'error_message': None
    }
    
    # Determine current status
    current_task = getattr(webui_manager, 'bu_current_task', None)
    if current_task:
        if current_task.done():
            if current_task.exception():
                status_info['status'] = 'error'
                status_info['error_message'] = str(current_task.exception())
            else:
                status_info['status'] = 'completed'
        else:
            agent = getattr(webui_manager, 'bu_agent', None)
            if agent and hasattr(agent, 'state'):
                if getattr(agent.state, 'stopped', False):
                    status_info['status'] = 'stopped'
                elif getattr(agent.state, 'paused', False):
                    status_info['status'] = 'paused'
                else:
                    status_info['status'] = 'running'
            else:
                status_info['status'] = 'running'
    
    # Check if waiting for user assistance
    if hasattr(webui_manager, 'bu_response_event') and webui_manager.bu_response_event:
        status_info['waiting_for_assistance'] = True
    else:
        status_info['waiting_for_assistance'] = False
    
    return status_info


def provide_user_assistance(webui_manager: WebuiManager, response: str) -> Dict[str, Any]:
    """Provide user assistance response."""
    try:
        if not hasattr(webui_manager, 'bu_response_event') or not webui_manager.bu_response_event:
            return {
                'success': False,
                'message': 'Task is not waiting for assistance'
            }
        
        # Provide the response
        webui_manager.bu_user_help_response = response
        webui_manager.bu_response_event.set()
        
        return {
            'success': True,
            'message': 'Assistance provided successfully'
        }
        
    except Exception as e:
        logger.error(f"Error providing assistance: {e}")
        return {
            'success': False,
            'message': str(e)
        }
