from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid


class AgentSettings(BaseModel):
    """Agent configuration settings."""
    llm_provider: Optional[str] = None
    llm_model_name: Optional[str] = None
    llm_temperature: float = 0.6
    llm_base_url: Optional[str] = None
    llm_api_key: Optional[str] = None
    use_vision: bool = True
    max_steps: int = 100
    max_actions: int = 10
    max_input_tokens: int = 128000
    tool_calling_method: Optional[str] = "auto"
    override_system_prompt: Optional[str] = None
    extend_system_prompt: Optional[str] = None
    ollama_num_ctx: int = 16000
    
    # Planner LLM Settings (Optional)
    planner_llm_provider: Optional[str] = None
    planner_llm_model_name: Optional[str] = None
    planner_llm_temperature: float = 0.6
    planner_llm_base_url: Optional[str] = None
    planner_llm_api_key: Optional[str] = None
    planner_use_vision: bool = False
    planner_ollama_num_ctx: int = 16000
    
    # MCP Server Config
    mcp_server_config: Optional[Dict[str, Any]] = None


class BrowserSettings(BaseModel):
    """Browser configuration settings."""
    browser_binary_path: Optional[str] = None
    browser_user_data_dir: Optional[str] = None
    use_own_browser: bool = False
    keep_browser_open: bool = False
    headless: bool = False
    disable_security: bool = False
    window_w: int = 1280
    window_h: int = 1100
    cdp_url: Optional[str] = None
    wss_url: Optional[str] = None
    save_recording_path: Optional[str] = None
    save_trace_path: Optional[str] = None
    save_agent_history_path: str = "./tmp/agent_history"
    save_download_path: str = "./tmp/downloads"


class TaskRequest(BaseModel):
    """Request model for creating a new task."""
    task: str
    agent_settings: Optional[AgentSettings] = None
    browser_settings: Optional[BrowserSettings] = None
    skip_refinement: bool = False  # Option to skip prompt refinement
    refinement_session_id: Optional[str] = None  # Use existing refinement session


class TaskResponse(BaseModel):
    """Response model for task operations."""
    task_id: str
    status: str
    message: Optional[str] = None
    created_at: datetime


class TaskStatus(BaseModel):
    """Task status information."""
    task_id: str
    status: str  # "running", "paused", "stopped", "completed", "error"
    progress: Optional[Dict[str, Any]] = None
    chat_history: List[Dict[str, Any]] = []
    error_message: Optional[str] = None
    duration: Optional[float] = None
    tokens_used: Optional[int] = None


class TaskHistoryResponse(BaseModel):
    """Task history file response."""
    task_id: str
    history_file_path: Optional[str] = None
    gif_file_path: Optional[str] = None
    history_data: Optional[Dict[str, Any]] = None


class UserAssistanceRequest(BaseModel):
    """User assistance response."""
    task_id: str
    response: str


class ChatMessage(BaseModel):
    """Chat message model."""
    role: str  # "user", "assistant"
    content: str
    timestamp: Optional[datetime] = None


class ConfigurationRequest(BaseModel):
    """Configuration save/load request."""
    config_name: Optional[str] = None
    agent_settings: Optional[AgentSettings] = None
    browser_settings: Optional[BrowserSettings] = None


class ConfigurationResponse(BaseModel):
    """Configuration response."""
    config_name: str
    config_path: str
    saved_at: datetime


class AvailableModelsResponse(BaseModel):
    """Available models for a provider."""
    provider: str
    models: List[str]


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None


class QASummaryResponse(BaseModel):
    """QA Summary response."""
    task_id: str
    summary: str
    token_usage: Dict[str, int]
    saved_to_drcode: bool = False


# Prompt Refinement Models

class RefinementStartRequest(BaseModel):
    """Request to start prompt refinement."""
    prompt: str


class RefinementStartResponse(BaseModel):
    """Response when starting prompt refinement."""
    session_id: Optional[str]
    needs_refinement: bool
    clarifying_questions: Optional[List[str]] = None
    refined_prompt: Optional[str] = None
    state: str
    analysis: Optional[Dict[str, Any]] = None
    message: str
    error: Optional[str] = None


class RefinementAnswersRequest(BaseModel):
    """Request to provide answers to clarifying questions."""
    session_id: str
    answers: List[str]


class RefinementAnswersResponse(BaseModel):
    """Response after providing answers."""
    session_id: str
    needs_further_refinement: bool
    refined_prompt: str
    clarifying_questions: Optional[List[str]] = None
    state: str
    iteration: int
    message: str
    error: Optional[str] = None


class RefinementStatusResponse(BaseModel):
    """Response for refinement session status."""
    session_id: str
    original_prompt: str
    state: str
    created_at: str
    updated_at: str
    analysis_result: Optional[Dict[str, Any]] = None
    clarifying_questions: List[str]
    user_answers: List[str]
    refined_prompt: Optional[str] = None
    iteration_count: int
    max_iterations: int
    history: List[Dict[str, Any]]
    error: Optional[str] = None
