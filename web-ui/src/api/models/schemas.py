from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class HealthCheck(BaseModel):
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = "1.0.0"
    uptime: Optional[str] = None
    redis_status: Optional[str] = None


# Enums
class TaskStatusEnum(str, Enum):
    PENDING = "pending"
    INITIALIZING = "initializing"
    RUNNING = "running"
    AGENT_COMPLETED = "agent_completed"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    FAILED = "failed"
    AGENT_COMPLETED_WITH_ERRORS = "agent_completed_with_errors"


class TaskPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class MessageRole(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    AGENT = "agent"


# Base Response Models
class BaseResponse(BaseModel):
    success: bool
    message: str


# Task Models
class TaskBase(BaseModel):
    title: str = Field(..., description="Title of the task")
    description: Optional[str] = Field(default="", description="Detailed description of the task")
    priority: TaskPriority = Field(TaskPriority.MEDIUM, description="Priority level of the task")
    user_id: Optional[str] = Field(default="", description="ID of the user who created the task")
    project_id: Optional[str] = Field(default="", description="ID of the project this task belongs to")
    chat_id: Optional[str] = Field(default="", description="ID of the chat this task belongs to")


class TaskCreate(TaskBase):
    pass


class TaskUpdate(BaseModel):
    title: Optional[str] = Field(default=None, description="Title of the task")
    description: Optional[str] = Field(default=None, description="Detailed description of the task")
    priority: Optional[TaskPriority] = Field(default=None, description="Priority level of the task")
    status: Optional[TaskStatusEnum] = Field(default=None, description="Status of the task")
    json_data: Optional[str] = Field(default=None, description="JSON data of the task")
    gif_data: Optional[str] = Field(default=None, description="GIF data of the task")
    urls_visited: Optional[str] = Field(default=None, description="URLs visited by the task")
    errors: Optional[str] = Field(default=None, description="Errors encountered by the task")
    user_prompt: Optional[str] = Field(default=None, description="User prompt for the task")
    summary: Optional[str] = Field(default=None, description="Summary of the task")
    final_result: Optional[str] = Field(default=None, description="Final result of the task")
    total_input_tokens: Optional[int] = Field(default=None, description="Total input tokens used by the task")
    total_duration_seconds: Optional[float | int] = Field(default=None, description="Total duration of the task in seconds")
    summary_token_usage: Optional[str] = Field(default=None, description="Summary token usage of the task")
    playwright_script: Optional[str] = Field(default=None, description="Playwright script of the task")
    
class Task(TaskBase):
    id: str = Field(..., description="Unique identifier for the task")
    status: TaskStatusEnum = Field(TaskStatusEnum.PENDING, description="Current status of the task")
    created_at: datetime = Field(..., description="When the task was created")
    updated_at: datetime = Field(..., description="When the task was last updated")

class TaskDetailed(TaskBase):
    id: str = Field(..., description="Unique identifier for the task")
    status: TaskStatusEnum = Field(TaskStatusEnum.PENDING, description="Current status of the task")
    created_at: datetime = Field(..., description="When the task was created")
    updated_at: datetime = Field(..., description="When the task was last updated")
    playwright_script: Optional[str] = Field(default=None, description="Playwright script of the task")
    json_data: Optional[str] = Field(default=None, description="JSON data of the task")

class TaskResponse(BaseResponse):
    data: Optional[Task] = None

class TaskDetailedResponse(BaseResponse):
    data: Optional[TaskDetailed] = None

# TaskRefinementResponse removed - tasks are now independent of refinements

class TaskListResponse(BaseResponse):
    data: List[Task] = []
    total: int = Field(0, description="Total number of tasks")


# Chat Models
class ChatBase(BaseModel):
    user_id: str = Field(..., description="ID of the user who owns this chat")
    project_id: Optional[str] = Field(None, description="ID of the project this chat belongs to")
    title: Optional[str] = Field(None, description="Title or name of the chat")


class ChatCreate(ChatBase):
    pass


class ChatUpdate(BaseModel):
    title: Optional[str] = Field(None, description="Title or name of the chat")
    project_id: Optional[str] = Field(None, description="ID of the project this chat belongs to")


class Chat(ChatBase):
    chat_id: str = Field(..., description="Unique identifier for the chat")
    created_at: datetime = Field(..., description="When the chat was created")
    updated_at: Optional[datetime] = Field(None, description="When the chat was last updated")
    message_count: Optional[int] = Field(0, description="Number of messages in this chat")


class ChatResponse(BaseResponse):
    data: Optional[Chat] = None


class ChatListResponse(BaseResponse):
    data: List[Chat] = []
    total: int = Field(0, description="Total number of chats")


# Message Models
class MessageBase(BaseModel):
    content: str = Field(..., description="Content of the message")
    role: MessageRole = Field(..., description="Role of the message sender")
    metadata: Optional[Dict[str, Any]] = Field(default={ "timestamp": datetime.now().isoformat() }, description="Additional metadata for the message")


class MessageCreate(MessageBase):
    chat_id: str = Field(..., description="ID of the chat this message belongs to")


class MessageUpdate(BaseModel):
    content: Optional[str] = Field(None, description="Content of the message")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata for the message")


class Message(MessageBase):
    message_id: str = Field(..., description="Unique identifier for the message")
    chat_id: str = Field(..., description="ID of the chat this message belongs to")
    created_at: datetime = Field(..., description="When the message was created")
    updated_at: Optional[datetime] = Field(None, description="When the message was last updated")


class MessageResponse(BaseResponse):
    data: Optional[Message] = None


class MessageListResponse(BaseResponse):
    data: List[Message] = []
    total: int = Field(0, description="Total number of messages")


# Chat with Messages (for detailed chat view)
class ChatWithMessages(Chat):
    messages: List[Message] = Field([], description="Messages in this chat")
    tasks: List[Task] = Field([], description="Tasks associated with this chat")


class ChatWithMessagesResponse(BaseResponse):
    data: Optional[ChatWithMessages] = None


# Scheduled Task Models
class ScheduledTaskBase(BaseModel):
    task_id: str = Field(..., description="ID of the task to schedule")
    cron_expression: str = Field(..., description="Cron expression for scheduling")
    is_active: bool = Field(True, description="Whether the scheduled task is active")
    description: Optional[str] = Field(default="", description="Description of the scheduled task")


class ScheduledTaskCreate(ScheduledTaskBase):
    pass


class ScheduledTaskUpdate(BaseModel):
    cron_expression: Optional[str] = Field(default=None, description="Cron expression for scheduling")
    is_active: Optional[bool] = Field(default=None, description="Whether the scheduled task is active")
    description: Optional[str] = Field(default=None, description="Description of the scheduled task")


class ScheduledTask(ScheduledTaskBase):
    id: str = Field(..., description="Unique identifier for the scheduled task")
    created_at: datetime = Field(..., description="When the scheduled task was created")
    updated_at: datetime = Field(..., description="When the scheduled task was last updated")
    last_run: Optional[datetime] = Field(default=None, description="When the scheduled task last ran")
    next_run: Optional[datetime] = Field(default=None, description="When the scheduled task will next run")


class ScheduledTaskResponse(BaseResponse):
    data: Optional[ScheduledTask] = None


class ScheduledTaskListResponse(BaseResponse):
    data: List[ScheduledTask] = []
    total: int = Field(0, description="Total number of scheduled tasks")
