# from pydantic import BaseModel, Field
# from typing import Optional

# class AgentConfig(BaseModel):
#     """Configuration for browser agent"""
#     provider: str = Field(default="openai", description="LLM provider (openai, google, anthropic, etc.)")
#     model_name: str = Field(default="gpt-4o-mini", description="Model name")
#     temperature: float = Field(default=0.6, description="Temperature for LLM")
#     api_key: Optional[str] = Field(default=None, description="API key for LLM provider")
#     base_url: Optional[str] = Field(default=None, description="Custom base URL for LLM")
#     max_steps: int = Field(default=100, description="Maximum steps for agent")
#     max_actions: int = Field(default=10, description="Maximum actions per step")
#     use_vision: bool = Field(default=True, description="Enable vision capabilities")
#     tool_calling_method: str = Field(default="auto", description="Tool calling method")
