"""
Refinement Controller for Prompt Refinement System
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from datetime import datetime

from ..models.schemas import BaseResponse
from ...prompt_refinement.manager_singleton import get_refinement_manager

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["refinement"])

# Global refinement manager instance
refinement_manager = get_refinement_manager()


# Pydantic models for refinement endpoints
from pydantic import BaseModel
from typing import Optional, List


class RefinementStartRequest(BaseModel):
    """Request to start prompt refinement."""
    prompt: str


class RefinementStartResponse(BaseResponse):
    """Response when starting prompt refinement."""
    session_id: Optional[str] = None
    needs_refinement: bool
    clarifying_questions: Optional[List[str]] = None
    refined_prompt: Optional[str] = None
    state: str
    analysis: Optional[Dict[str, Any]] = None


class RefinementAnswersRequest(BaseModel):
    """Request to provide answers to clarifying questions."""
    session_id: str
    answers: List[str]


class RefinementAnswersResponse(BaseResponse):
    """Response after providing answers."""
    session_id: str
    needs_further_refinement: bool
    refined_prompt: str
    clarifying_questions: Optional[List[str]] = None
    state: str
    iteration: int


class RefinementStatusResponse(BaseResponse):
    """Response for refinement session status."""
    session_id: str
    original_prompt: str
    state: str
    created_at: str
    updated_at: str
    analysis_result: Optional[Dict[str, Any]] = None
    clarifying_questions: List[str]
    user_answers: List[str]
    refined_prompt: Optional[str] = None
    iteration_count: int
    max_iterations: int
    history: List[Dict[str, Any]]


@router.post("/refinement/start", response_model=RefinementStartResponse)
async def start_refinement(request: RefinementStartRequest):
    """Start a new prompt refinement session."""
    try:
        result = refinement_manager.start_refinement_session(request.prompt)
        
        return RefinementStartResponse(
            success=True,
            message=result.get("message", ""),
            session_id=result.get("session_id"),
            needs_refinement=result.get("needs_refinement", True),
            clarifying_questions=result.get("clarifying_questions"),
            refined_prompt=result.get("refined_prompt"),
            state=result.get("state", "unknown"),
            analysis=result.get("analysis")
        )
        
    except Exception as e:
        logger.error(f"Error starting refinement: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/refinement/{session_id}/answers", response_model=RefinementAnswersResponse)
async def provide_refinement_answers(session_id: str, request: RefinementAnswersRequest):
    """Provide answers to clarifying questions."""
    try:
        if request.session_id != session_id:
            raise HTTPException(status_code=400, detail="Session ID mismatch")
        
        result = refinement_manager.provide_answers(session_id, request.answers)
        
        if result.get("error"):
            raise HTTPException(status_code=400, detail=result["error"])
        
        return RefinementAnswersResponse(
            success=True,
            message=result.get("message", ""),
            session_id=session_id,
            needs_further_refinement=result.get("needs_further_refinement", False),
            refined_prompt=result.get("refined_prompt", ""),
            clarifying_questions=result.get("clarifying_questions"),
            state=result.get("state", "unknown"),
            iteration=result.get("iteration", 0)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error providing answers: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/refinement/{session_id}/status", response_model=RefinementStatusResponse)
async def get_refinement_status(session_id: str):
    """Get the status of a refinement session."""
    try:
        result = refinement_manager.get_session_status(session_id)
        
        if result.get("error"):
            raise HTTPException(status_code=404, detail=result["error"])
        
        return RefinementStatusResponse(
            success=True,
            message="Session status retrieved successfully",
            **result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting refinement status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/refinement/sessions")
async def list_refinement_sessions():
    """List all active refinement sessions."""
    try:
        sessions = refinement_manager.list_active_sessions()
        return {
            "success": True,
            "message": f"Found {len(sessions)} active sessions",
            "sessions": sessions
        }
        
    except Exception as e:
        logger.error(f"Error listing refinement sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/refinement/{session_id}")
async def delete_refinement_session(session_id: str):
    """Delete a refinement session."""
    try:
        # Complete and clean up the session
        refined_prompt = refinement_manager.complete_session(session_id)
        
        if refined_prompt is None:
            raise HTTPException(status_code=404, detail="Session not found or not completed")
        
        return {
            "success": True,
            "message": "Session deleted successfully",
            "final_refined_prompt": refined_prompt
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting refinement session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/refinement/cleanup")
async def cleanup_old_sessions():
    """Clean up old refinement sessions."""
    try:
        refinement_manager.cleanup_old_sessions()
        
        return {
            "success": True,
            "message": "Old sessions cleaned up successfully"
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))
