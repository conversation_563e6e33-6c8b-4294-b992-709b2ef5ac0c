from fastapi import <PERSON><PERSON>out<PERSON>, Request, Query
from fastapi.responses import StreamingResponse
import asyncio
import json
from ..helpers.event_bus import register_chat_queue, unregister_chat_queue

router = APIRouter()

@router.get("/sse/messages")
async def sse_chat_messages(request: Request, chat_id: str = Query(...)):
    
    async def event_generator():
        queue = register_chat_queue(chat_id)
        try:
            while True:
                get_task = asyncio.create_task(queue.get())
                disconnect_task = asyncio.create_task(request.is_disconnected())
                done, pending = await asyncio.wait(
                    [get_task, disconnect_task],
                    return_when=asyncio.FIRST_COMPLETED
                )
                if disconnect_task in done and disconnect_task.result():
                    get_task.cancel()
                    break
                if get_task in done:
                    message = get_task.result()
                    if message is None:
                        # Received disconnect signal, break the loop
                        break
                    # Ensure message is properly formatted as JSON string
                    if isinstance(message, dict):
                        message_str = json.dumps(message)
                    else:
                        message_str = str(message)
                    yield f"data: {message_str}\n\n"
                for task in pending:
                    task.cancel()
        finally:
            unregister_chat_queue(chat_id, queue)
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    ) 