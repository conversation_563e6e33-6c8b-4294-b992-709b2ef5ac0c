from fastapi import APIRouter, HTTPException, Query
from typing import Optional
from ..models.schemas import (
    ChatCreate, ChatUpdate, ChatResponse, ChatListResponse, 
    ChatWithMessagesResponse, MessageCreate, MessageResponse, 
    MessageListResponse, MessageUpdate
)
from ..services.chat_service import chat_service
from ..services.message_service import message_service

router = APIRouter(prefix="/chats", tags=["chats"])


# Chat endpoints
@router.post("/", response_model=ChatResponse, status_code=201)
async def create_chat(chat_data: ChatCreate):
    """Create a new chat."""
    response = await chat_service.create_chat(chat_data)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    return response


@router.get("/", response_model=ChatListResponse)
async def get_all_chats(
    skip: int = Query(0, ge=0, description="Number of chats to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of chats to return"),
    user_id: Optional[str] = Query(None, description="Filter chats by user ID"),
    project_id: Optional[str] = Query(None, description="Filter chats by project ID")
):
    """Get all chats with optional pagination, user, and project filtering."""
    if user_id:
        response = await chat_service.get_user_chats(user_id, skip=skip, limit=limit)
    elif project_id:
        response = await chat_service.get_project_chats(project_id, skip=skip, limit=limit)
    else:
        response = await chat_service.get_all_chats(skip=skip, limit=limit)
    
    if not response.success:
        raise HTTPException(status_code=500, detail=response.message)
    return response


@router.get("/{chat_id}", response_model=ChatResponse)
async def get_chat(chat_id: str):
    """Get a specific chat by ID."""
    response = await chat_service.get_chat(chat_id)
    if not response.success:
        raise HTTPException(status_code=404, detail=response.message)
    return response


@router.get("/{chat_id}/details", response_model=ChatWithMessagesResponse)
async def get_chat_with_messages(
    chat_id: str,
    message_limit: int = Query(100, ge=1, le=1000, description="Maximum number of messages to return")
):
    """Get a chat with its messages and tasks."""
    response = await chat_service.get_chat_with_messages(chat_id, message_limit=message_limit)
    if not response.success:
        raise HTTPException(status_code=404, detail=response.message)
    return response


@router.put("/{chat_id}", response_model=ChatResponse)
async def update_chat(chat_id: str, chat_update: ChatUpdate):
    """Update a specific chat."""
    response = await chat_service.update_chat(chat_id, chat_update)
    if not response.success:
        if "not found" in response.message:
            raise HTTPException(status_code=404, detail=response.message)
        raise HTTPException(status_code=400, detail=response.message)
    return response


@router.delete("/{chat_id}", response_model=ChatResponse)
async def delete_chat(chat_id: str):
    """Delete a specific chat."""
    response = await chat_service.delete_chat(chat_id)
    if not response.success:
        if "not found" in response.message:
            raise HTTPException(status_code=404, detail=response.message)
        raise HTTPException(status_code=400, detail=response.message)
    return response


# Message endpoints within chats
@router.post("/{chat_id}/messages", response_model=MessageResponse, status_code=201)
async def create_message(chat_id: str, message_data: MessageCreate):
    """Create a new message in a chat."""
    # Ensure the message is associated with the correct chat
    message_data.chat_id = chat_id
    response = await message_service.create_message(message_data)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    return response


@router.get("/{chat_id}/messages", response_model=MessageListResponse)
async def get_chat_messages(
    chat_id: str,
    skip: int = Query(0, ge=0, description="Number of messages to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of messages to return")
):
    """Get all messages in a chat."""
    response = await message_service.get_chat_messages(chat_id, skip=skip, limit=limit)
    if not response.success:
        raise HTTPException(status_code=500, detail=response.message)
    return response


@router.get("/{chat_id}/messages/{message_id}", response_model=MessageResponse)
async def get_message(chat_id: str, message_id: str):
    """Get a specific message by ID."""
    response = await message_service.get_message(message_id)
    if not response.success:
        raise HTTPException(status_code=404, detail=response.message)
    
    # Verify the message belongs to the specified chat
    if not response.data or response.data.chat_id != chat_id:
        raise HTTPException(status_code=404, detail="Message not found in this chat")
    
    return response


@router.put("/{chat_id}/messages/{message_id}", response_model=MessageResponse)
async def update_message(chat_id: str, message_id: str, message_update: MessageUpdate):
    """Update a specific message."""
    # First verify the message belongs to this chat
    message_response = await message_service.get_message(message_id)
    if not message_response.success or not message_response.data or message_response.data.chat_id != chat_id:
        raise HTTPException(status_code=404, detail="Message not found in this chat")
    
    response = await message_service.update_message(message_id, message_update)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    return response


@router.delete("/{chat_id}/messages/{message_id}", response_model=MessageResponse)
async def delete_message(chat_id: str, message_id: str):
    """Delete a specific message."""
    # First verify the message belongs to this chat
    message_response = await message_service.get_message(message_id)
    if not message_response.success or not message_response.data or message_response.data.chat_id != chat_id:
        raise HTTPException(status_code=404, detail="Message not found in this chat")
    
    response = await message_service.delete_message(message_id)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    return response


# User-specific chat endpoints
@router.get("/users/{user_id}/chats", response_model=ChatListResponse)
async def get_user_chats(
    user_id: str,
    skip: int = Query(0, ge=0, description="Number of chats to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of chats to return")
):
    """Get all chats for a specific user."""
    response = await chat_service.get_user_chats(user_id, skip=skip, limit=limit)
    if not response.success:
        raise HTTPException(status_code=500, detail=response.message)
    return response
