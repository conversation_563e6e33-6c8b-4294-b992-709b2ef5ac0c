from fastapi import APIRouter
from datetime import datetime
from ..models.schemas import HealthCheck
from ..helpers.redis_client import get_redis_client_status

router = APIRouter(prefix="/health", tags=["health"])

# Store the server start time
SERVER_START_TIME = datetime.now()


@router.get("/", response_model=HealthCheck)
async def health_check():
    """Health check endpoint."""
    uptime = datetime.now() - SERVER_START_TIME
    uptime_str = str(uptime).split('.')[0]  # Remove microseconds

    # Test Redis connection
    try:
        status = get_redis_client_status()
        redis_status = "connected" if status else "unreachable"
    except Exception:
        redis_status = "unreachable"
    
    return HealthCheck(
        status="healthy",
        timestamp=datetime.now(),
        version="1.0.0",
        uptime=uptime_str,
        redis_status=redis_status
    )


@router.get("/ping")
async def ping():
    """Simple ping endpoint."""
    return {"message": "pong", "timestamp": datetime.now()}
