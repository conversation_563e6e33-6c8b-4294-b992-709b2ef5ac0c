from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from typing import Optional
from ..models.schemas import (
    TaskCreate, TaskUpdate, TaskStatusEnum, TaskResponse, TaskListResponse,
    ScheduledTaskCreate, ScheduledTaskUpdate, ScheduledTaskResponse, ScheduledTaskListResponse
)
from ..services.task_service import task_service
from ..services.scheduled_task_service import scheduled_task_service
from ..services.task_scheduler_service import task_scheduler_service
from fastapi.responses import PlainTextResponse

router = APIRouter(prefix="/tasks", tags=["tasks"])


@router.post("/", response_model=TaskResponse, status_code=201)
async def create_task(task_data: TaskCreate, background_tasks: BackgroundTasks):
    """Create a new task."""
    response = await task_service.create_task(task_data, background_tasks)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    return response


@router.get("/", response_model=TaskListResponse)
async def get_all_tasks(
    skip: int = Query(0, ge=0, description="Number of tasks to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of tasks to return"),
    status: Optional[TaskStatusEnum] = Query(None, description="Filter tasks by status")
):
    """Get all tasks with optional pagination and status filtering."""
    if status:
        response = await task_service.get_tasks_by_status(status)
    else:
        response = await task_service.get_all_tasks(skip=skip, limit=limit)
    
    if not response.success:
        raise HTTPException(status_code=500, detail=response.message)
    return response


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(task_id: str):
    """Get a specific task by ID."""
    response = await task_service.get_task(task_id)
    if not response.success:
        raise HTTPException(status_code=404, detail=response.message)
    return response


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(task_id: str, task_update: TaskUpdate):
    """Update a specific task."""
    response = await task_service.update_task(task_id, task_update)
    if not response.success:
        if "not found" in response.message:
            raise HTTPException(status_code=404, detail=response.message)
        else:
            raise HTTPException(status_code=400, detail=response.message)
    return response


@router.delete("/{task_id}", response_model=TaskResponse)
async def delete_task(task_id: str):
    """Delete a specific task."""
    response = await task_service.delete_task(task_id)
    if not response.success:
        if "not found" in response.message:
            raise HTTPException(status_code=404, detail=response.message)
        else:
            raise HTTPException(status_code=400, detail=response.message)
    return response


@router.patch("/{task_id}/status", response_model=TaskResponse)
async def update_task_status(task_id: str, status: TaskStatusEnum):
    """Update only the status of a specific task."""
    task_update = TaskUpdate(status=status)
    response = await task_service.update_task(task_id, task_update)
    if not response.success:
        if "not found" in response.message:
            raise HTTPException(status_code=404, detail=response.message)
        else:
            raise HTTPException(status_code=400, detail=response.message)
    return response


@router.post("/{task_id}/run-script", response_model=TaskResponse)
async def run_task_script(task_id: str, background_tasks: BackgroundTasks):
    """Run the playwright script of a specific task."""
    response = await task_service.run_playwright_script(task_id, background_tasks)
    if not response.success:
        if "not found" in response.message:
            raise HTTPException(status_code=404, detail=response.message)
        else:
            raise HTTPException(status_code=400, detail=response.message)
    return response

@router.get("/{task_id}/playwright_script", response_class=PlainTextResponse)
async def get_playwright_script(task_id: str):
    """Get the Playwright script for a specific task by ID."""
    response = await task_service.get_task(task_id)
    if not response.success or not response.data:
        raise HTTPException(status_code=404, detail="Task not found")
    playwright_script = getattr(response.data, "playwright_script", None)
    if not playwright_script:
        raise HTTPException(status_code=404, detail="Playwright script not found or not generated yet")
    return playwright_script


# Scheduled Task Endpoints
@router.post("/{task_id}/schedule", response_model=ScheduledTaskResponse, status_code=201)
async def create_scheduled_task(task_id: str, cron_expression: str = Query(..., description="Cron expression for scheduling"), description: Optional[str] = Query("", description="Description of the scheduled task")):
    """Create a scheduled task for a specific task ID with a cron expression."""
    scheduled_task_data = ScheduledTaskCreate(
        task_id=task_id,
        cron_expression=cron_expression,
        description=description,
        is_active=True
    )
    response = await scheduled_task_service.create_scheduled_task(scheduled_task_data)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    
    # Add to scheduler if successful
    if response.data:
        await task_scheduler_service.add_scheduled_task(response.data)
    
    return response


@router.get("/schedules", response_model=ScheduledTaskListResponse)
async def get_all_scheduled_tasks(
    skip: int = Query(0, ge=0, description="Number of scheduled tasks to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of scheduled tasks to return")
):
    """Get all scheduled tasks with optional pagination."""
    response = await scheduled_task_service.get_all_scheduled_tasks(skip=skip, limit=limit)
    if not response.success:
        raise HTTPException(status_code=500, detail=response.message)
    return response


@router.get("/schedules/{scheduled_task_id}", response_model=ScheduledTaskResponse)
async def get_scheduled_task(scheduled_task_id: str):
    """Get a specific scheduled task by ID."""
    response = await scheduled_task_service.get_scheduled_task(scheduled_task_id)
    if not response.success:
        raise HTTPException(status_code=404, detail=response.message)
    return response


@router.put("/schedules/{scheduled_task_id}", response_model=ScheduledTaskResponse)
async def update_scheduled_task(scheduled_task_id: str, scheduled_task_update: ScheduledTaskUpdate):
    """Update a specific scheduled task."""
    response = await scheduled_task_service.update_scheduled_task(scheduled_task_id, scheduled_task_update)
    if not response.success:
        if "not found" in response.message:
            raise HTTPException(status_code=404, detail=response.message)
        else:
            raise HTTPException(status_code=400, detail=response.message)
    
    # Update in scheduler if successful
    if response.data:
        await task_scheduler_service.update_scheduled_task(response.data)
    
    return response


@router.delete("/schedules/{scheduled_task_id}", response_model=ScheduledTaskResponse)
async def delete_scheduled_task(scheduled_task_id: str):
    """Delete a specific scheduled task."""
    response = await scheduled_task_service.delete_scheduled_task(scheduled_task_id)
    if not response.success:
        if "not found" in response.message:
            raise HTTPException(status_code=404, detail=response.message)
        else:
            raise HTTPException(status_code=400, detail=response.message)
    
    # Remove from scheduler
    await task_scheduler_service.remove_scheduled_task(scheduled_task_id)
    
    return response
