"""
Client SDK for DrCode UI Testing API

This module provides a Python client for interacting with the DrCode UI Testing API,
making it easy to create and manage browser automation tasks programmatically.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import time

try:
    import aiohttp
    import websockets
except ImportError:
    aiohttp = None
    websockets = None

from .models import (
    TaskRequest, TaskResponse, TaskStatus, AgentSettings, BrowserSettings,
    ConfigurationRequest, UserAssistanceRequest
)

logger = logging.getLogger(__name__)


class DrCodeAPIClient:
    """Client for DrCode UI Testing API."""
    
    def __init__(self, base_url: str = "http://localhost:8000", session_id: str = "default"):
        """
        Initialize the API client.
        
        Args:
            base_url: Base URL of the API server
            session_id: Session ID for managing multiple sessions
        """
        self.base_url = base_url.rstrip('/')
        self.session_id = session_id
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        if aiohttp:
            self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def _get_session(self):
        """Get or create session."""
        if not aiohttp:
            raise ImportError("aiohttp is required for API client. Install with: pip install aiohttp")
        
        if not self.session:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request."""
        session = self._get_session()
        url = f"{self.base_url}{endpoint}"
        
        # Add session_id to params if not already present
        if 'params' not in kwargs:
            kwargs['params'] = {}
        if 'session_id' not in kwargs['params']:
            kwargs['params']['session_id'] = self.session_id
        
        async with session.request(method, url, **kwargs) as response:
            if response.status >= 400:
                error_text = await response.text()
                raise Exception(f"API request failed: {response.status} - {error_text}")
            return await response.json()
    
    # Task Management
    
    async def create_task(
        self,
        task: str,
        agent_settings: Optional[AgentSettings] = None,
        browser_settings: Optional[BrowserSettings] = None
    ) -> TaskResponse:
        """Create a new browser automation task."""
        request_data = TaskRequest(
            task=task,
            agent_settings=agent_settings,
            browser_settings=browser_settings
        )
        
        response = await self._request(
            "POST", 
            "/tasks", 
            json=request_data.dict(exclude_none=True)
        )
        return TaskResponse(**response)
    
    async def get_task_status(self, task_id: str) -> TaskStatus:
        """Get the status of a task."""
        response = await self._request("GET", f"/tasks/{task_id}")
        return TaskStatus(**response)
    
    async def stop_task(self, task_id: str) -> Dict[str, str]:
        """Stop a running task."""
        return await self._request("POST", f"/tasks/{task_id}/stop")
    
    async def pause_task(self, task_id: str) -> Dict[str, str]:
        """Pause a running task."""
        return await self._request("POST", f"/tasks/{task_id}/pause")
    
    async def resume_task(self, task_id: str) -> Dict[str, str]:
        """Resume a paused task."""
        return await self._request("POST", f"/tasks/{task_id}/resume")
    
    async def clear_task(self, task_id: str) -> Dict[str, str]:
        """Clear/delete a task."""
        return await self._request("DELETE", f"/tasks/{task_id}")
    
    async def list_tasks(self) -> Dict[str, List[Dict[str, Any]]]:
        """List all tasks."""
        return await self._request("GET", "/tasks")
    
    async def provide_assistance(self, task_id: str, response: str) -> Dict[str, str]:
        """Provide user assistance for a task."""
        assistance = UserAssistanceRequest(task_id=task_id, response=response)
        return await self._request(
            "POST", 
            f"/tasks/{task_id}/assist", 
            json=assistance.dict()
        )
    
    # Task History and Files
    
    async def get_task_history(self, task_id: str) -> Dict[str, Any]:
        """Get task history and files."""
        return await self._request("GET", f"/tasks/{task_id}/history")
    
    async def download_task_history(self, task_id: str, save_path: str = None) -> str:
        """Download task history JSON file."""
        session = self._get_session()
        url = f"{self.base_url}/tasks/{task_id}/history/download"
        
        async with session.get(url) as response:
            if response.status >= 400:
                raise Exception(f"Download failed: {response.status}")
            
            content = await response.read()
            
            if save_path is None:
                save_path = f"task_{task_id}_history.json"
            
            with open(save_path, 'wb') as f:
                f.write(content)
            
            return save_path
    
    async def download_task_gif(self, task_id: str, save_path: str = None) -> str:
        """Download task recording GIF."""
        session = self._get_session()
        url = f"{self.base_url}/tasks/{task_id}/gif/download"
        
        async with session.get(url) as response:
            if response.status >= 400:
                raise Exception(f"Download failed: {response.status}")
            
            content = await response.read()
            
            if save_path is None:
                save_path = f"task_{task_id}_recording.gif"
            
            with open(save_path, 'wb') as f:
                f.write(content)
            
            return save_path
    
    # Configuration Management
    
    async def save_configuration(
        self,
        config_name: Optional[str] = None,
        agent_settings: Optional[AgentSettings] = None,
        browser_settings: Optional[BrowserSettings] = None
    ) -> Dict[str, Any]:
        """Save current configuration."""
        config_request = ConfigurationRequest(
            config_name=config_name,
            agent_settings=agent_settings,
            browser_settings=browser_settings
        )
        return await self._request(
            "POST", 
            "/config/save", 
            json=config_request.dict(exclude_none=True)
        )
    
    async def load_configuration(self, config_path: str) -> Dict[str, str]:
        """Load configuration from file."""
        return await self._request(
            "POST", 
            "/config/load", 
            params={"config_path": config_path}
        )
    
    # System Information
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get available models for all providers."""
        return await self._request("GET", "/models")
    
    async def get_provider_models(self, provider: str) -> Dict[str, Any]:
        """Get available models for a specific provider."""
        return await self._request("GET", f"/models/{provider}")
    
    async def get_providers(self) -> Dict[str, Any]:
        """Get list of available LLM providers."""
        return await self._request("GET", "/providers")
    
    async def get_qa_system_prompt(self) -> Dict[str, str]:
        """Get the current QA system prompt."""
        return await self._request("GET", "/system/prompt/qa")
    
    async def get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        return await self._request("GET", "/system/info")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        return await self._request("GET", "/health")
    
    # Helper Methods
    
    async def wait_for_task_completion(
        self, 
        task_id: str, 
        timeout: int = 300, 
        poll_interval: float = 2.0
    ) -> TaskStatus:
        """
        Wait for a task to complete.
        
        Args:
            task_id: Task ID to wait for
            timeout: Maximum time to wait in seconds
            poll_interval: How often to check status in seconds
            
        Returns:
            Final task status
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = await self.get_task_status(task_id)
            
            if status.status in ["completed", "error", "stopped"]:
                return status
            
            await asyncio.sleep(poll_interval)
        
        raise TimeoutError(f"Task {task_id} did not complete within {timeout} seconds")
    
    async def run_task_and_wait(
        self,
        task: str,
        agent_settings: Optional[AgentSettings] = None,
        browser_settings: Optional[BrowserSettings] = None,
        timeout: int = 300
    ) -> TaskStatus:
        """
        Create a task and wait for completion.
        
        Args:
            task: Task description
            agent_settings: Agent configuration
            browser_settings: Browser configuration
            timeout: Maximum time to wait
            
        Returns:
            Final task status
        """
        task_response = await self.create_task(task, agent_settings, browser_settings)
        return await self.wait_for_task_completion(task_response.task_id, timeout)


class DrCodeWebSocketClient:
    """WebSocket client for real-time task updates."""
    
    def __init__(self, base_url: str = "ws://localhost:8000"):
        """Initialize WebSocket client."""
        self.base_url = base_url.replace('http://', 'ws://').replace('https://', 'wss://')
        self.websocket = None
        self.message_handlers = {}
    
    async def connect(self, task_id: str = None):
        """Connect to WebSocket."""
        if not websockets:
            raise ImportError("websockets is required. Install with: pip install websockets")
        
        if task_id:
            url = f"{self.base_url}/ws/tasks/{task_id}"
        else:
            url = f"{self.base_url}/ws"
        
        self.websocket = await websockets.connect(url)
    
    async def disconnect(self):
        """Disconnect from WebSocket."""
        if self.websocket:
            await self.websocket.close()
    
    def on_message(self, message_type: str, handler):
        """Register a message handler."""
        self.message_handlers[message_type] = handler
    
    async def listen(self):
        """Listen for messages."""
        if not self.websocket:
            raise Exception("Not connected to WebSocket")
        
        async for message in self.websocket:
            try:
                data = json.loads(message)
                message_type = data.get('type')
                
                if message_type in self.message_handlers:
                    await self.message_handlers[message_type](data)
                else:
                    logger.debug(f"Unhandled message type: {message_type}")
            
            except json.JSONDecodeError:
                logger.error(f"Failed to decode WebSocket message: {message}")
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")


# Convenience functions

async def quick_task(
    task: str,
    llm_provider: str = "openai",
    llm_model: str = "gpt-4.1-mini",
    headless: bool = True,
    timeout: int = 300,
    api_url: str = "http://localhost:8000"
) -> TaskStatus:
    """
    Quick way to run a single task.
    
    Args:
        task: Task description
        llm_provider: LLM provider to use
        llm_model: LLM model to use
        headless: Run browser in headless mode
        timeout: Maximum time to wait
        api_url: API server URL
        
    Returns:
        Final task status
    """
    agent_settings = AgentSettings(
        llm_provider=llm_provider,
        llm_model_name=llm_model
    )
    browser_settings = BrowserSettings(headless=headless)
    
    async with DrCodeAPIClient(api_url) as client:
        return await client.run_task_and_wait(
            task, agent_settings, browser_settings, timeout
        )


__all__ = [
    "DrCodeAPIClient",
    "DrCodeWebSocketClient", 
    "quick_task"
]
