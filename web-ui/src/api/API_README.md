# DrCode Structured API

A comprehensive FastAPI-based REST API for managing chats, tasks, and messages with Redis as the backend storage.

## Features

- **Chat Management**: Create, retrieve, update, and delete chats
- **Message System**: Full message lifecycle within chats with role-based messaging
- **Task Management**: Create and manage tasks associated with chats
- **User-based Organization**: Users can have multiple chats, each chat can have multiple messages and tasks
- **Redis Backend**: High-performance Redis storage with proper data relationships
- **RESTful Design**: Clean REST API design with proper HTTP status codes
- **Auto-generated Documentation**: Swagger/OpenAPI documentation available

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Users       │    │     Chats       │    │    Messages     │
│                 │    │                 │    │                 │
│ • user_id       │◄──►│ • chat_id       │◄──►│ • message_id    │
│                 │    │ • user_id       │    │ • chat_id       │
│                 │    │ • project_id    │    │ • content       │
│                 │    │ • title         │    │ • role          │
│                 │    │ • created_at    │    │ • metadata      │
│                 │    │ • message_count │    │ • created_at    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                │
                        ┌─────────────────┐
                        │     Tasks       │
                        │                 │
                        │ • task_id       │
                        │ • chat_id       │
                        │ • title         │
                        │ • description   │
                        │ • priority      │
                        │ • status        │
                        │ • user_id       │
                        │ • project_id    │
                        └─────────────────┘
```

## Getting Started

### Prerequisites

- Python 3.8+
- Redis server running (localhost:6379 by default)

### Installation

1. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

2. Set up Redis (if not already running):

   ```bash
   # On macOS with Homebrew
   brew install redis
   brew services start redis

   # On Ubuntu/Debian
   sudo apt-get install redis-server
   sudo systemctl start redis-server

   # Using Docker
   docker run -d -p 6379:6379 redis:alpine
   ```

3. Set environment variables (optional):
   ```bash
   export REDIS_HOST=localhost
   export REDIS_PORT=6379
   export REDIS_DB=0
   export REDIS_PASSWORD=  # if password protected
   ```

### Running the API

Start the server:

```bash
python src/run_api.py
```

The API will be available at:

- **API Base URL**: http://localhost:8000
- **Interactive Documentation**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/api/v1/health

## API Endpoints

### Health Check

- `GET /api/v1/health` - Check API and Redis status
- `GET /api/v1/health/ping` - Simple ping endpoint

### Chat Management

- `POST /api/v1/chats` - Create a new chat
- `GET /api/v1/chats` - List all chats (with pagination)
- `GET /api/v1/chats/{chat_id}` - Get specific chat
- `GET /api/v1/chats/{chat_id}/details` - Get chat with messages and tasks
- `PUT /api/v1/chats/{chat_id}` - Update chat
- `DELETE /api/v1/chats/{chat_id}` - Delete chat
- `GET /api/v1/chats/users/{user_id}/chats` - Get user's chats

### Message Management

- `POST /api/v1/chats/{chat_id}/messages` - Create message in chat
- `GET /api/v1/chats/{chat_id}/messages` - Get all messages in chat
- `GET /api/v1/chats/{chat_id}/messages/{message_id}` - Get specific message
- `PUT /api/v1/chats/{chat_id}/messages/{message_id}` - Update message
- `DELETE /api/v1/chats/{chat_id}/messages/{message_id}` - Delete message

### Task Management

- `POST /api/v1/tasks` - Create a new task
- `GET /api/v1/tasks` - List all tasks (with pagination and filtering)
- `GET /api/v1/tasks/{task_id}` - Get specific task
- `PUT /api/v1/tasks/{task_id}` - Update task
- `DELETE /api/v1/tasks/{task_id}` - Delete task

## API Examples

### Creating a Chat

```bash
curl -X POST "http://localhost:8000/api/v1/chats" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "project_id": "project456",
    "title": "My First Chat"
  }'
```

Response:

```json
{
  "success": true,
  "message": "Chat created successfully",
  "data": {
    "chat_id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "user123",
    "project_id": "project456",
    "title": "My First Chat",
    "created_at": "2024-01-15T10:30:00.123456",
    "updated_at": "2024-01-15T10:30:00.123456",
    "message_count": 0
  }
}
```

### Adding Messages to a Chat

```bash
curl -X POST "http://localhost:8000/api/v1/chats/550e8400-e29b-41d4-a716-446655440000/messages" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Hello, this is my first message!",
    "role": "user",
    "metadata": {
      "source": "web_ui",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  }'
```

### Creating a Task

```bash
curl -X POST "http://localhost:8000/api/v1/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Complete API Documentation",
    "description": "Write comprehensive API documentation with examples",
    "priority": "high",
    "user_id": "user123",
    "project_id": "project456",
    "chat_id": "550e8400-e29b-41d4-a716-446655440000"
  }'
```

### Getting Chat with Messages and Tasks

```bash
curl "http://localhost:8000/api/v1/chats/550e8400-e29b-41d4-a716-446655440000/details"
```

Response:

```json
{
  "success": true,
  "message": "Chat with messages retrieved successfully",
  "data": {
    "chat_id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "user123",
    "project_id": "project456",
    "title": "My First Chat",
    "created_at": "2024-01-15T10:30:00.123456",
    "updated_at": "2024-01-15T10:30:00.123456",
    "message_count": 1,
    "messages": [
      {
        "message_id": "msg-123",
        "chat_id": "550e8400-e29b-41d4-a716-446655440000",
        "content": "Hello, this is my first message!",
        "role": "user",
        "metadata": {
          "source": "web_ui",
          "timestamp": "2024-01-15T10:30:00Z"
        },
        "created_at": "2024-01-15T10:30:00.123456",
        "updated_at": "2024-01-15T10:30:00.123456"
      }
    ],
    "tasks": [
      {
        "id": "task-456",
        "title": "Complete API Documentation",
        "description": "Write comprehensive API documentation with examples",
        "priority": "high",
        "status": "pending",
        "user_id": "user123",
        "project_id": "project456",
        "chat_id": "550e8400-e29b-41d4-a716-446655440000",
        "created_at": "2024-01-15T10:30:00.123456",
        "updated_at": "2024-01-15T10:30:00.123456"
      }
    ]
  }
}
```

## Data Models

### Chat

- `chat_id`: Unique identifier
- `user_id`: Owner of the chat
- `project_id`: Optional project association
- `title`: Optional chat title
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
- `message_count`: Number of messages in chat

### Message

- `message_id`: Unique identifier
- `chat_id`: Associated chat
- `content`: Message content
- `role`: Message role (user, assistant, system)
- `metadata`: Optional additional data
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### Task

- `id`: Unique identifier
- `title`: Task title
- `description`: Task description
- `priority`: Task priority (low, medium, high, urgent)
- `status`: Task status (pending, in_progress, completed, cancelled)
- `user_id`: Task owner
- `project_id`: Optional project association
- `chat_id`: Associated chat
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

## Error Handling

The API uses standard HTTP status codes:

- `200 OK` - Successful request
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request data
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error

All error responses follow this format:

```json
{
  "detail": "Error message describing what went wrong"
}
```

## Configuration

Environment variables for Redis configuration:

| Variable         | Default     | Description                  |
| ---------------- | ----------- | ---------------------------- |
| `REDIS_HOST`     | `localhost` | Redis server hostname        |
| `REDIS_PORT`     | `6379`      | Redis server port            |
| `REDIS_DB`       | `0`         | Redis database number        |
| `REDIS_PASSWORD` | `None`      | Redis password (if required) |

## Development

### Project Structure

```
structured-api/
├── main.py                 # FastAPI app and startup
├── controllers/            # HTTP request handlers
│   ├── chat_controller.py
│   ├── health_controller.py
│   └── task_controller.py
├── services/               # Business logic layer
│   ├── chat_service.py
│   ├── message_service.py
│   └── task_service.py
├── repository/             # Data access layer
│   ├── chat_repository.py
│   ├── message_repository.py
│   └── task_repository.py
├── models/                 # Data models and schemas
│   ├── schemas.py
│   └── task_model.py
└── helpers/                # Utility functions
    └── redis_client.py
```

### Adding New Features

1. **Add new models** in `models/schemas.py`
2. **Create repository** in `repository/` for data access
3. **Create service** in `services/` for business logic
4. **Create controller** in `controllers/` for HTTP endpoints
5. **Register router** in `main.py`

### Testing

Check health status:

```bash
curl http://localhost:8000/api/v1/health
```

View API documentation:

- Open http://localhost:8000/docs in your browser

## Production Deployment

1. **Use environment variables** for configuration
2. **Set up proper Redis configuration** with persistence
3. **Configure CORS** for specific origins
4. **Use a production ASGI server** like Gunicorn with Uvicorn workers
5. **Set up monitoring** and logging
6. **Configure Redis clustering** for high availability

Example production startup:

```bash
gunicorn -w 4 -k uvicorn.workers.UvicornWorker src.structured-api.main:app
```
