from typing import List, Optional
from ..models.schemas import (
    Message, MessageCreate, MessageUpdate, MessageResponse, MessageListResponse
)
from ..repository.message_repository import message_repository
from ..helpers.event_bus import publish_chat_message

class MessageService:
    """Service layer for message operations."""
    
    def __init__(self):
        self.repository = message_repository
    
    async def create_message(self, message_data: MessageCreate) -> MessageResponse:
        """Create a new message."""
        try:
            message = self.repository.create_message(message_data)
            await publish_chat_message(message_data.chat_id, message.content)
            return MessageResponse(
                success=True,
                message="Message created successfully",
                data=message
            )
        except Exception as e:
            return MessageResponse(
                success=False,
                message=f"Failed to create message: {str(e)}",
                data=None
            )
    
    async def get_message(self, message_id: str) -> MessageResponse:
        """Get a message by ID."""
        try:
            message = self.repository.get_message(message_id)
            if message:
                return MessageResponse(
                    success=True,
                    message="Message retrieved successfully",
                    data=message
                )
            else:
                return MessageResponse(
                    success=False,
                    message=f"Message with ID {message_id} not found",
                    data=None
                )
        except Exception as e:
            return MessageResponse(
                success=False,
                message=f"Failed to retrieve message: {str(e)}",
                data=None
            )
    
    async def get_chat_messages(self, chat_id: str, skip: int = 0, limit: int = 100) -> MessageListResponse:
        """Get all messages in a chat."""
        try:
            messages = self.repository.get_chat_messages(chat_id, skip=skip, limit=limit)
            total = self.repository.get_chat_message_count(chat_id)
            
            return MessageListResponse(
                success=True,
                message="Messages retrieved successfully",
                data=messages,
                total=total
            )
        except Exception as e:
            return MessageListResponse(
                success=False,
                message=f"Failed to retrieve messages: {str(e)}",
                data=[],
                total=0
            )
    
    async def update_message(self, message_id: str, message_update: MessageUpdate) -> MessageResponse:
        """Update a message."""
        try:
            message = self.repository.update_message(message_id, message_update)
            if message:
                return MessageResponse(
                    success=True,
                    message="Message updated successfully",
                    data=message
                )
            else:
                return MessageResponse(
                    success=False,
                    message=f"Message with ID {message_id} not found",
                    data=None
                )
        except Exception as e:
            return MessageResponse(
                success=False,
                message=f"Failed to update message: {str(e)}",
                data=None
            )
    
    async def delete_message(self, message_id: str) -> MessageResponse:
        """Delete a message."""
        try:
            success = self.repository.delete_message(message_id)
            if success:
                return MessageResponse(
                    success=True,
                    message="Message deleted successfully",
                    data=None
                )
            else:
                return MessageResponse(
                    success=False,
                    message=f"Message with ID {message_id} not found",
                    data=None
                )
        except Exception as e:
            return MessageResponse(
                success=False,
                message=f"Failed to delete message: {str(e)}",
                data=None
            )


# Global service instance
message_service = MessageService()
