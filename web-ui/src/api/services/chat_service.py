from typing import List, Optional
from ..models.schemas import (
    Chat, ChatCreate, ChatUpdate, ChatResponse, ChatListResponse, 
    ChatWithMessages, ChatWithMessagesResponse, Task
)
from ..repository.chat_repository import chat_repository


class ChatService:
    """Service layer for chat operations."""
    
    def __init__(self):
        self.repository = chat_repository
    
    async def create_chat(self, chat_data: ChatCreate) -> ChatResponse:
        """Create a new chat."""
        try:
            chat = self.repository.create_chat(chat_data)
            return ChatResponse(
                success=True,
                message="Chat created successfully",
                data=chat
            )
        except Exception as e:
            return ChatResponse(
                success=False,
                message=f"Failed to create chat: {str(e)}",
                data=None
            )
    
    async def get_chat(self, chat_id: str) -> ChatResponse:
        """Get a chat by ID."""
        try:
            chat = self.repository.get_chat(chat_id)
            if chat:
                return ChatResponse(
                    success=True,
                    message="Chat retrieved successfully",
                    data=chat
                )
            else:
                return ChatResponse(
                    success=False,
                    message=f"Chat with ID {chat_id} not found",
                    data=None
                )
        except Exception as e:
            return ChatResponse(
                success=False,
                message=f"Failed to retrieve chat: {str(e)}",
                data=None
            )
    
    async def get_all_chats(self, skip: int = 0, limit: int = 100) -> ChatListResponse:
        """Get all chats with pagination."""
        try:
            chats = self.repository.get_all_chats(skip=skip, limit=limit)
            total = self.repository.get_chat_count()
            
            return ChatListResponse(
                success=True,
                message="Chats retrieved successfully",
                data=chats,
                total=total
            )
        except Exception as e:
            return ChatListResponse(
                success=False,
                message=f"Failed to retrieve chats: {str(e)}",
                data=[],
                total=0
            )
    
    async def get_user_chats(self, user_id: str, skip: int = 0, limit: int = 100) -> ChatListResponse:
        """Get all chats for a specific user."""
        try:
            chats = self.repository.get_user_chats(user_id, skip=skip, limit=limit)
            total = self.repository.get_user_chat_count(user_id)
            
            return ChatListResponse(
                success=True,
                message="User chats retrieved successfully",
                data=chats,
                total=total
            )
        except Exception as e:
            return ChatListResponse(
                success=False,
                message=f"Failed to retrieve user chats: {str(e)}",
                data=[],
                total=0
            )
    
    async def get_project_chats(self, project_id: str, skip: int = 0, limit: int = 100) -> ChatListResponse:
        """Get all chats for a specific project."""
        try:
            chats = self.repository.get_project_chats(project_id, skip=skip, limit=limit)
            total = len(chats)  # Optionally, implement a more efficient count if needed
            return ChatListResponse(
                success=True,
                message="Project chats retrieved successfully",
                data=chats,
                total=total
            )
        except Exception as e:
            return ChatListResponse(
                success=False,
                message=f"Failed to retrieve project chats: {str(e)}",
                data=[],
                total=0
            )
    
    async def get_chat_with_messages(self, chat_id: str, message_limit: int = 100) -> ChatWithMessagesResponse:
        """Get a chat with its messages and tasks."""
        try:
            chat_with_messages = self.repository.get_chat_with_messages(chat_id, message_limit=message_limit)
            if chat_with_messages:
                return ChatWithMessagesResponse(
                    success=True,
                    message="Chat with messages retrieved successfully",
                    data=chat_with_messages
                )
            else:
                return ChatWithMessagesResponse(
                    success=False,
                    message=f"Chat with ID {chat_id} not found",
                    data=None
                )
        except Exception as e:
            return ChatWithMessagesResponse(
                success=False,
                message=f"Failed to retrieve chat with messages: {str(e)}",
                data=None
            )
    
    async def update_chat(self, chat_id: str, chat_update: ChatUpdate) -> ChatResponse:
        """Update a chat."""
        try:
            chat = self.repository.update_chat(chat_id, chat_update)
            if chat:
                return ChatResponse(
                    success=True,
                    message="Chat updated successfully",
                    data=chat
                )
            else:
                return ChatResponse(
                    success=False,
                    message=f"Chat with ID {chat_id} not found",
                    data=None
                )
        except Exception as e:
            return ChatResponse(
                success=False,
                message=f"Failed to update chat: {str(e)}",
                data=None
            )
    
    async def delete_chat(self, chat_id: str) -> ChatResponse:
        """Delete a chat."""
        try:
            success = self.repository.delete_chat(chat_id)
            if success:
                return ChatResponse(
                    success=True,
                    message="Chat deleted successfully",
                    data=None
                )
            else:
                return ChatResponse(
                    success=False,
                    message=f"Chat with ID {chat_id} not found",
                    data=None
                )
        except Exception as e:
            return ChatResponse(
                success=False,
                message=f"Failed to delete chat: {str(e)}",
                data=None
            )


# Global service instance
chat_service = ChatService()
