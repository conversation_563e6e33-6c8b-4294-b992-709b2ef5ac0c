import logging
from typing import List, Optional
from datetime import datetime
try:
    from croniter import croniter
except ImportError:
    croniter = None
from ..models.schemas import (
    ScheduledTask, ScheduledTaskCreate, ScheduledTaskUpdate, 
    ScheduledTaskResponse, ScheduledTaskListResponse
)
from ..repository.scheduled_task_repository import scheduled_task_repository
from ..repository.task_repository import task_repository

logger = logging.getLogger(__name__)


class ScheduledTaskService:
    """Service layer for scheduled task operations."""
    
    def __init__(self):
        self.repository = scheduled_task_repository
        self.task_repository = task_repository
    
    async def create_scheduled_task(self, scheduled_task_data: ScheduledTaskCreate) -> ScheduledTaskResponse:
        """Create a new scheduled task."""
        try:
            if croniter is None:
                return ScheduledTaskResponse(
                    success=False,
                    message="croniter library not available. Please install: pip install croniter",
                    data=None
                )
            
            # Validate that the task exists
            task = self.task_repository.get_task(scheduled_task_data.task_id)
            if not task:
                return ScheduledTaskResponse(
                    success=False,
                    message=f"Task with ID {scheduled_task_data.task_id} not found",
                    data=None
                )
            
            # Validate cron expression
            try:
                cron = croniter(scheduled_task_data.cron_expression, datetime.now())
                next_run_timestamp = cron.get_next()
                next_run = datetime.fromtimestamp(next_run_timestamp)
                logger.info(f"Cron expression '{scheduled_task_data.cron_expression}' is valid. Next run: {next_run}")
            except Exception as e:
                return ScheduledTaskResponse(
                    success=False,
                    message=f"Invalid cron expression: {str(e)}",
                    data=None
                )
            
            # Create the scheduled task
            scheduled_task = self.repository.create_scheduled_task(scheduled_task_data)
            
            # Calculate and update next run time
            if scheduled_task.is_active:
                cron = croniter(scheduled_task.cron_expression, datetime.now())
                next_run_timestamp = cron.get_next()
                next_run = datetime.fromtimestamp(next_run_timestamp)
                self.repository.update_scheduled_task_run_times(
                    scheduled_task.id, 
                    last_run=datetime.now(),  # Set current time as created time
                    next_run=next_run
                )
                # Refresh the scheduled task to get updated times
                scheduled_task = self.repository.get_scheduled_task(scheduled_task.id)
            
            return ScheduledTaskResponse(
                success=True,
                message="Scheduled task created successfully",
                data=scheduled_task
            )
        except Exception as e:
            logger.error(f"Failed to create scheduled task: {str(e)}")
            return ScheduledTaskResponse(
                success=False,
                message=f"Failed to create scheduled task: {str(e)}",
                data=None
            )

    async def get_scheduled_task(self, scheduled_task_id: str) -> ScheduledTaskResponse:
        """Get a scheduled task by ID."""
        try:
            scheduled_task = self.repository.get_scheduled_task(scheduled_task_id)
            if not scheduled_task:
                return ScheduledTaskResponse(
                    success=False,
                    message="Scheduled task not found",
                    data=None
                )
            
            return ScheduledTaskResponse(
                success=True,
                message="Scheduled task retrieved successfully",
                data=scheduled_task
            )
        except Exception as e:
            logger.error(f"Failed to get scheduled task: {str(e)}")
            return ScheduledTaskResponse(
                success=False,
                message=f"Failed to get scheduled task: {str(e)}",
                data=None
            )

    async def get_all_scheduled_tasks(self, skip: int = 0, limit: int = 100) -> ScheduledTaskListResponse:
        """Get all scheduled tasks with pagination."""
        try:
            scheduled_tasks = self.repository.get_all_scheduled_tasks(skip=skip, limit=limit)
            total = self.repository.get_total_count()
            
            return ScheduledTaskListResponse(
                success=True,
                message="Scheduled tasks retrieved successfully",
                data=scheduled_tasks,
                total=total
            )
        except Exception as e:
            logger.error(f"Failed to get scheduled tasks: {str(e)}")
            return ScheduledTaskListResponse(
                success=False,
                message=f"Failed to get scheduled tasks: {str(e)}",
                data=[],
                total=0
            )

    async def update_scheduled_task(self, scheduled_task_id: str, update_data: ScheduledTaskUpdate) -> ScheduledTaskResponse:
        """Update a scheduled task."""
        try:
            if croniter is None:
                return ScheduledTaskResponse(
                    success=False,
                    message="croniter library not available. Please install: pip install croniter",
                    data=None
                )
            
            # Validate cron expression if provided
            if update_data.cron_expression:
                try:
                    cron = croniter(update_data.cron_expression, datetime.now())
                    next_run_timestamp = cron.get_next()
                    next_run = datetime.fromtimestamp(next_run_timestamp)
                    logger.info(f"Cron expression '{update_data.cron_expression}' is valid. Next run: {next_run}")
                except Exception as e:
                    return ScheduledTaskResponse(
                        success=False,
                        message=f"Invalid cron expression: {str(e)}",
                        data=None
                    )
            
            scheduled_task = self.repository.update_scheduled_task(scheduled_task_id, update_data)
            if not scheduled_task:
                return ScheduledTaskResponse(
                    success=False,
                    message="Scheduled task not found",
                    data=None
                )
            
            # Update next run time if cron expression or active status changed
            if update_data.cron_expression or update_data.is_active is not None:
                if scheduled_task.is_active:
                    cron = croniter(scheduled_task.cron_expression, datetime.now())
                    next_run_timestamp = cron.get_next()
                    next_run = datetime.fromtimestamp(next_run_timestamp)
                    self.repository.update_scheduled_task_run_times(
                        scheduled_task.id, 
                        last_run=scheduled_task.last_run or datetime.now(),
                        next_run=next_run
                    )
                else:
                    # Clear next run if inactive
                    self.repository.update_scheduled_task_run_times(
                        scheduled_task.id, 
                        last_run=scheduled_task.last_run or datetime.now(),
                        next_run=None
                    )
                # Refresh the scheduled task to get updated times
                scheduled_task = self.repository.get_scheduled_task(scheduled_task.id)
            
            return ScheduledTaskResponse(
                success=True,
                message="Scheduled task updated successfully",
                data=scheduled_task
            )
        except Exception as e:
            logger.error(f"Failed to update scheduled task: {str(e)}")
            return ScheduledTaskResponse(
                success=False,
                message=f"Failed to update scheduled task: {str(e)}",
                data=None
            )

    async def delete_scheduled_task(self, scheduled_task_id: str) -> ScheduledTaskResponse:
        """Delete a scheduled task."""
        try:
            # Get the scheduled task first to return it in response
            scheduled_task = self.repository.get_scheduled_task(scheduled_task_id)
            if not scheduled_task:
                return ScheduledTaskResponse(
                    success=False,
                    message="Scheduled task not found",
                    data=None
                )
            
            success = self.repository.delete_scheduled_task(scheduled_task_id)
            if success:
                return ScheduledTaskResponse(
                    success=True,
                    message="Scheduled task deleted successfully",
                    data=scheduled_task
                )
            else:
                return ScheduledTaskResponse(
                    success=False,
                    message="Failed to delete scheduled task",
                    data=None
                )
        except Exception as e:
            logger.error(f"Failed to delete scheduled task: {str(e)}")
            return ScheduledTaskResponse(
                success=False,
                message=f"Failed to delete scheduled task: {str(e)}",
                data=None
            )

    def get_active_scheduled_tasks(self) -> List[ScheduledTask]:
        """Get all active scheduled tasks for the scheduler."""
        try:
            return self.repository.get_active_scheduled_tasks()
        except Exception as e:
            logger.error(f"Failed to get active scheduled tasks: {str(e)}")
            return []


# Create singleton instance
scheduled_task_service = ScheduledTaskService()
