import logging
import asyncio
from datetime import datetime
from typing import Dict, Any

from src.api.background.tasks_runner import execute_playwright_script
try:
    from apscheduler.schedulers.asyncio import AsyncIOScheduler
    from apscheduler.triggers.cron import CronTrigger
    from apscheduler.jobstores.memory import MemoryJobStore
except ImportError:
    AsyncIOScheduler = None
    CronTrigger = None
    MemoryJobStore = None

from .scheduled_task_service import scheduled_task_service
from .task_service import task_service
from ..models.schemas import TaskCreate, ScheduledTask
from ..repository.task_repository import task_repository

logger = logging.getLogger(__name__)


class TaskSchedulerService:
    """Service to manage and execute scheduled tasks using APScheduler."""
    
    def __init__(self):
        self.scheduler = None
        self.is_running = False
        self._job_id_map: Dict[str, str] = {}  # Maps scheduled_task_id to scheduler job_id
        
    async def start(self):
        """Start the scheduler and load existing scheduled tasks."""
        if AsyncIOScheduler is None:
            logger.error("APScheduler not available. Please install: pip install apscheduler")
            return
        
        if self.scheduler is None:
            # Configure jobstore
            jobstores = {
                'default': MemoryJobStore() if MemoryJobStore else None
            }
            
            if jobstores['default'] is None:
                logger.error("MemoryJobStore not available")
                return
            
            self.scheduler = AsyncIOScheduler(
                jobstores=jobstores,
                timezone='UTC'
            )
        
        if not self.is_running:
            self.scheduler.start()
            self.is_running = True
            logger.info("Task scheduler started")
            
            # Load existing scheduled tasks
            await self.load_scheduled_tasks()
    
    async def stop(self):
        """Stop the scheduler."""
        if self.scheduler and self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            self._job_id_map.clear()
            logger.info("Task scheduler stopped")
    
    async def load_scheduled_tasks(self):
        """Load all active scheduled tasks from Redis and add them to scheduler."""
        try:
            active_scheduled_tasks = scheduled_task_service.get_active_scheduled_tasks()
            
            for scheduled_task in active_scheduled_tasks:
                await self.add_scheduled_task(scheduled_task)
            
            logger.info(f"Loaded {len(active_scheduled_tasks)} scheduled tasks")
        except Exception as e:
            logger.error(f"Failed to load scheduled tasks: {str(e)}")
    
    async def add_scheduled_task(self, scheduled_task: ScheduledTask):
        """Add a scheduled task to the scheduler."""
        if not self.scheduler or not self.is_running:
            logger.warning("Scheduler not running, cannot add scheduled task")
            return
        
        if CronTrigger is None:
            logger.error("CronTrigger not available")
            return
        
        try:
            # Parse cron expression
            cron_parts = scheduled_task.cron_expression.split()
            if len(cron_parts) != 5:
                logger.error(f"Invalid cron expression for scheduled task {scheduled_task.id}: {scheduled_task.cron_expression}")
                return
            
            minute, hour, day, month, day_of_week = cron_parts
            
            # Create cron trigger
            trigger = CronTrigger(
                minute=minute,
                hour=hour,
                day=day,
                month=month,
                day_of_week=day_of_week,
                timezone='UTC'
            )
            
            # Create unique job ID
            job_id = f"scheduled_task_{scheduled_task.id}"
            
            # Add job to scheduler
            self.scheduler.add_job(
                func=self.execute_scheduled_task,
                trigger=trigger,
                args=[scheduled_task.id],
                id=job_id,
                name=f"Scheduled Task: {scheduled_task.task_id}",
                replace_existing=True
            )
            
            # Store mapping
            self._job_id_map[scheduled_task.id] = job_id
            
            logger.info(f"Added scheduled task {scheduled_task.id} with cron '{scheduled_task.cron_expression}'")
            
        except Exception as e:
            logger.error(f"Failed to add scheduled task {scheduled_task.id}: {str(e)}")
    
    async def remove_scheduled_task(self, scheduled_task_id: str):
        """Remove a scheduled task from the scheduler."""
        if not self.scheduler or not self.is_running:
            return
        
        try:
            job_id = self._job_id_map.get(scheduled_task_id)
            if job_id:
                self.scheduler.remove_job(job_id)
                del self._job_id_map[scheduled_task_id]
                logger.info(f"Removed scheduled task {scheduled_task_id}")
        except Exception as e:
            logger.error(f"Failed to remove scheduled task {scheduled_task_id}: {str(e)}")
    
    async def update_scheduled_task(self, scheduled_task: ScheduledTask):
        """Update a scheduled task in the scheduler."""
        # Remove old job and add new one
        await self.remove_scheduled_task(scheduled_task.id)
        
        if scheduled_task.is_active:
            await self.add_scheduled_task(scheduled_task)
    
    async def execute_scheduled_task(self, scheduled_task_id: str):
        """Execute a scheduled task."""
        try:
            logger.info(f"Executing scheduled task {scheduled_task_id}")
            
            # Get the scheduled task
            response = await scheduled_task_service.get_scheduled_task(scheduled_task_id)
            if not response.success or not response.data:
                logger.error(f"Scheduled task {scheduled_task_id} not found")
                return
            
            scheduled_task = response.data
            
            # Start running the task
            task = task_repository.get_detailed_task(scheduled_task.task_id)
            if not task:
                logger.error(f"Original task {scheduled_task.task_id} not found for scheduled task {scheduled_task_id}")
                return
            await execute_playwright_script(
                task_id=task.id,
                script=task.playwright_script or ""
            )
            
           
        except Exception as e:
            logger.error(f"Failed to execute scheduled task {scheduled_task_id}: {str(e)}")


# Create singleton instance
task_scheduler_service = TaskSchedulerService()
