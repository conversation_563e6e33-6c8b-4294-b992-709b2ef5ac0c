import logging
from typing import List, Optional
from ..models.schemas import *
from ..repository.task_repository import task_repository
from ..repository.chat_repository import chat_repository
from ..services.message_service import message_service
from fastapi import BackgroundTasks
from ..background.tasks_runner import execute_task, execute_playwright_script

logger = logging.getLogger(__name__)

class TaskService:
    """Service layer for task operations."""

    def __init__(self):
        self.repository = task_repository
    
    async def create_task(self, task_data: TaskCreate, background_tasks: BackgroundTasks) -> TaskResponse:
        """Create a new task."""
        try:
            # Validate chat if provided
            if task_data.chat_id:
                chat = chat_repository.get_chat(task_data.chat_id)
                if not chat:
                    return TaskResponse(
                        success=False,
                        message=f"Chat with ID {task_data.chat_id} not found",
                        data=None
                    )

            # Create task directly
            task = self.repository.create_task(task_data)

            # Create message
            await message_service.create_message(MessageCreate(
                chat_id=task_data.chat_id or "",
                content=task.title + "\n" + (task.description or ""),
                role=MessageRole.USER,
                metadata={"task_id": task.id}
            ))

            # Execute task in background
            background_tasks.add_task(execute_task, chat_id=task.chat_id or "", task_id=task.id, task_request=task_data)

            return TaskResponse(
                success=True,
                message="Task created successfully and will be executed in the background",
                data=task
            )
        except Exception as e:
            logger.error(f"Failed to create task: {str(e)}")
            return TaskResponse(
                success=False,
                message=f"Failed to create task: {str(e)}",
                data=None
            )


    async def run_playwright_script(self, task_id: str, background_tasks: BackgroundTasks) -> TaskDetailedResponse:
        """Run the playwright script of a task."""
        try:
            task = self.repository.get_detailed_task(task_id)
            if not task:
                return TaskDetailedResponse(
                    success=False,
                    message=f"Task with ID {task_id} not found",
                    data=None
                )
            
            if not task.playwright_script:
                return TaskDetailedResponse(
                    success=False,
                    message=f"Task with ID {task_id} has no playwright script",
                    data=None
                )

            background_tasks.add_task(execute_playwright_script, task_id=task.id, script=task.playwright_script)
            
            return TaskDetailedResponse(
                success=True,
                message="Playwright script execution started in the background",
                data=task
            )
        except Exception as e:
            logger.error(f"Failed to run playwright script for task {task_id}: {str(e)}")
            return TaskDetailedResponse(
                success=False,
                message=f"Failed to run playwright script: {str(e)}",
                data=None
            )
    
    async def get_task(self, task_id: str) -> TaskResponse:
        """Get a task by ID."""
        task = self.repository.get_task(task_id)
        if task:
            return TaskResponse(
                success=True,
                message="Task retrieved successfully",
                data=task
            )
        else:
            return TaskResponse(
                success=False,
                message=f"Task with ID {task_id} not found",
                data=None
            )
    
    async def get_all_tasks(self, skip: int = 0, limit: int = 100) -> TaskListResponse:
        """Get all tasks with pagination."""
        try:
            tasks = self.repository.get_all_tasks(skip=skip, limit=limit)
            total = self.repository.get_task_count()
            
            return TaskListResponse(
                success=True,
                message="Tasks retrieved successfully",
                data=tasks,
                total=total
            )
        except Exception as e:
            logger.error(f"Failed to get all tasks: {str(e)}")
            return TaskListResponse(
                success=False,
                message=f"Failed to retrieve tasks: {str(e)}",
                data=[],
                total=0
            )
    
    async def update_task(self, task_id: str, task_update: TaskUpdate) -> TaskResponse:
        """Update a task."""
        try:
            task = self.repository.update_task(task_id, task_update)
            if task:
                return TaskResponse(
                    success=True,
                    message="Task updated successfully",
                    data=task
                )
            else:
                return TaskResponse(
                    success=False,
                    message=f"Task with ID {task_id} not found",
                    data=None
                )
        except Exception as e:
            logger.error(f"Failed to update task: {str(e)}")
            return TaskResponse(
                success=False,
                message=f"Failed to update task: {str(e)}",
                data=None
            )
    
    async def delete_task(self, task_id: str) -> TaskResponse:
        """Delete a task."""
        try:
            success = self.repository.delete_task(task_id)
            if success:
                return TaskResponse(
                    success=True,
                    message="Task deleted successfully",
                    data=None
                )
            else:
                return TaskResponse(
                    success=False,
                    message=f"Task with ID {task_id} not found",
                    data=None
                )
        except Exception as e:
            logger.error(f"Failed to delete task: {str(e)}")
            return TaskResponse(
                success=False,
                message=f"Failed to delete task: {str(e)}",
                data=None
            )
    
    async def get_tasks_by_status(self, status: TaskStatusEnum) -> TaskListResponse:
        """Get tasks filtered by status."""
        try:
            tasks = self.repository.get_tasks_by_status(status)
            return TaskListResponse(
                success=True,
                message=f"Tasks with status '{status}' retrieved successfully",
                data=tasks,
                total=len(tasks)
            )
        except Exception as e:
            logger.error(f"Failed to get tasks by status: {str(e)}")
            return TaskListResponse(
                success=False,
                message=f"Failed to retrieve tasks by status: {str(e)}",
                data=[],
                total=0
            )

    async def update_task_status(self, task_id: str, status: TaskStatusEnum) -> TaskResponse:
        """Update the status of a task."""
        try:
            task = self.repository.update_task(task_id, TaskUpdate(status=status))
            return TaskResponse(
                success=True,
                message=f"Task status updated to {status}",
                data=task
            )
        except Exception as e:
            logger.error(f"Failed to update task status: {str(e)}")
            return TaskResponse(
                success=False,
                message=f"Failed to update task status: {str(e)}",
                data=None
            )

# Global service instance
task_service = TaskService()
