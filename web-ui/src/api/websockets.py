"""
WebSocket Server for real-time task updates.

This module provides WebSocket endpoints for real-time communication
with browser automation tasks, allowing clients to receive live updates
on task progress, chat messages, and browser state.
"""

import asyncio
import json
import logging
from typing import Dict, Set
from datetime import datetime

try:
    from fastapi import WebSocket, WebSocketDisconnect
    from fastapi.routing import APIRouter
except ImportError:
    # Graceful fallback if FastAPI is not available
    WebSocket = None
    WebSocketDisconnect = None
    APIRouter = None

logger = logging.getLogger(__name__)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.task_connections: Dict[str, Set[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, task_id: str = None):
        """Accept a WebSocket connection."""
        await websocket.accept()
        
        if task_id:
            if task_id not in self.task_connections:
                self.task_connections[task_id] = set()
            self.task_connections[task_id].add(websocket)
        else:
            # General connection
            session_id = "default"
            if session_id not in self.active_connections:
                self.active_connections[session_id] = set()
            self.active_connections[session_id].add(websocket)

    def disconnect(self, websocket: WebSocket, task_id: str = None):
        """Remove a WebSocket connection."""
        if task_id and task_id in self.task_connections:
            self.task_connections[task_id].discard(websocket)
            if not self.task_connections[task_id]:
                del self.task_connections[task_id]
        else:
            for session_connections in self.active_connections.values():
                session_connections.discard(websocket)

    async def send_to_task(self, task_id: str, message: dict):
        """Send message to all connections for a specific task."""
        if task_id in self.task_connections:
            disconnected = set()
            for connection in self.task_connections[task_id]:
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending message to task {task_id}: {e}")
                    disconnected.add(connection)
            
            # Remove disconnected connections
            for conn in disconnected:
                self.task_connections[task_id].discard(conn)

    async def broadcast(self, message: dict):
        """Broadcast message to all connections."""
        for session_connections in self.active_connections.values():
            disconnected = set()
            for connection in session_connections:
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error broadcasting message: {e}")
                    disconnected.add(connection)
            
            # Remove disconnected connections
            for conn in disconnected:
                session_connections.discard(conn)


# Global connection manager
manager = ConnectionManager()

# Create WebSocket router
def create_websocket_router():
    """Create WebSocket router if FastAPI is available."""
    if not APIRouter:
        return None
        
    router = APIRouter()

    @router.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        """General WebSocket endpoint for system updates."""
        await manager.connect(websocket)
        try:
            while True:
                data = await websocket.receive_text()
                # Handle incoming messages if needed
                logger.debug(f"Received WebSocket message: {data}")
        except WebSocketDisconnect:
            manager.disconnect(websocket)

    @router.websocket("/ws/tasks/{task_id}")
    async def task_websocket_endpoint(websocket: WebSocket, task_id: str):
        """Task-specific WebSocket endpoint for task updates."""
        await manager.connect(websocket, task_id)
        try:
            # Send initial connection confirmation
            await websocket.send_text(json.dumps({
                "type": "connection",
                "task_id": task_id,
                "message": "Connected to task updates",
                "timestamp": datetime.now().isoformat()
            }))
            
            while True:
                data = await websocket.receive_text()
                # Handle task-specific messages if needed
                logger.debug(f"Received task WebSocket message for {task_id}: {data}")
        except WebSocketDisconnect:
            manager.disconnect(websocket, task_id)

    return router


# Helper functions for sending updates
async def send_task_update(task_id: str, update_type: str, data: dict):
    """Send a task update via WebSocket."""
    message = {
        "type": update_type,
        "task_id": task_id,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }
    await manager.send_to_task(task_id, message)


async def send_chat_message(task_id: str, role: str, content: str):
    """Send a chat message update via WebSocket."""
    await send_task_update(task_id, "chat_message", {
        "role": role,
        "content": content
    })


async def send_status_update(task_id: str, status: str, details: dict = None):
    """Send a status update via WebSocket."""
    await send_task_update(task_id, "status_update", {
        "status": status,
        "details": details or {}
    })


async def send_browser_screenshot(task_id: str, screenshot_b64: str):
    """Send a browser screenshot update via WebSocket."""
    await send_task_update(task_id, "browser_screenshot", {
        "screenshot": screenshot_b64
    })


# Integration with existing webui components
class WebSocketNotifier:
    """Helper class to integrate WebSocket notifications with existing webui components."""
    
    def __init__(self, task_id: str):
        self.task_id = task_id
    
    async def notify_chat_update(self, chat_history: list):
        """Notify about chat history updates."""
        await send_task_update(self.task_id, "chat_update", {
            "chat_history": chat_history
        })
    
    async def notify_status_change(self, status: str, details: dict = None):
        """Notify about status changes."""
        await send_status_update(self.task_id, status, details)
    
    async def notify_browser_update(self, screenshot_b64: str):
        """Notify about browser updates."""
        await send_browser_screenshot(self.task_id, screenshot_b64)
    
    async def notify_task_complete(self, result: dict):
        """Notify about task completion."""
        await send_task_update(self.task_id, "task_complete", result)
    
    async def notify_error(self, error: str, details: dict = None):
        """Notify about errors."""
        await send_task_update(self.task_id, "error", {
            "error": error,
            "details": details or {}
        })


# Export the router and helper functions
websocket_router = create_websocket_router()

__all__ = [
    "websocket_router",
    "WebSocketNotifier", 
    "send_task_update",
    "send_chat_message",
    "send_status_update",
    "send_browser_screenshot"
]
