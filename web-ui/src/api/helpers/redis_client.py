import os
import redis

# You can set these environment variables in your deployment environment
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.getenv('REDIS_PORT', 6379))
REDIS_DB = int(os.getenv('REDIS_DB', 0))
REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', None)

redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True  # returns strings instead of bytes
        )

def get_redis_client():
    """
    Returns a Redis client instance. Raises redis.ConnectionError if connection fails.
    """
    try:
        return redis_client
    except redis.ConnectionError as e:
        raise RuntimeError(f"Failed to connect to Redis: {e}")

def get_redis_client_status():
    """
    Returns the status of the Redis client.
    """
    try:
        return redis_client.ping()
    except redis.ConnectionError as e:
        return False
    
def get_redis_url():
    if REDIS_PASSWORD:
        return f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
    else:
        return f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"