import asyncio
import json
from collections import defaultdict

# Each chat_id maps to a list of queues (one per connected client)
chat_queues = defaultdict(list)

def register_chat_queue(chat_id: str):
    queue = asyncio.Queue()
    chat_queues[chat_id].append(queue)
    return queue

def unregister_chat_queue(chat_id: str, queue):
    if chat_id in chat_queues:
        try:
            chat_queues[chat_id].remove(queue)
            if not chat_queues[chat_id]:
                del chat_queues[chat_id]
        except ValueError:
            # Queue was already removed, ignore
            pass

async def publish_chat_message(chat_id: str, message: dict | str):
    # Ensure message is serializable
    if isinstance(message, dict):
        try:
            # Test JSON serialization
            json.dumps(message)
        except (TypeError, ValueError):
            # If not serializable, convert to string representation
            message = str(message)
    
    queues_to_remove = []
    for queue in chat_queues.get(chat_id, []):
        try:
            await queue.put(message)
        except Exception:
            # Queue is probably closed, mark for removal
            queues_to_remove.append(queue)
    
    # Clean up closed queues
    for queue in queues_to_remove:
        unregister_chat_queue(chat_id, queue) 

# Disconnect all SSE clients for a particular chat_id
def disconnect_sse_chat(chat_id: str):
    """
    Disconnects all SSE clients for the given chat_id by removing all queues and cleaning up.
    """
    if chat_id in chat_queues:
        queues = chat_queues.pop(chat_id)
        # Optionally, notify clients by putting a sentinel value or closing queues
        for queue in queues:
            try:
                queue.put_nowait(None)  # Optionally signal disconnect
            except Exception:
                pass