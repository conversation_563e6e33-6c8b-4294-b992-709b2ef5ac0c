import asyncio
import json
import logging
import os
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.responses import FileResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from src.api.models import (
    TaskRequest, TaskResponse, TaskStatus, TaskHistoryResponse,
    UserAssistanceRequest, ConfigurationRequest, ConfigurationResponse,
    AvailableModelsResponse, ErrorResponse, QASummaryResponse, AgentSettings, BrowserSettings,
    RefinementStartRequest, RefinementStartResponse, RefinementAnswersRequest,
    RefinementAnswersResponse, RefinementStatusResponse
)
from src.webui.webui_manager import WebuiManager
from src.api.agent_wrapper import (
    api_run_agent_task, api_handle_stop, api_handle_pause_resume, api_handle_clear,
    get_task_status_info, provide_user_assistance
)
from src.utils import config
from src.utils.qa_system_prompt import get_qa_system_prompt
from src.prompt_refinement.refinement_manager import RefinementManager

logger = logging.getLogger(__name__)

app = FastAPI(
    title="DrCode UI Testing API",
    description="API for browser automation and UI testing using DrCode",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global state management
webui_managers: Dict[str, WebuiManager] = {}
active_tasks: Dict[str, Dict[str, Any]] = {}
refinement_manager = RefinementManager()


def get_or_create_manager(session_id: str = "default") -> WebuiManager:
    """Get or create a WebuiManager for a session."""
    if session_id not in webui_managers:
        webui_managers[session_id] = WebuiManager()
        webui_managers[session_id].init_browser_use_agent()
    return webui_managers[session_id]


def components_to_dict(webui_manager: WebuiManager, agent_settings: AgentSettings, browser_settings: BrowserSettings) -> Dict[Any, Any]:
    """Convert API models to component dictionary format expected by webui functions."""
    # This simulates the component dictionary that would normally come from Gradio UI
    components_dict = {}
    
    # Initialize agent settings tab components if not done
    if not hasattr(webui_manager, 'id_to_component') or not webui_manager.id_to_component:
        # Initialize empty component registry
        webui_manager.id_to_component = {}
        webui_manager.component_to_id = {}
    
    # Add agent settings values directly to a mock component dict
    for key, value in agent_settings.dict(exclude_none=True).items():
        # Create a mock component ID
        comp_id = f"agent_settings.{key}"
        # Store the value directly - we'll handle it in the webui functions
        components_dict[comp_id] = value
    
    # Add browser settings
    for key, value in browser_settings.dict(exclude_none=True).items():
        comp_id = f"browser_settings.{key}"
        components_dict[comp_id] = value
    
    # Add task text as user input
    components_dict["browser_use_agent.user_input"] = ""  # Will be set when creating task
            
    return components_dict


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "DrCode UI Testing API",
        "version": "1.0.0",
        "endpoints": {
            "tasks": "/tasks",
            "models": "/models",
            "config": "/config",
            "docs": "/docs"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.now()}


# Task Management Endpoints
@app.post("/tasks", response_model=TaskResponse)
async def create_task(
    task_request: TaskRequest,
    background_tasks: BackgroundTasks,
    session_id: str = "default"
):
    """Create and start a new browser automation task."""
    try:
        task_id = str(uuid.uuid4())
        webui_manager = get_or_create_manager(session_id)

        # Handle prompt refinement
        final_task = task_request.task

        if not task_request.skip_refinement:
            # Check if we have a refinement session ID
            if task_request.refinement_session_id:
                # Use existing refinement session
                refined_prompt = refinement_manager.complete_session(task_request.refinement_session_id)
                if refined_prompt:
                    final_task = refined_prompt
                else:
                    raise HTTPException(status_code=400, detail="Invalid or incomplete refinement session")
            else:
                # Start new refinement process
                refinement_result = refinement_manager.start_refinement_session(task_request.task)

                if refinement_result.get("needs_refinement", False):
                    # Return refinement questions instead of starting task
                    return TaskResponse(
                        task_id=None,
                        status="needs_refinement",
                        message="Task needs refinement. Please answer the clarifying questions.",
                        created_at=datetime.now(),
                        refinement_session_id=refinement_result.get("session_id"),
                        clarifying_questions=refinement_result.get("clarifying_questions", [])
                    )
                else:
                    # Use the refined prompt
                    final_task = refinement_result.get("refined_prompt", task_request.task)

        # Set default settings if not provided
        agent_settings = task_request.agent_settings or AgentSettings()
        browser_settings = task_request.browser_settings or BrowserSettings()
        # Convert to components format
        components_dict = components_to_dict(webui_manager, agent_settings, browser_settings)

        # Add the final task to the components dict
        components_dict["browser_use_agent.user_input"] = final_task
        
        # Store task info
        active_tasks[task_id] = {
            "session_id": session_id,
            "status": "created",
            "created_at": datetime.now(),
            "task": task_request.task,
            "webui_manager": webui_manager
        }
        
        # Start the task in background
        background_tasks.add_task(execute_task, task_id, webui_manager, components_dict)
        
        return TaskResponse(
            task_id=task_id,
            status="created",
            message="Task created and will start shortly",
            created_at=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Error creating task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def execute_task(task_id: str, webui_manager: WebuiManager, components_dict: Dict[Any, Any]):
    """Execute the task in background."""
    try:
        active_tasks[task_id]["status"] = "running"
        
        # Run the task using the API wrapper
        async for update in api_run_agent_task(webui_manager, components_dict):
            # Update can be processed here if needed
            pass
            
        active_tasks[task_id]["status"] = "completed"
        
    except Exception as e:
        logger.error(f"Error executing task {task_id}: {e}")
        active_tasks[task_id]["status"] = "error"
        active_tasks[task_id]["error"] = str(e)


@app.get("/tasks/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str):
    """Get the status of a specific task."""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = active_tasks[task_id]
    webui_manager = task_info["webui_manager"]
    
    return TaskStatus(
        task_id=task_id,
        status=task_info["status"],
        chat_history=get_task_status_info(webui_manager).get("chat_history", []),
        error_message=task_info.get("error"),
        duration=None,  # Could calculate from timestamps
        tokens_used=None  # Could extract from history
    )


@app.post("/tasks/{task_id}/stop")
async def stop_task(task_id: str):
    """Stop a running task."""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = active_tasks[task_id]
    webui_manager = task_info["webui_manager"]
    
    try:
        result = await api_handle_stop(webui_manager)
        active_tasks[task_id]["status"] = "stopped"
        return {"message": "Task stopped successfully", "result": result}
    except Exception as e:
        logger.error(f"Error stopping task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/tasks/{task_id}/pause")
async def pause_task(task_id: str):
    """Pause a running task."""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = active_tasks[task_id]
    webui_manager = task_info["webui_manager"]
    
    try:
        result = await api_handle_pause_resume(webui_manager)
        return {"message": "Task paused/resumed", "result": result}
    except Exception as e:
        logger.error(f"Error pausing task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/tasks/{task_id}/resume")
async def resume_task(task_id: str):
    """Resume a paused task."""
    return await pause_task(task_id)  # Same function handles both


@app.delete("/tasks/{task_id}")
async def clear_task(task_id: str):
    """Clear/delete a task and its resources."""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = active_tasks[task_id]
    webui_manager = task_info["webui_manager"]
    
    try:
        await handle_clear(webui_manager)
        del active_tasks[task_id]
        return {"message": "Task cleared successfully"}
    except Exception as e:
        logger.error(f"Error clearing task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/tasks")
async def list_tasks():
    """List all tasks."""
    return {
        "tasks": [
            {
                "task_id": task_id,
                "status": info["status"],
                "created_at": info["created_at"],
                "task": info["task"]
            }
            for task_id, info in active_tasks.items()
        ]
    }


# Prompt Refinement Endpoints

@app.post("/refinement/start", response_model=RefinementStartResponse)
async def start_refinement(request: RefinementStartRequest):
    """Start a new prompt refinement session."""
    try:
        result = refinement_manager.start_refinement_session(request.prompt)

        return RefinementStartResponse(
            session_id=result.get("session_id"),
            needs_refinement=result.get("needs_refinement", True),
            clarifying_questions=result.get("clarifying_questions"),
            refined_prompt=result.get("refined_prompt"),
            state=result.get("state", "unknown"),
            analysis=result.get("analysis"),
            message=result.get("message", ""),
            error=result.get("error")
        )

    except Exception as e:
        logger.error(f"Error starting refinement: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/refinement/{session_id}/answers", response_model=RefinementAnswersResponse)
async def provide_refinement_answers(session_id: str, request: RefinementAnswersRequest):
    """Provide answers to clarifying questions."""
    try:
        if request.session_id != session_id:
            raise HTTPException(status_code=400, detail="Session ID mismatch")

        result = refinement_manager.provide_answers(session_id, request.answers)

        if result.get("error"):
            raise HTTPException(status_code=400, detail=result["error"])

        return RefinementAnswersResponse(
            session_id=session_id,
            needs_further_refinement=result.get("needs_further_refinement", False),
            refined_prompt=result.get("refined_prompt", ""),
            clarifying_questions=result.get("clarifying_questions"),
            state=result.get("state", "unknown"),
            iteration=result.get("iteration", 0),
            message=result.get("message", ""),
            error=result.get("error")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error providing answers: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/refinement/{session_id}/status", response_model=RefinementStatusResponse)
async def get_refinement_status(session_id: str):
    """Get the status of a refinement session."""
    try:
        result = refinement_manager.get_session_status(session_id)

        if result.get("error"):
            raise HTTPException(status_code=404, detail=result["error"])

        return RefinementStatusResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting refinement status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/refinement/sessions")
async def list_refinement_sessions():
    """List all active refinement sessions."""
    try:
        sessions = refinement_manager.list_active_sessions()
        return {"sessions": sessions}

    except Exception as e:
        logger.error(f"Error listing refinement sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Task History and Files

@app.get("/tasks/{task_id}/history", response_model=TaskHistoryResponse)
async def get_task_history(task_id: str):
    """Get task history and files."""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = active_tasks[task_id]
    webui_manager = task_info["webui_manager"]
    
    # Get history file paths
    history_file_path = None
    gif_file_path = None
    history_data = None
    
    if hasattr(webui_manager, 'bu_agent_task_id') and webui_manager.bu_agent_task_id:
        save_agent_history_path = "./tmp/agent_history"
        history_file_path = os.path.join(
            save_agent_history_path,
            webui_manager.bu_agent_task_id,
            f"{webui_manager.bu_agent_task_id}.json"
        )
        gif_file_path = os.path.join(
            save_agent_history_path,
            webui_manager.bu_agent_task_id,
            f"{webui_manager.bu_agent_task_id}.gif"
        )
        
        # Load history data if file exists
        if history_file_path and os.path.exists(history_file_path):
            try:
                with open(history_file_path, 'r') as f:
                    history_data = json.load(f)
            except Exception as e:
                logger.error(f"Error loading history data: {e}")
    
    return TaskHistoryResponse(
        task_id=task_id,
        history_file_path=history_file_path,
        gif_file_path=gif_file_path,
        history_data=history_data
    )


@app.get("/tasks/{task_id}/history/download")
async def download_task_history(task_id: str):
    """Download task history JSON file."""
    history_response = await get_task_history(task_id)
    
    if not history_response.history_file_path or not os.path.exists(history_response.history_file_path):
        raise HTTPException(status_code=404, detail="History file not found")
    
    return FileResponse(
        history_response.history_file_path,
        filename=f"task_{task_id}_history.json",
        media_type="application/json"
    )


@app.get("/tasks/{task_id}/gif/download")
async def download_task_gif(task_id: str):
    """Download task recording GIF."""
    history_response = await get_task_history(task_id)
    
    if not history_response.gif_file_path or not os.path.exists(history_response.gif_file_path):
        raise HTTPException(status_code=404, detail="GIF file not found")
    
    return FileResponse(
        history_response.gif_file_path,
        filename=f"task_{task_id}_recording.gif",
        media_type="image/gif"
    )


# User Assistance

@app.post("/tasks/{task_id}/assist")
async def provide_user_assistance(task_id: str, assistance: UserAssistanceRequest):
    """Provide user assistance for a task that's waiting for help."""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = active_tasks[task_id]
    webui_manager = task_info["webui_manager"]
    
    # Check if task is waiting for assistance
    if not hasattr(webui_manager, 'bu_response_event') or not webui_manager.bu_response_event:
        raise HTTPException(status_code=400, detail="Task is not waiting for assistance")
    
    try:
        # Provide the response
        webui_manager.bu_user_help_response = assistance.response
        webui_manager.bu_response_event.set()
        
        return {"message": "Assistance provided successfully"}
    except Exception as e:
        logger.error(f"Error providing assistance for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Configuration Management

@app.post("/config/save", response_model=ConfigurationResponse)
async def save_configuration(config_request: ConfigurationRequest, session_id: str = "default"):
    """Save current configuration."""
    try:
        webui_manager = get_or_create_manager(session_id)
        
        # Create components dict from settings
        components_dict = {}
        if config_request.agent_settings:
            components_dict.update(components_to_dict(config_request.agent_settings, BrowserSettings()))
        if config_request.browser_settings:
            components_dict.update(components_to_dict(AgentSettings(), config_request.browser_settings))
        
        # Save configuration
        config_path = webui_manager.save_config(components_dict)
        config_name = config_request.config_name or os.path.basename(config_path)
        
        return ConfigurationResponse(
            config_name=config_name,
            config_path=config_path,
            saved_at=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Error saving configuration: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/config/load")
async def load_configuration(config_path: str, session_id: str = "default"):
    """Load configuration from file."""
    try:
        webui_manager = get_or_create_manager(session_id)
        
        if not os.path.exists(config_path):
            raise HTTPException(status_code=404, detail="Configuration file not found")
        
        # Load configuration (this returns a generator)
        async for update in webui_manager.load_config(config_path):
            pass  # Process updates if needed
        
        return {"message": "Configuration loaded successfully"}
        
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Model and Provider Information

@app.get("/models", response_model=List[AvailableModelsResponse])
async def get_available_models():
    """Get available models for all providers."""
    return [
        AvailableModelsResponse(provider=provider, models=models)
        for provider, models in config.model_names.items()
    ]


@app.get("/models/{provider}", response_model=AvailableModelsResponse)
async def get_provider_models(provider: str):
    """Get available models for a specific provider."""
    if provider not in config.model_names:
        raise HTTPException(status_code=404, detail="Provider not found")
    
    return AvailableModelsResponse(
        provider=provider,
        models=config.model_names[provider]
    )


@app.get("/providers")
async def get_providers():
    """Get list of available LLM providers."""
    return {
        "providers": list(config.PROVIDER_DISPLAY_NAMES.keys()),
        "display_names": config.PROVIDER_DISPLAY_NAMES
    }


# System Information

@app.get("/system/prompt/qa")
async def get_qa_system_prompt():
    """Get the current QA system prompt."""
    return {"qa_system_prompt": get_qa_system_prompt()}


@app.get("/system/info")
async def get_system_info():
    """Get system information."""
    return {
        "active_tasks_count": len(active_tasks),
        "active_sessions": list(webui_managers.keys()),
        "default_qa_mode": os.getenv("DEFAULT_QA_MODE", "true").lower() == "true",
        "environment": {
            "openai_api_key_set": bool(os.getenv("OPENAI_API_KEY")),
            "drcode_api_key_set": bool(os.getenv("DRCODE_API_KEY")),
            "drcode_api_url": os.getenv("DRCODE_API_URL", "https://devapi.drcode.ai/testgpt/api/uiTesting")
        }
    }


# Error Handlers

@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {"error": "Not Found", "message": "The requested resource was not found"}


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return {"error": "Internal Server Error", "message": "An internal server error occurred"}


if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=True
    )
