#!/usr/bin/env python3
"""
Simple test script for DrCode API functionality.

This script performs basic tests to verify the API is working correctly.
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    import aiohttp
except ImportError:
    print("❌ aiohttp not installed. Run: pip install aiohttp")
    sys.exit(1)


class APITester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def request(self, method, endpoint, **kwargs):
        """Make HTTP request."""
        url = f"{self.base_url}{endpoint}"
        async with self.session.request(method, url, **kwargs) as response:
            if response.status >= 400:
                error_text = await response.text()
                raise Exception(f"Request failed: {response.status} - {error_text}")
            return await response.json()
    
    async def test_health(self):
        """Test health endpoint."""
        print("🔍 Testing health endpoint...")
        result = await self.request("GET", "/health")
        assert result["status"] == "healthy"
        print("✅ Health check passed")
        return True
    
    async def test_system_info(self):
        """Test system info endpoint."""
        print("🔍 Testing system info...")
        result = await self.request("GET", "/system/info")
        assert "active_tasks_count" in result
        print(f"✅ System info: {result['active_tasks_count']} active tasks")
        return True
    
    async def test_providers(self):
        """Test providers endpoint."""
        print("🔍 Testing providers...")
        result = await self.request("GET", "/providers")
        assert "providers" in result
        assert len(result["providers"]) > 0
        print(f"✅ Found {len(result['providers'])} providers")
        return True
    
    async def test_models(self):
        """Test models endpoint."""
        print("🔍 Testing models...")
        result = await self.request("GET", "/models")
        assert isinstance(result, list)
        assert len(result) > 0
        print(f"✅ Found models for {len(result)} providers")
        return True
    
    async def test_task_creation(self):
        """Test task creation (without actually running)."""
        print("🔍 Testing task creation...")
        
        task_data = {
            "task": "This is a test task - do not execute",
            "agent_settings": {
                "llm_provider": "openai",
                "llm_model_name": "gpt-4.1-mini",
                "max_steps": 1  # Limit to prevent actual execution
            },
            "browser_settings": {
                "headless": True
            }
        }
        
        try:
            result = await self.request("POST", "/tasks", json=task_data)
            assert "task_id" in result
            task_id = result["task_id"]
            print(f"✅ Task created: {task_id}")
            
            # Immediately stop the task
            await self.request("POST", f"/tasks/{task_id}/stop")
            print("✅ Task stopped")
            
            # Clean up
            await self.request("DELETE", f"/tasks/{task_id}")
            print("✅ Task cleaned up")
            
            return True
        except Exception as e:
            print(f"⚠️  Task test failed (expected if no LLM configured): {e}")
            return False
    
    async def test_list_tasks(self):
        """Test list tasks endpoint."""
        print("🔍 Testing list tasks...")
        result = await self.request("GET", "/tasks")
        assert "tasks" in result
        print(f"✅ Tasks endpoint working: {len(result['tasks'])} tasks")
        return True
    
    async def run_all_tests(self):
        """Run all tests."""
        print("🚀 Starting DrCode API Tests")
        print("=" * 40)
        
        tests = [
            ("Health Check", self.test_health),
            ("System Info", self.test_system_info), 
            ("Providers", self.test_providers),
            ("Models", self.test_models),
            ("List Tasks", self.test_list_tasks),
            ("Task Creation", self.test_task_creation),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                success = await test_func()
                if success:
                    passed += 1
            except Exception as e:
                print(f"❌ {test_name} failed: {e}")
            
            print()  # Empty line for readability
        
        print("=" * 40)
        print(f"📊 Test Results: {passed}/{total} passed")
        
        if passed == total:
            print("🎉 All tests passed!")
            return True
        else:
            print("⚠️  Some tests failed - check API server and configuration")
            return False


async def main():
    """Main test function."""
    # Check if API server is reachable
    print("Checking API server availability...")
    
    try:
        async with APITester() as tester:
            success = await tester.run_all_tests()
            
        if success:
            print("\n✅ API is ready for use!")
            print("📚 See API_README.md for detailed usage instructions")
            print("🌐 Interactive docs: http://localhost:8000/docs")
        else:
            print("\n⚠️  API tests completed with some failures")
            
    except Exception as e:
        print(f"❌ Cannot connect to API server: {e}")
        print("💡 Make sure the API server is running:")
        print("   python api_server.py")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
