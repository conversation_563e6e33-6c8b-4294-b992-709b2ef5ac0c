# Scheduled Tasks Feature

This document explains how to use the scheduled tasks feature that allows running tasks at specific times using cron expressions.

## Overview

The scheduled tasks feature allows you to:

- Schedule existing tasks to run automatically at specified intervals
- Use standard cron expressions for flexible scheduling
- Manage scheduled tasks (create, update, delete, list)
- Automatically load and resume schedules when the application restarts
- Store schedule data persistently in Redis

## Architecture

The feature consists of several components:

1. **Models** (`schemas.py`):

   - `ScheduledTaskCreate`: For creating new scheduled tasks
   - `ScheduledTaskUpdate`: For updating existing scheduled tasks
   - `ScheduledTask`: The main scheduled task model
   - Response models for API responses

2. **Repository** (`scheduled_task_repository.py`):

   - Handles Redis storage operations for scheduled tasks
   - Provides CRUD operations

3. **Service** (`scheduled_task_service.py`):

   - Business logic for scheduled task operations
   - Validates cron expressions
   - Manages scheduled task lifecycle

4. **Scheduler** (`task_scheduler_service.py`):

   - Uses APScheduler to manage cron jobs
   - Loads schedules on startup
   - Executes scheduled tasks

5. **Controller** (`task_controller.py`):
   - Provides REST API endpoints for scheduled tasks

## Installation

First, install the required dependencies:

```bash
pip install apscheduler>=3.10.0 croniter>=1.3.0
```

These are already included in the `requirements.txt` file.

## API Endpoints

### Create a Scheduled Task

```http
POST /api/v1/tasks/{task_id}/schedule?cron_expression={cron}&description={desc}
```

**Parameters:**

- `task_id`: ID of the existing task to schedule
- `cron_expression`: Standard cron expression (5 fields: minute hour day month day_of_week)
- `description`: Optional description

**Example:**

```http
POST /api/v1/tasks/123e4567-e89b-12d3-a456-426614174000/schedule?cron_expression=0 9 * * 1-5&description=Run every weekday at 9 AM
```

### List All Scheduled Tasks

```http
GET /api/v1/tasks/schedules?skip=0&limit=100
```

### Get Specific Scheduled Task

```http
GET /api/v1/tasks/schedules/{scheduled_task_id}
```

### Update Scheduled Task

```http
PUT /api/v1/tasks/schedules/{scheduled_task_id}
```

**Body:**

```json
{
  "cron_expression": "0 10 * * 1-5",
  "is_active": true,
  "description": "Updated description"
}
```

### Delete Scheduled Task

```http
DELETE /api/v1/tasks/schedules/{scheduled_task_id}
```

## Cron Expression Examples

| Expression     | Description                       |
| -------------- | --------------------------------- |
| `*/5 * * * *`  | Every 5 minutes                   |
| `0 */2 * * *`  | Every 2 hours                     |
| `0 9 * * 1-5`  | 9 AM Monday to Friday             |
| `0 0 * * 0`    | Every Sunday at midnight          |
| `30 14 1 * *`  | 2:30 PM on the 1st of every month |
| `0 22 * * 1-5` | 10 PM Monday to Friday            |

## Usage Examples

### Using cURL

1. **Create a scheduled task:**

```bash
curl -X POST "http://localhost:8000/api/v1/tasks/your-task-id/schedule?cron_expression=*/5%20*%20*%20*%20*&description=Test%20every%205%20minutes" \
  -H "Content-Type: application/json"
```

2. **List scheduled tasks:**

```bash
curl -X GET "http://localhost:8000/api/v1/tasks/schedules" \
  -H "Content-Type: application/json"
```

3. **Update a scheduled task:**

```bash
curl -X PUT "http://localhost:8000/api/v1/tasks/schedules/your-scheduled-task-id" \
  -H "Content-Type: application/json" \
  -d '{"cron_expression": "0 9 * * 1-5", "description": "Updated to weekdays 9 AM"}'
```

### Using Python

```python
import requests

base_url = "http://localhost:8000/api/v1"

# Create a scheduled task
response = requests.post(
    f"{base_url}/tasks/your-task-id/schedule",
    params={
        "cron_expression": "0 9 * * 1-5",
        "description": "Run every weekday at 9 AM"
    }
)
scheduled_task = response.json()

# List scheduled tasks
response = requests.get(f"{base_url}/tasks/schedules")
schedules = response.json()

# Update scheduled task
response = requests.put(
    f"{base_url}/tasks/schedules/{scheduled_task['data']['id']}",
    json={
        "cron_expression": "0 10 * * 1-5",
        "description": "Updated to 10 AM"
    }
)
```

## How It Works

1. **Task Creation**: When you create a scheduled task, it:

   - Validates the cron expression
   - Stores the schedule in Redis
   - Adds the job to APScheduler
   - Calculates the next run time

2. **Task Execution**: When a scheduled time arrives:

   - The scheduler executes the job
   - Creates a new task instance based on the original task
   - Updates the last run time in Redis
   - The new task is processed normally through the system

3. **Persistence**: All schedules are stored in Redis, so they persist across application restarts.

4. **Startup**: When the application starts:
   - Loads all active scheduled tasks from Redis
   - Adds them back to the scheduler
   - Continues execution from where it left off

## Configuration

The scheduler uses UTC timezone by default. You can modify this in `task_scheduler_service.py`:

```python
self.scheduler = AsyncIOScheduler(
    jobstores=jobstores,
    timezone='UTC'  # Change this to your preferred timezone
)
```

## Monitoring and Debugging

- Check application logs for scheduler events
- Use the GET endpoints to monitor scheduled task status
- The `last_run` and `next_run` fields show execution history and future schedules
- Set `is_active` to `false` to temporarily disable a scheduled task

## Error Handling

- Invalid cron expressions are rejected with detailed error messages
- If the original task is deleted, the scheduled task will log errors but continue to exist
- Failed task executions are logged but don't affect the schedule
- The scheduler automatically handles application restarts

## Testing

Run the test script to verify functionality:

```bash
python test_scheduled_tasks.py
```

This will test all aspects of the scheduled tasks feature including creation, updating, and cleanup.
