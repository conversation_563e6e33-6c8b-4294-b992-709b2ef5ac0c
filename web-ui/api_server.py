#!/usr/bin/env python3
"""
API Server for DrCode UI Testing

This script starts the FastAPI server that provides REST API endpoints
for browser automation tasks, reusing the same components as the WebUI.

Usage:
    python api_server.py [--host HOST] [--port PORT]

Environment Variables:
    OPENAI_API_KEY: Required for QA summary generation
    DRCODE_API_KEY: Optional for saving reports to DrCode backend
    DRCODE_API_URL: DrCode API endpoint (default: https://devapi.drcode.ai/testgpt/api/uiTesting)
    DEFAULT_QA_MODE: Enable QA testing mode (default: true)
"""

import argparse
import os
import sys
from pathlib import Path
from src.api import get_app

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

app = get_app()

# Import after path setup
def main():
    parser = argparse.ArgumentParser(
        description="DrCode UI Testing API Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    parser.add_argument(
        "--host", 
        type=str, 
        default="127.0.0.1", 
        help="Host to bind the API server to (default: 127.0.0.1)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="Port to bind the API server to (default: 8000)"
    )
    parser.add_argument(
        "--reload", 
        action="store_true", 
        help="Enable auto-reload for development"
    )
    parser.add_argument(
        "--log-level", 
        type=str, 
        default="info", 
        choices=["critical", "error", "warning", "info", "debug", "trace"],
        help="Set logging level (default: info)"
    )
    
    args = parser.parse_args()
    
    # Print startup information
    print("=" * 60)
    print("🚀 DrCode UI Testing API Server")
    print("=" * 60)
    print(f"📡 Server: http://{args.host}:{args.port}")
    print(f"📚 API Docs: http://{args.host}:{args.port}/docs")
    print(f"🔧 Interactive API: http://{args.host}:{args.port}/redoc")
    print("=" * 60)
    
    # Check environment
    env_status = []
    if os.getenv("OPENAI_API_KEY"):
        env_status.append("✅ OPENAI_API_KEY: Set")
    else:
        env_status.append("❌ OPENAI_API_KEY: Not set (QA summaries disabled)")
    
    if os.getenv("DRCODE_API_KEY"):
        env_status.append("✅ DRCODE_API_KEY: Set")
    else:
        env_status.append("⚠️  DRCODE_API_KEY: Not set (DrCode integration disabled)")
    
    qa_mode = os.getenv("DEFAULT_QA_MODE", "true").lower() == "true"
    env_status.append(f"🧪 QA Mode: {'Enabled' if qa_mode else 'Disabled'}")
    
    print("Environment Status:")
    for status in env_status:
        print(f"  {status}")
    print("=" * 60)
    
    # Start the server
    try:
        import uvicorn
        uvicorn.run("src.api.routes:app", host=args.host, port=args.port, log_level=args.log_level or "debug", reload=args.reload or True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
