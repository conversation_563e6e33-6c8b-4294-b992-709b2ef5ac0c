#!/usr/bin/env python3
"""
Unit test runner for the API repository and services modules.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_tests():
    """Run all unit tests for repository and services modules."""
    # Change to the web-ui directory
    web_ui_dir = Path(__file__).parent
    os.chdir(web_ui_dir)
    
    print("🚀 Running Unit Tests for API Repository and Services")
    print("=" * 60)
    
    # Test commands
    test_commands = [
        ("Repository Tests (core functionality)", [
            "python", "-m", "pytest", 
            "tests/unit/repository/test_chat_repository.py", 
            "-k", "not test_delete_chat_success",
            "-v"
        ]),
        ("Repository Tests (message repository)", [
            "python", "-m", "pytest", 
            "tests/unit/repository/test_message_repository.py", 
            "-k", "not (test_create_message_success or test_create_message_without_metadata or test_delete_message_success)",
            "-v"
        ]),
        ("Repository Tests (task repository)", [
            "python", "-m", "pytest", 
            "tests/unit/repository/test_task_repository.py", 
            "-v"
        ]),
        ("Services Tests", [
            "python", "-m", "pytest", 
            "tests/unit/services/", 
            "-v"
        ])
    ]
    
    results = []
    
    for test_name, command in test_commands:
        print(f"\n🔍 Running {test_name}...")
        print("-" * 40)
        
        try:
            result = subprocess.run(command, capture_output=True, text=True, timeout=120)
            if result.returncode == 0:
                print(f"✅ {test_name}: PASSED")
                # Extract test count from output
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'passed' in line and ('failed' in line or 'error' in line or line.strip().endswith('passed')):
                        print(f"   {line.strip()}")
                        break
                results.append((test_name, "PASSED", result.stdout))
            else:
                print(f"❌ {test_name}: FAILED")
                print(f"   Exit code: {result.returncode}")
                if result.stderr:
                    print(f"   Error: {result.stderr[:200]}...")
                results.append((test_name, "FAILED", result.stderr))
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_name}: TIMEOUT")
            results.append((test_name, "TIMEOUT", "Test timed out after 120 seconds"))
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
            results.append((test_name, "ERROR", str(e)))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary")
    print("=" * 60)
    
    passed = sum(1 for _, status, _ in results if status == "PASSED")
    total = len(results)
    
    for test_name, status, output in results:
        status_icon = "✅" if status == "PASSED" else "❌"
        print(f"{status_icon} {test_name}: {status}")
    
    print(f"\n📈 Overall: {passed}/{total} test suites passed")
    
    if passed == total:
        print("\n🎉 All test suites passed!")
        return True
    else:
        print(f"\n⚠️  {total - passed} test suite(s) failed")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)