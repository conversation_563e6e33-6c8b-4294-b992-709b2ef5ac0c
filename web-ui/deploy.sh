#!/bin/bash

# Browser-Use Web UI Deployment Script
# This script automates the deployment process

set -e

echo "🚀 Starting Browser-Use Web UI Deployment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from template..."
    
    # Create .env file with default values
    cat > .env << EOF
# LLM API Keys & Endpoints
OPENAI_ENDPOINT=https://api.openai.com/v1
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_ENDPOINT=https://api.anthropic.com
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_API_VERSION=2025-01-01-preview
DEEPSEEK_ENDPOINT=https://api.deepseek.com
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OLLAMA_ENDPOINT=http://localhost:11434
MISTRAL_ENDPOINT=https://api.mistral.ai/v1
MISTRAL_API_KEY=your_mistral_api_key_here
ALIBABA_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode/v1
ALIBABA_API_KEY=your_alibaba_api_key_here
MOONSHOT_ENDPOINT=https://api.moonshot.cn/v1
MOONSHOT_API_KEY=your_moonshot_api_key_here
UNBOUND_ENDPOINT=https://api.getunbound.ai
UNBOUND_API_KEY=your_unbound_api_key_here
SiliconFLOW_ENDPOINT=https://api.siliconflow.cn/v1/
SiliconFLOW_API_KEY=your_siliconflow_api_key_here
IBM_ENDPOINT=https://us-south.ml.cloud.ibm.com
IBM_API_KEY=your_ibm_api_key_here
IBM_PROJECT_ID=your_ibm_project_id_here

# Application Settings
ANONYMIZED_TELEMETRY=false
BROWSER_USE_LOGGING_LEVEL=info
DEFAULT_LLM=google
DEFAULT_LLM_MODEL=gemini-2.0-flash
DEFAULT_LLM_TEMPERATURE=0.6
DEFAULT_USE_VISION=true
DEFAULT_MAX_STEPS=100
DEFAULT_MAX_ACTIONS=10
DEFAULT_MAX_INPUT_TOKENS=128000
DEFAULT_TOOL_CALLING_METHOD=auto

# Browser Settings
BROWSER_PATH=
BROWSER_USER_DATA=
BROWSER_DEBUGGING_PORT=9222
BROWSER_DEBUGGING_HOST=localhost
USE_OWN_BROWSER=false
KEEP_BROWSER_OPEN=true
BROWSER_CDP=

# Display Settings
DISPLAY=:99
PLAYWRIGHT_BROWSERS_PATH=/ms-browsers
RESOLUTION=1920x1080x24
RESOLUTION_WIDTH=1920
RESOLUTION_HEIGHT=1080

# VNC Settings
VNC_PASSWORD=youvncpassword
EOF
    
    echo "✅ .env file created. Please edit it to add your API keys."
    echo "📝 You can edit the .env file now or continue with default settings."
    read -p "Continue with deployment? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Deployment cancelled."
        exit 0
    fi
fi

# Detect system architecture
ARCH=$(uname -m)
if [ "$ARCH" = "arm64" ] || [ "$ARCH" = "aarch64" ]; then
    echo "🖥️  Detected ARM64 architecture. Using ARM64 build..."
    export TARGETPLATFORM=linux/arm64
fi

# Stop existing containers if running
echo "🛑 Stopping existing containers..."
docker compose down 2>/dev/null || true

# Build and start the application
echo "🔨 Building and starting the application..."
docker compose up --build -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
if docker compose ps | grep -q "Up"; then
    echo "✅ Deployment successful!"
    echo ""
    echo "🌐 Access your application:"
    echo "   Web UI: http://localhost:7788"
    echo "   VNC Viewer: http://localhost:6080/vnc.html"
    echo "   VNC Password: youvncpassword (change in .env file)"
    echo ""
    echo "📋 Useful commands:"
    echo "   View logs: docker compose logs -f"
    echo "   Stop: docker compose down"
    echo "   Restart: docker compose restart"
    echo ""
    echo "🔧 To customize:"
    echo "   - Edit .env file for API keys and settings"
    echo "   - Modify docker-compose.yml for custom ports"
    echo "   - Check DEPLOYMENT.md for advanced configuration"
else
    echo "❌ Deployment failed. Check logs with: docker compose logs"
    exit 1
fi 