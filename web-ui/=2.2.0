Requirement already satisfied: pinecone-client in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (6.0.0)
Requirement already satisfied: google-generativeai in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (0.8.5)
Collecting python-docx
  Downloading python_docx-1.2.0-py3-none-any.whl.metadata (2.0 kB)
Collecting sentence-transformers
  Downloading sentence_transformers-5.0.0-py3-none-any.whl.metadata (16 kB)
Requirement already satisfied: certifi>=2019.11.17 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from pinecone-client) (2025.7.14)
Requirement already satisfied: pinecone-plugin-interface<0.0.8,>=0.0.7 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from pinecone-client) (0.0.7)
Requirement already satisfied: python-dateutil>=2.5.3 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from pinecone-client) (2.9.0.post0)
Requirement already satisfied: typing-extensions>=3.7.4 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from pinecone-client) (4.14.1)
Requirement already satisfied: urllib3>=1.26.5 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from pinecone-client) (2.5.0)
Requirement already satisfied: google-ai-generativelanguage==0.6.15 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-generativeai) (0.6.15)
Requirement already satisfied: google-api-core in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-generativeai) (2.25.1)
Requirement already satisfied: google-api-python-client in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-generativeai) (2.176.0)
Requirement already satisfied: google-auth>=2.15.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-generativeai) (2.40.3)
Requirement already satisfied: protobuf in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-generativeai) (5.29.5)
Requirement already satisfied: pydantic in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-generativeai) (2.11.7)
Requirement already satisfied: tqdm in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-generativeai) (4.67.1)
Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-ai-generativelanguage==0.6.15->google-generativeai) (1.26.1)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-api-core->google-generativeai) (1.70.0)
Requirement already satisfied: requests<3.0.0,>=2.18.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-api-core->google-generativeai) (2.32.4)
Requirement already satisfied: grpcio<2.0.0,>=1.33.2 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai) (1.73.1)
Requirement already satisfied: grpcio-status<2.0.0,>=1.33.2 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai) (1.71.2)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-auth>=2.15.0->google-generativeai) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-auth>=2.15.0->google-generativeai) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-auth>=2.15.0->google-generativeai) (4.9.1)
Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (3.10)
Requirement already satisfied: pyasn1>=0.1.3 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from rsa<5,>=3.1.4->google-auth>=2.15.0->google-generativeai) (0.6.1)
Requirement already satisfied: lxml>=3.1.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from python-docx) (5.4.0)
Collecting transformers<5.0.0,>=4.41.0 (from sentence-transformers)
  Downloading transformers-4.53.2-py3-none-any.whl.metadata (40 kB)
Collecting torch>=1.11.0 (from sentence-transformers)
  Downloading torch-2.7.1-cp313-none-macosx_11_0_arm64.whl.metadata (29 kB)
Collecting scikit-learn (from sentence-transformers)
  Downloading scikit_learn-1.7.0-cp313-cp313-macosx_12_0_arm64.whl.metadata (31 kB)
Collecting scipy (from sentence-transformers)
  Downloading scipy-1.16.0-cp313-cp313-macosx_14_0_arm64.whl.metadata (61 kB)
Requirement already satisfied: huggingface-hub>=0.20.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from sentence-transformers) (0.33.4)
Requirement already satisfied: Pillow in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from sentence-transformers) (11.3.0)
Requirement already satisfied: filelock in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (3.18.0)
Requirement already satisfied: numpy>=1.17 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2.3.1)
Requirement already satisfied: packaging>=20.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (24.2)
Requirement already satisfied: pyyaml>=5.1 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (6.0.2)
Requirement already satisfied: regex!=2019.12.17 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2024.11.6)
Requirement already satisfied: tokenizers<0.22,>=0.21 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.21.2)
Collecting safetensors>=0.4.3 (from transformers<5.0.0,>=4.41.0->sentence-transformers)
  Downloading safetensors-0.5.3-cp38-abi3-macosx_11_0_arm64.whl.metadata (3.8 kB)
Requirement already satisfied: fsspec>=2023.5.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2025.7.0)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (1.1.5)
Requirement already satisfied: six>=1.5 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from python-dateutil>=2.5.3->pinecone-client) (1.17.0)
Requirement already satisfied: setuptools in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from torch>=1.11.0->sentence-transformers) (80.9.0)
Collecting sympy>=1.13.3 (from torch>=1.11.0->sentence-transformers)
  Downloading sympy-1.14.0-py3-none-any.whl.metadata (12 kB)
Requirement already satisfied: networkx in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from torch>=1.11.0->sentence-transformers) (3.5)
Requirement already satisfied: jinja2 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from torch>=1.11.0->sentence-transformers) (3.1.6)
Collecting mpmath<1.4,>=1.1.0 (from sympy>=1.13.3->torch>=1.11.0->sentence-transformers)
  Downloading mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)
Requirement already satisfied: httplib2<1.0.0,>=0.19.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-api-python-client->google-generativeai) (0.22.0)
Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-api-python-client->google-generativeai) (0.2.0)
Requirement already satisfied: uritemplate<5,>=3.0.1 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from google-api-python-client->google-generativeai) (4.2.0)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from httplib2<1.0.0,>=0.19.0->google-api-python-client->google-generativeai) (3.2.3)
Requirement already satisfied: MarkupSafe>=2.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from jinja2->torch>=1.11.0->sentence-transformers) (3.0.2)
Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from pydantic->google-generativeai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from pydantic->google-generativeai) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from pydantic->google-generativeai) (0.4.1)
Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/Desktop/Internship_25/ui-testing-backend/.venv/lib/python3.13/site-packages (from scikit-learn->sentence-transformers) (1.5.1)
Collecting threadpoolctl>=3.1.0 (from scikit-learn->sentence-transformers)
  Downloading threadpoolctl-3.6.0-py3-none-any.whl.metadata (13 kB)
Downloading python_docx-1.2.0-py3-none-any.whl (252 kB)
Downloading sentence_transformers-5.0.0-py3-none-any.whl (470 kB)
Downloading transformers-4.53.2-py3-none-any.whl (10.8 MB)
   ━━━━━━━━━━━━━━━━━━ 10.8/10.8 MB 23.3 MB/s eta 0:00:00
Downloading safetensors-0.5.3-cp38-abi3-macosx_11_0_arm64.whl (418 kB)
Downloading torch-2.7.1-cp313-none-macosx_11_0_arm64.whl (68.6 MB)
   ━━━━━━━━━━━━━━━━━━ 68.6/68.6 MB 37.3 MB/s eta 0:00:00
Downloading sympy-1.14.0-py3-none-any.whl (6.3 MB)
   ━━━━━━━━━━━━━━━━━━━━ 6.3/6.3 MB 34.5 MB/s eta 0:00:00
Downloading mpmath-1.3.0-py3-none-any.whl (536 kB)
   ━━━━━━━━━━━━━━━━ 536.2/536.2 kB 17.8 MB/s eta 0:00:00
Downloading scikit_learn-1.7.0-cp313-cp313-macosx_12_0_arm64.whl (10.6 MB)
   ━━━━━━━━━━━━━━━━━━ 10.6/10.6 MB 45.9 MB/s eta 0:00:00
Downloading scipy-1.16.0-cp313-cp313-macosx_14_0_arm64.whl (20.7 MB)
   ━━━━━━━━━━━━━━━━━━ 20.7/20.7 MB 38.1 MB/s eta 0:00:00
Downloading threadpoolctl-3.6.0-py3-none-any.whl (18 kB)
Installing collected packages: mpmath, threadpoolctl, sympy, scipy, safetensors, python-docx, torch, scikit-learn, transformers, sentence-transformers

Successfully installed mpmath-1.3.0 python-docx-1.2.0 safetensors-0.5.3 scikit-learn-1.7.0 scipy-1.16.0 sentence-transformers-5.0.0 sympy-1.14.0 threadpoolctl-3.6.0 torch-2.7.1 transformers-4.53.2
