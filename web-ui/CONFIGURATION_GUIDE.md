# Configuration Guide for Prompt Refinement System

This guide explains how to configure the prompt refinement system for your specific use case and domain.

## Environment Variables

### Required Variables

```bash
# Vector Database
PINECONE_API_KEY=your_pinecone_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
```

### Optional Configuration Variables

```bash
# Pinecone Configuration
PINECONE_INDEX_NAME=ui-testing-knowledge-base  # Default index name
PINECONE_CLOUD=aws                              # Cloud provider (aws/gcp/azure)
PINECONE_REGION=us-east-1                       # Region

# Refinement Thresholds
REFINEMENT_MIN_SPECIFICITY=0.6                  # Minimum specificity score (0.0-1.0)
REFINEMENT_MIN_SIMILARITY=0.6                   # Minimum similarity for context relevance
REFINEMENT_MIN_PROMPT_LENGTH=5                  # Minimum words in prompt

# Session Management
REFINEMENT_MAX_ITERATIONS=3                     # Maximum refinement rounds
REFINEMENT_SESSION_TIMEOUT_HOURS=24             # Session timeout in hours

# Vector Search
REFINEMENT_VECTOR_SEARCH_TOP_K=5                # Number of similar documents to retrieve
REFINEMENT_CONTEXT_CHUNKS=3                     # Context chunks for question generation

# Question Generation
REFINEMENT_MAX_QUESTIONS=3                      # Maximum questions per iteration
REFINEMENT_QUESTION_MODEL=gpt-4o-mini           # Model for generating questions
REFINEMENT_QUESTION_TEMPERATURE=0.7             # Temperature for question generation

# Prompt Refinement
REFINEMENT_PROMPT_MODEL=gpt-4o-mini             # Model for refining prompts
REFINEMENT_PROMPT_TEMPERATURE=0.3               # Temperature for prompt refinement

# Document Processing
DOCUMENT_CHUNK_SIZE=800                         # Size of document chunks
DOCUMENT_CHUNK_OVERLAP=100                      # Overlap between chunks
```

## Customizing for Your Domain

### 1. Update Specific Terms

Edit `src/prompt_refinement/config.py` to customize terms for your domain:

```python
# Add domain-specific terms that indicate detailed prompts
SPECIFIC_TERMS = [
    # Generic UI terms
    "login", "signup", "register", "authenticate", "dashboard",
    "profile", "form", "button", "click", "navigate", "page",
    
    # Add your domain-specific terms
    "inventory", "checkout", "payment", "shipping", "order",
    "customer", "product", "catalog", "search", "filter"
]

# Add domain-specific vague indicators
VAGUE_INDICATORS = [
    "test", "check", "verify", "make sure", "ensure",
    
    # Add your domain-specific vague terms
    "process", "handle", "manage", "deal with"
]
```

### 2. Customize System Prompts

Update the system prompts in `config.py` for your specific use case:

```python
QUESTION_GENERATION_SYSTEM_PROMPT = """You are an AI assistant helping to refine user prompts for [YOUR DOMAIN] testing automation.

Your task is to generate 2-3 specific clarifying questions that will help make a vague prompt more actionable for browser automation.

Focus on:
1. Specific [YOUR DOMAIN] pages or features to interact with
2. Exact actions to perform relevant to [YOUR DOMAIN]
3. Expected outcomes specific to [YOUR DOMAIN] workflows
4. Any domain-specific data or credentials needed

Keep questions concise and actionable for [YOUR DOMAIN] testing."""
```

### 3. Document Setup

Place your documentation in the `vector-db-setup/` directory:

```
vector-db-setup/
├── user-manual.docx
├── api-documentation.pdf
├── feature-specifications.md
└── troubleshooting-guide.txt
```

Supported formats:
- `.docx` - Microsoft Word documents
- `.pdf` - PDF documents (future enhancement)
- `.txt` - Plain text files (future enhancement)
- `.md` - Markdown files (future enhancement)

### 4. Index Configuration

For different environments or domains, you can use different Pinecone indexes:

```bash
# Development
PINECONE_INDEX_NAME=dev-ui-testing-kb

# Staging
PINECONE_INDEX_NAME=staging-ui-testing-kb

# Production
PINECONE_INDEX_NAME=prod-ui-testing-kb
```

## Performance Tuning

### Specificity Scoring

Adjust the specificity threshold based on your domain:

- **Lower threshold (0.4-0.5)**: More prompts will trigger refinement
- **Higher threshold (0.7-0.8)**: Only very vague prompts will trigger refinement

### Vector Search

Tune vector search parameters:

- **Higher TOP_K (7-10)**: More context for question generation, but slower
- **Lower TOP_K (3-5)**: Faster processing, less context

### Question Generation

Adjust question generation:

- **Higher temperature (0.8-1.0)**: More creative/varied questions
- **Lower temperature (0.3-0.5)**: More consistent/focused questions

## Testing Your Configuration

### 1. Test Vector Database Setup

```bash
# Setup with your documents
python -m src.vector_db.setup_vector_db --action setup

# Verify setup
python -m src.vector_db.setup_vector_db --action verify

# Test with domain-specific queries
python -c "
from src.vector_db.pinecone_client import PineconeVectorDBManager
db = PineconeVectorDBManager()
results = db.search('your domain specific query', top_k=3)
print(f'Found {len(results)} results')
for r in results:
    print(f'Score: {r[\"score\"]:.3f} - {r[\"text\"][:100]}...')
"
```

### 2. Test Refinement Logic

```bash
# Run comprehensive tests
python tests/refinement/test_refinement_system.py
```

### 3. Test API Integration

```bash
# Start API server
python api_server.py

# Test with domain-specific prompts
curl -X POST "http://localhost:8001/refinement/start" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "test my domain feature"}'
```

## Monitoring and Debugging

### Enable Debug Logging

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Monitor Refinement Sessions

```bash
# Check active sessions
curl http://localhost:8001/refinement/sessions

# Monitor specific session
curl http://localhost:8001/refinement/{session_id}/status
```

### Performance Metrics

Track these metrics for optimization:

- **Refinement Rate**: Percentage of prompts that need refinement
- **Iteration Count**: Average number of refinement rounds
- **Success Rate**: Percentage of successful refinements
- **Response Time**: Time to generate questions and refine prompts

## Troubleshooting

### Common Issues

1. **High refinement rate**: Lower `REFINEMENT_MIN_SPECIFICITY`
2. **Poor question quality**: Update `SPECIFIC_TERMS` for your domain
3. **Slow performance**: Reduce `REFINEMENT_VECTOR_SEARCH_TOP_K`
4. **Context not relevant**: Add more domain-specific documentation

### Debug Commands

```bash
# Check vector database stats
python -c "
from src.vector_db.pinecone_client import PineconeVectorDBManager
db = PineconeVectorDBManager()
print(db.get_index_stats())
"

# Test prompt analysis
python -c "
from src.prompt_refinement.analyzer import PromptAnalyzer
analyzer = PromptAnalyzer()
result = analyzer.analyze_prompt('your test prompt')
print(result)
"
```

## Best Practices

1. **Documentation Quality**: Ensure your documentation is comprehensive and well-structured
2. **Regular Updates**: Update the vector database when documentation changes
3. **Domain Customization**: Regularly review and update domain-specific terms
4. **Performance Monitoring**: Monitor refinement metrics and adjust thresholds
5. **User Feedback**: Collect feedback on question quality and refinement effectiveness
